from playwright.sync_api import sync_playwright
import time

def debug_msn_page():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        target_url = 'https://www.msn.com/en-in/channel/topic/Top%20stories/tp-Y_0b495ad3-9beb-45f8-9214-c8e95aa2468f?cvid=16f3161c429c45ada7dfe946323bde43&ocid=msedgntp&pc=U531&ei=24'
        page.goto(target_url)
        page.wait_for_load_state('networkidle')
        time.sleep(3)

        print('Page title:', page.title())
        print('Current URL:', page.url)
        
        # Check for various possible selectors
        selectors_to_check = [
            '.news-card',
            'article',
            '[role="article"]',
            '.story-card',
            '.article-card',
            '.cs-card',
            'a[href*="/news/"]',
            'a[href*="/story/"]',
            '.card',
            '.item',
            '.story',
            '.headline',
            'h1',
            'h2',
            'h3'
        ]
        
        for selector in selectors_to_check:
            elements = page.query_selector_all(selector)
            print(f'{selector}: {len(elements)} elements found')
            if elements and len(elements) > 0:
                try:
                    text = elements[0].inner_text()[:100]
                    print(f'  First element text: {text}...')
                except:
                    print('  Could not get text')
        
        # Let's also check the page content structure
        print("\n=== PAGE STRUCTURE ===")
        body = page.query_selector('body')
        if body:
            # Get all direct children of body
            children = body.query_selector_all('> *')
            print(f"Body has {len(children)} direct children")
            for i, child in enumerate(children[:5]):  # First 5 children
                try:
                    tag = child.evaluate('el => el.tagName')
                    classes = child.evaluate('el => el.className')
                    print(f"  Child {i+1}: <{tag}> class='{classes}'")
                except:
                    print(f"  Child {i+1}: Could not get info")
        
        browser.close()

if __name__ == "__main__":
    debug_msn_page()
