"""
Module for validating and automatically correcting generated Playwright scripts
"""

import os
import re
import time
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from loguru import logger
import httpx
from openai import OpenAI, AzureOpenAI


class ScriptValidationResult:
    """Container for script validation results"""
    
    def __init__(self):
        self.success = False
        self.execution_time = 0
        self.stdout = ""
        self.stderr = ""
        self.return_code = 0
        self.error_type = None
        self.error_message = ""
        self.output_analysis = {}
        self.meets_criteria = {}


class ScriptValidator:
    """Automated validation and correction system for Playwright scripts"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.validation_config = config.get('validation', {})
        self.openai_config = config.get('openai', {})
        self.max_retries = self.validation_config.get('max_retries', 3)
        self.timeout = self.validation_config.get('timeout_per_execution', 60)
        self.success_criteria = self.validation_config.get('success_criteria', {})
        self.save_intermediate = self.validation_config.get('save_intermediate_versions', True)
        self.detailed_logging = self.validation_config.get('detailed_logging', True)
        
        # Initialize OpenAI client for corrections
        self.client = self._initialize_openai_client()
        
    def _initialize_openai_client(self):
        """Initialize OpenAI client for script corrections"""
        api_key = self.openai_config.get('api_key')
        use_azure = self.openai_config.get('use_azure', True)
        
        if not api_key:
            api_key = os.environ.get('OPENAI_API_KEY', '')
            if not api_key:
                logger.error("OpenAI API key not provided for script validation")
                return None
        
        if use_azure:
            azure_endpoint = self.openai_config.get('azure_endpoint', '')
            if not azure_endpoint:
                azure_endpoint = os.environ.get('AZURE_OPENAI_ENDPOINT', '')
            
            azure_api_version = self.openai_config.get('azure_api_version', '2023-05-15')
            
            clean_http_client = httpx.Client()
            return AzureOpenAI(
                api_key=api_key,
                api_version=azure_api_version,
                azure_endpoint=azure_endpoint,
                http_client=clean_http_client
            )
        else:
            return OpenAI(api_key=api_key)
    
    def validate_and_correct_script(self, script_content: str, action_text: str, url: str, 
                                  dom_analysis: Dict, vision_analysis: str, 
                                  output_path: str) -> Tuple[bool, str, List[Dict]]:
        """
        Main validation and correction pipeline
        
        Args:
            script_content: Generated Playwright script content
            action_text: Original action description
            url: Target URL
            dom_analysis: DOM analysis results
            vision_analysis: Vision analysis results
            output_path: Path to save the final script
            
        Returns:
            Tuple of (success, final_script_content, validation_history)
        """
        if not self.validation_config.get('enabled', True):
            logger.info("Script validation is disabled, saving script without validation")
            self._save_script(script_content, output_path)
            return True, script_content, []
        
        logger.info("Starting automated script validation and correction pipeline")
        
        validation_history = []
        current_script = script_content
        
        for attempt in range(self.max_retries + 1):
            logger.info(f"Validation attempt {attempt + 1}/{self.max_retries + 1}")
            
            # Save intermediate version if enabled
            if self.save_intermediate and attempt > 0:
                intermediate_path = self._get_intermediate_path(output_path, attempt)
                self._save_script(current_script, intermediate_path)
                logger.debug(f"Saved intermediate script version to: {intermediate_path}")
            
            # Execute and validate the script
            validation_result = self._execute_script(current_script, action_text)
            
            # Analyze the results
            success = self._analyze_validation_result(validation_result, action_text)
            
            # Record this attempt
            attempt_record = {
                'attempt': attempt + 1,
                'script_content': current_script,
                'validation_result': validation_result,
                'success': success,
                'timestamp': time.time()
            }
            validation_history.append(attempt_record)
            
            if success:
                logger.success(f"Script validation successful on attempt {attempt + 1}")
                self._save_script(current_script, output_path)
                return True, current_script, validation_history
            
            # If this was the last attempt, break
            if attempt >= self.max_retries:
                break
            
            # Generate corrected script for next iteration
            logger.info(f"Script validation failed, generating correction for attempt {attempt + 2}")
            corrected_script = self._generate_corrected_script(
                current_script, validation_result, action_text, url, 
                dom_analysis, vision_analysis, validation_history
            )
            
            if corrected_script:
                current_script = corrected_script
                logger.info("Generated corrected script version")
            else:
                logger.error("Failed to generate corrected script, stopping validation")
                break
        
        # All attempts failed
        logger.error(f"Script validation failed after {self.max_retries + 1} attempts")
        self._generate_failure_report(validation_history, output_path, action_text, url)
        
        # Save the last attempted script anyway
        self._save_script(current_script, output_path)
        return False, current_script, validation_history
    
    def _execute_script(self, script_content: str, action_text: str) -> ScriptValidationResult:
        """Execute a Playwright script and capture results"""
        result = ScriptValidationResult()
        
        # Create temporary script file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as temp_file:
            temp_file.write(script_content)
            temp_script_path = temp_file.name
        
        try:
            start_time = time.time()
            
            # Execute the script
            process = subprocess.run(
                ['python', temp_script_path],
                capture_output=True,
                text=True,
                timeout=self.timeout,
                cwd=os.getcwd()
            )
            
            result.execution_time = time.time() - start_time
            result.stdout = process.stdout
            result.stderr = process.stderr
            result.return_code = process.returncode
            
            # Analyze the output for errors
            if process.returncode == 0:
                result.success = True
                logger.debug(f"Script executed successfully in {result.execution_time:.2f}s")
            else:
                result.success = False
                result.error_type, result.error_message = self._parse_error_output(process.stderr)
                logger.debug(f"Script execution failed with return code {process.returncode}")
            
        except subprocess.TimeoutExpired:
            result.success = False
            result.error_type = "TimeoutError"
            result.error_message = f"Script execution timed out after {self.timeout} seconds"
            logger.debug(f"Script execution timed out after {self.timeout}s")
            
        except Exception as e:
            result.success = False
            result.error_type = "ExecutionError"
            result.error_message = str(e)
            logger.debug(f"Script execution failed with exception: {e}")
            
        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_script_path)
            except Exception:
                pass
        
        return result
    
    def _parse_error_output(self, stderr: str) -> Tuple[str, str]:
        """Parse error output to identify error type and message"""
        if not stderr:
            return "UnknownError", "No error message available"
        
        # Common error patterns
        error_patterns = {
            'TimeoutError': r'TimeoutError|Timeout \d+ms exceeded',
            'AssertionError': r'AssertionError',
            'ElementNotFoundError': r'Element.*not found|No such element',
            'SelectorError': r'Invalid selector|Selector.*failed',
            'NavigationError': r'Navigation.*failed|Page.*not loaded',
            'ImportError': r'ImportError|ModuleNotFoundError',
            'SyntaxError': r'SyntaxError|Invalid syntax',
            'AttributeError': r'AttributeError',
            'TypeError': r'TypeError'
        }
        
        for error_type, pattern in error_patterns.items():
            if re.search(pattern, stderr, re.IGNORECASE):
                # Extract the specific error message
                lines = stderr.strip().split('\n')
                error_line = next((line for line in lines if re.search(pattern, line, re.IGNORECASE)), stderr)
                return error_type, error_line.strip()
        
        # Return the last non-empty line as the error message
        lines = [line.strip() for line in stderr.strip().split('\n') if line.strip()]
        return "UnknownError", lines[-1] if lines else stderr
    
    def _analyze_validation_result(self, result: ScriptValidationResult, action_text: str) -> bool:
        """Analyze validation result against success criteria"""
        criteria = self.success_criteria
        meets_all_criteria = True
        
        # Check script execution without errors
        if criteria.get('script_executes_without_errors', True):
            meets_criteria = result.success and result.return_code == 0
            result.meets_criteria['script_executes_without_errors'] = meets_criteria
            if not meets_criteria:
                meets_all_criteria = False
                logger.debug("Failed criteria: script_executes_without_errors")
        
        # Check for timeout errors
        if criteria.get('no_timeout_errors', True):
            meets_criteria = result.error_type != 'TimeoutError'
            result.meets_criteria['no_timeout_errors'] = meets_criteria
            if not meets_criteria:
                meets_all_criteria = False
                logger.debug("Failed criteria: no_timeout_errors")
        
        # Check for assertion errors
        if criteria.get('no_assertion_errors', True):
            meets_criteria = result.error_type != 'AssertionError'
            result.meets_criteria['no_assertion_errors'] = meets_criteria
            if not meets_criteria:
                meets_all_criteria = False
                logger.debug("Failed criteria: no_assertion_errors")
        
        # Check expected output format
        if criteria.get('expected_output_format', True):
            meets_criteria = self._validate_output_format(result.stdout, action_text)
            result.meets_criteria['expected_output_format'] = meets_criteria
            if not meets_criteria:
                meets_all_criteria = False
                logger.debug("Failed criteria: expected_output_format")
        
        # Check minimum results count
        min_results = criteria.get('minimum_results_count', 1)
        if min_results > 0:
            meets_criteria = self._validate_minimum_results(result.stdout, min_results)
            result.meets_criteria['minimum_results_count'] = meets_criteria
            if not meets_criteria:
                meets_all_criteria = False
                logger.debug(f"Failed criteria: minimum_results_count ({min_results})")
        
        return meets_all_criteria
    
    def _validate_output_format(self, stdout: str, action_text: str) -> bool:
        """Validate that the output format matches expectations"""
        if not stdout.strip():
            return False
        
        # Check for common output patterns based on action type
        action_lower = action_text.lower()
        
        if any(keyword in action_lower for keyword in ['get', 'extract', 'fetch', 'collect']):
            # For data extraction actions, expect structured output
            return bool(re.search(r'\d+\.\s+.+|^\s*\w+:', stdout, re.MULTILINE))
        
        if any(keyword in action_lower for keyword in ['search', 'find']):
            # For search actions, expect results
            return 'result' in stdout.lower() or 'found' in stdout.lower()
        
        if any(keyword in action_lower for keyword in ['click', 'submit', 'login']):
            # For interaction actions, expect success confirmation
            return any(word in stdout.lower() for word in ['success', 'complete', 'done', 'clicked'])
        
        # Default: any non-empty output is considered valid
        return True
    
    def _validate_minimum_results(self, stdout: str, min_count: int) -> bool:
        """Validate that the output contains at least the minimum number of results"""
        if not stdout.strip():
            return False
        
        # Count numbered items (1. 2. 3. etc.)
        numbered_items = len(re.findall(r'^\s*\d+\.\s+', stdout, re.MULTILINE))
        if numbered_items >= min_count:
            return True
        
        # Count lines with content
        content_lines = [line for line in stdout.split('\n') if line.strip() and not line.strip().startswith('#')]
        return len(content_lines) >= min_count

    def _generate_corrected_script(self, current_script: str, validation_result: ScriptValidationResult,
                                 action_text: str, url: str, dom_analysis: Dict,
                                 vision_analysis: str, validation_history: List[Dict]) -> Optional[str]:
        """Generate a corrected version of the script using LLM"""
        if not self.client:
            logger.error("OpenAI client not available for script correction")
            return None

        try:
            # Prepare context for the LLM
            correction_prompt = self._build_correction_prompt(
                current_script, validation_result, action_text, url,
                dom_analysis, vision_analysis, validation_history
            )

            # Get model configuration
            model = self.openai_config.get('azure_deployment_name_completion', 'gpt-4o') if self.openai_config.get('use_azure') else self.openai_config.get('model', 'gpt-4o')
            max_tokens = self.openai_config.get('max_tokens', 6000)
            temperature = 0.1  # Low temperature for consistent corrections

            # Generate corrected script
            response = self.client.chat.completions.create(
                model=model,
                messages=correction_prompt,
                max_tokens=max_tokens,
                temperature=temperature
            )

            corrected_script = response.choices[0].message.content.strip()

            # Clean up the response (remove markdown if present)
            corrected_script = self._clean_script_response(corrected_script)

            logger.debug("Successfully generated corrected script")
            return corrected_script

        except Exception as e:
            logger.error(f"Error generating corrected script: {str(e)}")
            return None

    def _build_correction_prompt(self, current_script: str, validation_result: ScriptValidationResult,
                               action_text: str, url: str, dom_analysis: Dict,
                               vision_analysis: str, validation_history: List[Dict]) -> List[Dict]:
        """Build the prompt for script correction"""

        # Analyze previous attempts to avoid repeating mistakes
        previous_errors = []
        for attempt in validation_history:
            if not attempt['success']:
                error_info = {
                    'attempt': attempt['attempt'],
                    'error_type': attempt['validation_result'].error_type,
                    'error_message': attempt['validation_result'].error_message,
                    'failed_criteria': [k for k, v in attempt['validation_result'].meets_criteria.items() if not v]
                }
                previous_errors.append(error_info)

        system_prompt = """You are an expert Playwright automation engineer specializing in debugging and fixing automation scripts. Your task is to analyze failed Playwright scripts and generate corrected versions that will execute successfully.

## CORE PRINCIPLES FOR SCRIPT CORRECTION:
1. **Error Analysis First**: Carefully analyze the specific error messages and failure points
2. **Robust Selector Strategy**: Use multiple selector approaches with proper fallbacks
3. **Proper Wait Conditions**: Ensure adequate waiting for dynamic content
4. **Error Handling**: Add comprehensive try-catch blocks and graceful error handling
5. **Realistic Expectations**: Adjust assertions and expectations based on actual page behavior
6. **Debugging Information**: Include helpful logging and debugging output

## CORRECTION METHODOLOGY:
1. Identify the root cause of the failure from error messages
2. Analyze what selectors or approaches are not working
3. Implement alternative strategies based on DOM analysis
4. Add proper wait conditions and error handling
5. Ensure the script produces the expected output format
6. Test multiple fallback approaches for critical elements

## SCRIPT REQUIREMENTS:
- Must execute without errors
- Must handle timeouts and missing elements gracefully
- Must produce meaningful output in the expected format
- Must include debugging information to help identify issues
- Must use validated selectors when available
- Must implement multiple selector strategies with fallbacks

Your response must be ONLY valid, executable Python code with NO markdown formatting."""

        # Build detailed context about the failure
        failure_analysis = f"""
## CURRENT FAILURE ANALYSIS

**Original Action**: {action_text}
**Target URL**: {url}

**Current Error Details**:
- Error Type: {validation_result.error_type}
- Error Message: {validation_result.error_message}
- Return Code: {validation_result.return_code}
- Execution Time: {validation_result.execution_time:.2f}s

**Script Output**:
STDOUT: {validation_result.stdout}
STDERR: {validation_result.stderr}

**Failed Success Criteria**:
{self._format_failed_criteria(validation_result.meets_criteria)}

**Previous Attempts and Errors**:
{self._format_previous_errors(previous_errors)}
"""

        # Include DOM analysis for better selector choices
        dom_context = f"""
## DOM ANALYSIS FOR CORRECTION

{self._format_dom_analysis_for_correction(dom_analysis)}
"""

        # Include vision analysis
        vision_context = f"""
## VISUAL ANALYSIS FOR REFERENCE

{vision_analysis}
"""

        user_prompt = f"""Analyze the failed Playwright script and generate a corrected version that will execute successfully.

{failure_analysis}

{dom_context}

{vision_context}

## CURRENT FAILING SCRIPT:
```python
{current_script}
```

## CORRECTION REQUIREMENTS:

1. **Fix the Specific Error**: Address the exact error type and message identified above
2. **Implement Robust Selectors**: Use the validated selectors from DOM analysis if available
3. **Add Proper Wait Conditions**: Ensure the script waits for dynamic content to load
4. **Handle Edge Cases**: Add error handling for common issues (timeouts, missing elements)
5. **Improve Output Format**: Ensure the script produces output that meets the success criteria
6. **Add Debugging**: Include helpful print statements to track execution progress

## SPECIFIC FIXES NEEDED:
- Address the {validation_result.error_type} error
- Fix any selector issues identified in the error message
- Ensure proper wait conditions for dynamic content
- Add fallback strategies for element selection
- Improve error handling and user feedback

Generate a complete, corrected Playwright script that addresses all identified issues and will execute successfully."""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

    def _format_failed_criteria(self, meets_criteria: Dict) -> str:
        """Format failed criteria for the prompt"""
        failed = [criterion for criterion, passed in meets_criteria.items() if not passed]
        if not failed:
            return "None (all criteria passed)"
        return "\n".join([f"- {criterion}" for criterion in failed])

    def _format_previous_errors(self, previous_errors: List[Dict]) -> str:
        """Format previous errors for the prompt"""
        if not previous_errors:
            return "This is the first attempt."

        formatted = []
        for error in previous_errors:
            formatted.append(f"Attempt {error['attempt']}: {error['error_type']} - {error['error_message']}")
            if error['failed_criteria']:
                formatted.append(f"  Failed criteria: {', '.join(error['failed_criteria'])}")

        return "\n".join(formatted)

    def _format_dom_analysis_for_correction(self, dom_analysis: Dict) -> str:
        """Format DOM analysis specifically for script correction"""
        if not dom_analysis:
            return "No DOM analysis available."

        formatted = f"""
**Validated Selectors (USE THESE FIRST)**:
"""
        if dom_analysis.get('validated_selectors'):
            for selector in dom_analysis['validated_selectors'][:5]:
                formatted += f"✅ {selector['selector']} - {selector['element_count']} elements found\n"
        else:
            formatted += "No validated selectors available.\n"

        formatted += f"""
**Interactive Elements Found**: {len(dom_analysis.get('interactive_elements', []))}
**Content Elements Found**: {len(dom_analysis.get('content_elements', []))}

**Recommended Selectors**:
"""
        for suggestion in dom_analysis.get('selector_suggestions', [])[:5]:
            formatted += f"- {suggestion['selector']} ({suggestion['element_count']} elements)\n"

        return formatted

    def _clean_script_response(self, script_content: str) -> str:
        """Clean the LLM response to extract just the Python code"""
        # Remove markdown code blocks
        script_content = script_content.strip()

        if script_content.startswith('```python'):
            script_content = script_content[9:]
        elif script_content.startswith('```'):
            script_content = script_content[3:]

        if script_content.endswith('```'):
            script_content = script_content[:-3]

        return script_content.strip()

    def _save_script(self, script_content: str, output_path: str):
        """Save script to file"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            logger.debug(f"Script saved to: {output_path}")
        except Exception as e:
            logger.error(f"Error saving script to {output_path}: {str(e)}")

    def _get_intermediate_path(self, output_path: str, attempt: int) -> str:
        """Generate path for intermediate script versions"""
        path = Path(output_path)
        stem = path.stem
        suffix = path.suffix
        parent = path.parent

        return str(parent / f"{stem}_attempt_{attempt}{suffix}")

    def _generate_failure_report(self, validation_history: List[Dict], output_path: str,
                               action_text: str, url: str):
        """Generate a detailed failure report"""
        report_path = output_path.replace('.py', '_validation_report.txt')

        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("PLAYWRIGHT SCRIPT VALIDATION FAILURE REPORT\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"Action: {action_text}\n")
                f.write(f"URL: {url}\n")
                f.write(f"Total Attempts: {len(validation_history)}\n")
                f.write(f"Max Retries: {self.max_retries}\n\n")

                for i, attempt in enumerate(validation_history):
                    f.write(f"ATTEMPT {attempt['attempt']}\n")
                    f.write("-" * 20 + "\n")
                    f.write(f"Success: {attempt['success']}\n")

                    result = attempt['validation_result']
                    f.write(f"Error Type: {result.error_type}\n")
                    f.write(f"Error Message: {result.error_message}\n")
                    f.write(f"Return Code: {result.return_code}\n")
                    f.write(f"Execution Time: {result.execution_time:.2f}s\n")

                    f.write("\nFailed Criteria:\n")
                    for criterion, passed in result.meets_criteria.items():
                        if not passed:
                            f.write(f"  - {criterion}\n")

                    f.write(f"\nSTDOUT:\n{result.stdout}\n")
                    f.write(f"\nSTDERR:\n{result.stderr}\n")
                    f.write("\n" + "=" * 30 + "\n\n")

                f.write("RECOMMENDATIONS FOR MANUAL FIXES:\n")
                f.write("-" * 35 + "\n")
                f.write("1. Check if the target website structure has changed\n")
                f.write("2. Verify that the selectors are still valid\n")
                f.write("3. Consider adding longer wait times for dynamic content\n")
                f.write("4. Test the script manually with debugging enabled\n")
                f.write("5. Update the DOM analysis if the page structure changed\n")

            logger.info(f"Validation failure report saved to: {report_path}")

        except Exception as e:
            logger.error(f"Error generating failure report: {str(e)}")
