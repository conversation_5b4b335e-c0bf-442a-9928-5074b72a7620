# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from typing import Union
from typing_extensions import Annotated

from ..._utils import PropertyInfo
from .function_tool import FunctionTool
from .retrieval_tool import RetrievalTool
from .code_interpreter_tool import CodeInterpreterTool

__all__ = ["AssistantTool"]

AssistantTool = Annotated[Union[CodeInterpreterTool, RetrievalTool, FunctionTool], PropertyInfo(discriminator="type")]
