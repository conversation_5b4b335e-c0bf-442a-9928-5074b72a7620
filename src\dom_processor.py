"""
Module for processing and analyzing DOM content for Playwright script generation
"""

import re
from bs4 import BeautifulSoup, Comment
from loguru import logger


def clean_and_analyze_dom(dom_content, action_text):
    """
    Clean and analyze DOM content to extract relevant elements for automation
    
    Args:
        dom_content (str): Raw HTML DOM content
        action_text (str): Description of the action to automate
        
    Returns:
        dict: Processed DOM analysis with relevant elements
    """
    try:
        soup = BeautifulSoup(dom_content, 'html.parser')
        
        # Remove unnecessary elements
        for element in soup(['script', 'style', 'meta', 'link']):
            element.decompose()
        
        # Remove comments
        for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
            comment.extract()
        
        # Extract relevant elements based on action keywords
        action_keywords = extract_action_keywords(action_text)
        
        analysis = {
            'page_title': soup.title.string if soup.title else '',
            'interactive_elements': find_interactive_elements(soup),
            'text_elements': find_text_elements(soup, action_keywords),
            'form_elements': find_form_elements(soup),
            'navigation_elements': find_navigation_elements(soup),
            'content_elements': find_content_elements(soup, action_keywords),
            'cleaned_html': get_cleaned_html_snippet(soup),
            'element_hierarchy': build_element_hierarchy(soup),
            'selector_suggestions': generate_selector_suggestions(soup, action_keywords)
        }
        
        logger.debug(f"DOM analysis completed. Found {len(analysis['interactive_elements'])} interactive elements")
        return analysis
        
    except Exception as e:
        logger.error(f"Error processing DOM: {str(e)}")
        return {
            'page_title': '',
            'interactive_elements': [],
            'text_elements': [],
            'form_elements': [],
            'navigation_elements': [],
            'content_elements': [],
            'cleaned_html': dom_content[:5000],  # Fallback to raw content
            'element_hierarchy': {},
            'selector_suggestions': []
        }


def extract_action_keywords(action_text):
    """Extract relevant keywords from action description"""
    # Common action keywords and their variations
    action_mappings = {
        'click': ['click', 'press', 'tap', 'select'],
        'type': ['type', 'enter', 'input', 'fill', 'write'],
        'search': ['search', 'find', 'look', 'query'],
        'login': ['login', 'sign in', 'authenticate'],
        'submit': ['submit', 'send', 'post'],
        'navigate': ['go to', 'navigate', 'visit', 'open'],
        'scroll': ['scroll', 'swipe'],
        'wait': ['wait', 'pause', 'delay'],
        'extract': ['get', 'extract', 'scrape', 'collect', 'fetch'],
        'verify': ['check', 'verify', 'validate', 'confirm']
    }
    
    keywords = []
    action_lower = action_text.lower()
    
    for category, variations in action_mappings.items():
        for variation in variations:
            if variation in action_lower:
                keywords.append(category)
                break
    
    # Extract specific words that might be element identifiers
    words = re.findall(r'\b\w+\b', action_text.lower())
    keywords.extend([word for word in words if len(word) > 2])
    
    return list(set(keywords))


def find_interactive_elements(soup):
    """Find all interactive elements in the DOM"""
    interactive_tags = ['button', 'a', 'input', 'select', 'textarea', 'form']
    interactive_elements = []
    
    for tag in interactive_tags:
        elements = soup.find_all(tag)
        for element in elements:
            element_info = {
                'tag': tag,
                'text': element.get_text(strip=True)[:100],
                'attributes': dict(element.attrs),
                'selector_options': generate_element_selectors(element),
                'position_info': get_element_position_info(element)
            }
            interactive_elements.append(element_info)
    
    # Also find elements with click handlers or role attributes
    clickable_elements = soup.find_all(attrs={'onclick': True}) + \
                        soup.find_all(attrs={'role': re.compile(r'button|link|tab|menuitem')})
    
    for element in clickable_elements:
        if element.name not in interactive_tags:
            element_info = {
                'tag': element.name,
                'text': element.get_text(strip=True)[:100],
                'attributes': dict(element.attrs),
                'selector_options': generate_element_selectors(element),
                'position_info': get_element_position_info(element)
            }
            interactive_elements.append(element_info)
    
    return interactive_elements


def find_text_elements(soup, keywords):
    """Find text elements that might be relevant to the action"""
    text_elements = []
    
    # Find elements containing action keywords
    for keyword in keywords:
        elements = soup.find_all(string=re.compile(keyword, re.IGNORECASE))
        for element in elements:
            parent = element.parent
            if parent and parent.name not in ['script', 'style']:
                element_info = {
                    'tag': parent.name,
                    'text': parent.get_text(strip=True)[:200],
                    'keyword_matched': keyword,
                    'attributes': dict(parent.attrs),
                    'selector_options': generate_element_selectors(parent)
                }
                text_elements.append(element_info)
    
    return text_elements


def find_form_elements(soup):
    """Find all form-related elements"""
    form_elements = []
    
    forms = soup.find_all('form')
    for form in forms:
        form_info = {
            'tag': 'form',
            'action': form.get('action', ''),
            'method': form.get('method', 'GET'),
            'inputs': [],
            'selector_options': generate_element_selectors(form)
        }
        
        # Find all inputs within this form
        inputs = form.find_all(['input', 'select', 'textarea'])
        for input_elem in inputs:
            input_info = {
                'tag': input_elem.name,
                'type': input_elem.get('type', 'text'),
                'name': input_elem.get('name', ''),
                'placeholder': input_elem.get('placeholder', ''),
                'selector_options': generate_element_selectors(input_elem)
            }
            form_info['inputs'].append(input_info)
        
        form_elements.append(form_info)
    
    return form_elements


def find_navigation_elements(soup):
    """Find navigation-related elements"""
    nav_elements = []
    
    # Find nav tags and elements with navigation roles
    nav_tags = soup.find_all(['nav', 'header', 'footer']) + \
               soup.find_all(attrs={'role': re.compile(r'navigation|menubar|menu')})
    
    for nav in nav_tags:
        nav_info = {
            'tag': nav.name,
            'text': nav.get_text(strip=True)[:200],
            'links': [],
            'selector_options': generate_element_selectors(nav)
        }
        
        # Find links within navigation
        links = nav.find_all('a')
        for link in links:
            link_info = {
                'text': link.get_text(strip=True),
                'href': link.get('href', ''),
                'selector_options': generate_element_selectors(link)
            }
            nav_info['links'].append(link_info)
        
        nav_elements.append(nav_info)
    
    return nav_elements


def find_content_elements(soup, keywords):
    """Find content elements that might contain target information"""
    content_elements = []
    
    # Find articles, sections, and divs with relevant content
    content_tags = soup.find_all(['article', 'section', 'main', 'div'])
    
    for element in content_tags:
        text_content = element.get_text(strip=True)
        
        # Check if element contains relevant keywords
        relevance_score = 0
        for keyword in keywords:
            if keyword.lower() in text_content.lower():
                relevance_score += 1
        
        if relevance_score > 0 or len(text_content) > 100:
            element_info = {
                'tag': element.name,
                'text_preview': text_content[:300],
                'relevance_score': relevance_score,
                'child_count': len(element.find_all()),
                'selector_options': generate_element_selectors(element),
                'contains_links': len(element.find_all('a')),
                'contains_images': len(element.find_all('img'))
            }
            content_elements.append(element_info)
    
    # Sort by relevance score
    content_elements.sort(key=lambda x: x['relevance_score'], reverse=True)
    return content_elements[:20]  # Limit to top 20 most relevant


def generate_element_selectors(element):
    """Generate multiple selector options for an element"""
    selectors = []
    
    # ID selector (most reliable)
    if element.get('id'):
        selectors.append(f"#{element['id']}")
    
    # Data attribute selectors
    for attr, value in element.attrs.items():
        if attr.startswith('data-'):
            selectors.append(f"[{attr}='{value}']")
    
    # Class selectors
    if element.get('class'):
        classes = ' '.join(element['class'])
        selectors.append(f".{'.'.join(element['class'])}")
    
    # Attribute selectors
    for attr in ['name', 'type', 'role', 'aria-label']:
        if element.get(attr):
            selectors.append(f"[{attr}='{element[attr]}']")
    
    # Tag selector with attributes
    tag_selector = element.name
    if element.get('class'):
        tag_selector += f".{element['class'][0]}"
    selectors.append(tag_selector)
    
    # Text-based selector (for elements with unique text)
    text = element.get_text(strip=True)
    if text and len(text) < 50:
        selectors.append(f"text='{text}'")
    
    return selectors[:5]  # Limit to top 5 selectors


def get_element_position_info(element):
    """Get position information about an element"""
    position_info = {
        'has_siblings': len(element.find_next_siblings()) > 0,
        'parent_tag': element.parent.name if element.parent else None,
        'depth': len(list(element.parents)),
        'index_in_parent': len(element.find_previous_siblings())
    }
    return position_info


def get_cleaned_html_snippet(soup, max_length=8000):
    """Get a cleaned, relevant snippet of HTML"""
    # Remove empty elements and whitespace
    for element in soup.find_all():
        if not element.get_text(strip=True) and not element.find_all(['img', 'input', 'button']):
            element.decompose()
    
    html_str = str(soup)
    
    # If still too long, focus on body content
    if len(html_str) > max_length:
        body = soup.find('body')
        if body:
            html_str = str(body)
    
    # Final truncation if needed
    if len(html_str) > max_length:
        html_str = html_str[:max_length] + "\n<!-- HTML truncated for brevity -->"
    
    return html_str


def build_element_hierarchy(soup):
    """Build a simplified element hierarchy"""
    hierarchy = {}
    
    # Focus on main structural elements
    main_elements = soup.find_all(['header', 'nav', 'main', 'section', 'article', 'aside', 'footer'])
    
    for element in main_elements:
        element_id = element.get('id', f"{element.name}_{len(hierarchy)}")
        hierarchy[element_id] = {
            'tag': element.name,
            'classes': element.get('class', []),
            'children_count': len(element.find_all()),
            'text_length': len(element.get_text(strip=True)),
            'interactive_children': len(element.find_all(['button', 'a', 'input', 'select']))
        }
    
    return hierarchy


def generate_selector_suggestions(soup, keywords):
    """Generate smart selector suggestions based on action keywords"""
    suggestions = []
    
    # Map keywords to likely selector patterns
    keyword_patterns = {
        'login': ['[type="email"]', '[type="password"]', 'button[type="submit"]', '.login', '#login'],
        'search': ['[type="search"]', '.search', '#search', '[placeholder*="search"]'],
        'click': ['button', 'a', '[role="button"]', '.btn', '.button'],
        'submit': ['[type="submit"]', 'button[type="submit"]', '.submit'],
        'menu': ['nav', '.menu', '.navigation', '[role="menu"]'],
        'close': ['.close', '.modal-close', '[aria-label*="close"]'],
        'next': ['.next', '.continue', '[aria-label*="next"]'],
        'previous': ['.prev', '.back', '[aria-label*="previous"]']
    }
    
    for keyword in keywords:
        if keyword in keyword_patterns:
            for pattern in keyword_patterns[keyword]:
                elements = soup.select(pattern)
                if elements:
                    suggestions.append({
                        'keyword': keyword,
                        'selector': pattern,
                        'element_count': len(elements),
                        'confidence': 'high' if len(elements) == 1 else 'medium'
                    })
    
    return suggestions
