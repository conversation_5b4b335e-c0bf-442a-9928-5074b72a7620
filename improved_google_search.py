import sys
import time

try:
    from playwright.sync_api import sync_playwright
except ImportError:
    print("Error: Playwright module not found. Please install it using 'pip install playwright'.")
    sys.exit(1)

def navigate_to_google(page):
    try:
        print("Step 1: Navigating to Google...")
        
        # Use a more realistic user agent and viewport
        page.set_viewport_size({"width": 1920, "height": 1080})
        
        # Navigate with longer timeout and wait for network idle
        page.goto("https://www.google.com", timeout=60000, wait_until="networkidle")
        
        # Add a small delay to appear more human-like
        time.sleep(2)
        
        # Try multiple selectors for the search input (Google changes these frequently)
        search_selectors = [
            'input[name="q"]',
            'input[type="search"]',
            'textarea[name="q"]',
            '#APjFqb',  # Current Google search input ID
            '[role="combobox"]',
            'input[aria-label*="Search"]',
            'input[title*="Search"]'
        ]
        
        search_found = False
        for selector in search_selectors:
            try:
                page.wait_for_selector(selector, timeout=3000)
                print(f"Step 1: Found search input with selector: {selector}")
                search_found = True
                break
            except:
                continue
        
        if not search_found:
            print("Step 1: Search input not immediately found, checking page content...")
            # Check if we're on a CAPTCHA or blocked page
            page_content = page.content()
            if "captcha" in page_content.lower() or "unusual traffic" in page_content.lower():
                print("Step 1: CAPTCHA or bot detection page detected")
                print("Please solve the CAPTCHA manually or try again later")
                return False
            else:
                print("Step 1: Page loaded but search input not found with known selectors")
                print("This might be due to Google's updated interface")
        
        print("Step 1: Navigation completed.")
        return True
        
    except Exception as e:
        print(f"Step 1: Navigation failed. Error: {e}")
        print("This is likely due to Google's anti-bot protection.")
        print("Try using a different search engine or running with headless=False")
        return False

def perform_search(page, query):
    try:
        print(f"Step 2: Searching for '{query}'...")
        
        # Try multiple search input selectors
        search_selectors = [
            'input[name="q"]',
            'textarea[name="q"]',
            '#APjFqb',
            '[role="combobox"]',
            'input[aria-label*="Search"]'
        ]
        
        search_input = None
        for selector in search_selectors:
            try:
                search_input = page.locator(selector)
                if search_input.count() > 0:
                    print(f"Step 2: Using search input selector: {selector}")
                    break
            except:
                continue
        
        if not search_input or search_input.count() == 0:
            raise Exception("Search input not found")
        
        # Clear any existing text and type the query
        search_input.clear()
        search_input.fill(query)
        
        # Add human-like delay
        time.sleep(1)
        
        # Try to submit the search (multiple methods)
        try:
            # Method 1: Press Enter
            search_input.press("Enter")
        except:
            try:
                # Method 2: Click search button
                search_button_selectors = [
                    'input[value*="Google Search"]',
                    'input[value*="Search"]',
                    'button[type="submit"]',
                    '[role="button"][aria-label*="Search"]'
                ]
                
                for btn_selector in search_button_selectors:
                    try:
                        page.click(btn_selector)
                        break
                    except:
                        continue
            except:
                raise Exception("Could not submit search")
        
        # Wait for search results with multiple possible selectors
        result_selectors = [
            '.tF2Cxc',  # Standard result container
            '.g',       # Alternative result container
            '#search',  # Search results container
            '[data-ved]' # Elements with data-ved attribute (Google results)
        ]
        
        results_found = False
        for selector in result_selectors:
            try:
                page.wait_for_selector(selector, timeout=10000)
                print(f"Step 2: Search results loaded (found with selector: {selector})")
                results_found = True
                break
            except:
                continue
        
        if not results_found:
            print("Step 2: Search submitted but results format may have changed")
            print("Continuing anyway...")
        
        print("Step 2: Search executed successfully.")
        return True
        
    except Exception as e:
        print(f"Step 2: Search failed. Error: {e}")
        return False

def extract_top_results(page, max_results=10):
    try:
        print(f"Step 3: Extracting top {max_results} search results...")
        
        # Multiple strategies for finding results
        result_strategies = [
            {
                'container': '.tF2Cxc',
                'title': 'h3',
                'link': 'a'
            },
            {
                'container': '.g',
                'title': 'h3',
                'link': 'a'
            },
            {
                'container': '[data-ved]',
                'title': 'h3',
                'link': 'a'
            }
        ]
        
        results = []
        
        for strategy in result_strategies:
            try:
                containers = page.locator(strategy['container'])
                count = containers.count()
                
                if count > 0:
                    print(f"Step 3: Found {count} result containers using strategy: {strategy['container']}")
                    
                    for i in range(min(count, max_results)):
                        try:
                            container = containers.nth(i)
                            
                            # Extract title
                            title_element = container.locator(strategy['title']).first
                            title = title_element.inner_text() if title_element.count() > 0 else "No title"
                            
                            # Extract URL
                            link_element = container.locator(strategy['link']).first
                            url = link_element.get_attribute('href') if link_element.count() > 0 else "No URL"
                            
                            if title != "No title" and url != "No URL":
                                results.append({"title": title, "url": url})
                        
                        except Exception as e:
                            print(f"Step 3: Error extracting result {i+1}: {e}")
                            continue
                    
                    if results:
                        break  # Success with this strategy
                        
            except Exception as e:
                print(f"Step 3: Strategy {strategy['container']} failed: {e}")
                continue
        
        if not results:
            print("Step 3: No results extracted. The page structure may have changed.")
            print("Attempting to extract any visible links...")
            
            # Fallback: extract any links that look like search results
            try:
                all_links = page.locator('a[href*="/url?"]')  # Google result links
                count = all_links.count()
                
                for i in range(min(count, max_results)):
                    try:
                        link = all_links.nth(i)
                        title = link.inner_text() or f"Result {i+1}"
                        url = link.get_attribute('href') or "No URL"
                        results.append({"title": title, "url": url})
                    except:
                        continue
                        
            except Exception as e:
                print(f"Step 3: Fallback extraction failed: {e}")
        
        print(f"Step 3: Successfully extracted {len(results)} results.")
        return results
        
    except Exception as e:
        print(f"Step 3: Failed to extract search results. Error: {e}")
        return []

def print_results(results):
    print("Step 4: Printing results...")
    if not results:
        print("No results found to display.")
        print("This might be due to:")
        print("- Google's anti-bot protection")
        print("- Changed page structure")
        print("- Network issues")
        return
    
    print(f"\nFound {len(results)} search results:")
    print("-" * 50)
    
    for idx, result in enumerate(results, start=1):
        print(f"{idx}. {result['title']}")
        print(f"   URL: {result['url']}")
        print()
    
    print("Step 4: Results printed successfully.")

def execute_multi_step_action():
    query = "ai news"
    
    try:
        with sync_playwright() as playwright:
            # Launch browser with more realistic settings
            browser = playwright.chromium.launch(
                headless=False,  # Set to False to see what's happening
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            # Create context with realistic user agent
            context = browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                viewport={'width': 1920, 'height': 1080}
            )
            
            page = context.new_page()
            
            # Step 1: Navigate to Google
            if not navigate_to_google(page):
                print("Navigation failed. Exiting...")
                browser.close()
                return
            
            # Step 2: Perform search
            if not perform_search(page, query):
                print("Search failed. Exiting...")
                browser.close()
                return
            
            # Step 3: Extract top 10 results
            results = extract_top_results(page, max_results=10)
            
            # Step 4: Print results
            print_results(results)
            
            print("\nScript completed. Browser will close in 5 seconds...")
            time.sleep(5)
            browser.close()
            
    except Exception as e:
        print(f"Error in multi-step execution: {e}")
        print("\nTroubleshooting tips:")
        print("1. Make sure Playwright browsers are installed: playwright install")
        print("2. Try running with headless=False to see what's happening")
        print("3. Google may be blocking automated requests - try a different search engine")
        print("4. Check your internet connection")

if __name__ == "__main__":
    print("🤖 Enhanced Google Search Automation")
    print("=" * 40)
    print("This script will:")
    print("1. Navigate to Google")
    print("2. Search for 'ai news'")
    print("3. Extract top 10 results")
    print("4. Display the results")
    print("=" * 40)
    print()
    
    execute_multi_step_action()
