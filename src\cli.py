"""
CLI processing module for the Playwright Script Generator
"""

from rich.console import Console
from rich.panel import Panel
from rich.markdown import Markdown
from loguru import logger

from .web_capture import capture_website_sync
from .vision import analyze_screenshot
from .script_gen import generate_script, save_script
from .utils import validate_url


console = Console()


def process_url(url, action, browser_type, output_path, config):
    """
    Process a URL and generate a Playwright script
    
    Args:
        url (str): URL to process
        action (str): Action description
        browser_type (str): Browser type to use
        output_path (str): Path to save the generated script
        config (dict): Configuration dictionary
        
    Returns:
        bool: True if successful, False otherwise
    """
    # Validate URL
    if not validate_url(url):
        console.print(Panel.fit("[bold red]Invalid URL provided. Please include http:// or https://", title="Error"))
        return False
    
    # Step 1: Display a nice intro
    console.print(Panel.fit(
        f"[bold cyan]Analyzing[/bold cyan] [bold white]{url}[/bold white]\n"
        f"[bold cyan]Action:[/bold cyan] [bold white]{action}[/bold white]\n"
        f"[bold cyan]Browser:[/bold cyan] [bold white]{browser_type}[/bold white]",
        title="Playwright Script Generator"
    ))
    
    # Step 2: Capture website screenshot and DOM
    with console.status("[bold green]Capturing website...") as status:
        screenshot_base64, dom_content, page_title = capture_website_sync(url, browser_type, config)
        
        if not screenshot_base64 or not dom_content:
            console.print(Panel.fit("[bold red]Failed to capture website", title="Error"))
            return False
        
        status.update("[bold green]Website captured successfully")
    
    # Step 3: Analyze screenshot with Vision API
    with console.status("[bold green]Analyzing screenshot with AI...") as status:
        vision_analysis = analyze_screenshot(screenshot_base64, action, config)
        
        if not vision_analysis:
            console.print(Panel.fit("[bold red]Failed to analyze screenshot", title="Error"))
            return False
        
        status.update("[bold green]Screenshot analysis complete")
    
    # Step 4: Generate script using both DOM and vision analysis
    with console.status("[bold green]Generating Playwright script...") as status:
        script = generate_script(dom_content, vision_analysis, action, url, page_title, config)
        
        if not script:
            console.print(Panel.fit("[bold red]Failed to generate script", title="Error"))
            return False
        
        status.update("[bold green]Script generation complete")
    
    # Step 5: Save script to file
    with console.status("[bold green]Saving script...") as status:
        success = save_script(script, output_path, config)
        
        if not success:
            console.print(Panel.fit("[bold red]Failed to save script", title="Error"))
            return False
    
    # Step 6: Display success message with script preview
    console.print(Panel.fit(
        f"[bold green]✓[/bold green] Script generated successfully!\n"
        f"[bold cyan]Saved to:[/bold cyan] [bold white]{output_path}[/bold white]",
        title="Success"
    ))
    
    # Show preview of the script
    preview_lines = script.split('\n')[:15]
    preview = '\n'.join(preview_lines)
    if len(script.split('\n')) > 15:
        preview += "\n..."
    
    console.print(Panel.fit(
        preview,
        title="Script Preview"
    ))
    
    # Display usage instructions
    console.print(Markdown("""
    ### Next Steps
    
    1. Install the script dependencies:
       ```
       pip install playwright
       playwright install
       ```
    
    2. Run the script:
       ```
       python output_script.py
       ```
    """))
    
    return True
