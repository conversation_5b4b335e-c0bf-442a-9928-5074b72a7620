"""
Utility functions for the Playwright Script Generator
"""

import sys
import os
from pathlib import Path
from dotenv import load_dotenv
from loguru import logger


def setup_logging(verbose=False):
    """
    Configure logging settings
    
    Args:
        verbose (bool): Enable verbose logging if True
    """
    # Remove default logger
    logger.remove()
    
    # Set log level based on verbose flag
    log_level = "DEBUG" if verbose else "INFO"
    
    # Add console logger with custom format
    logger.add(
        sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
        level=log_level,
        colorize=True
    )
    
    # Add file logger
    os.makedirs("logs", exist_ok=True)
    logger.add(
        "logs/generator.log",
        rotation="5 MB",
        retention="1 week",
        level="DEBUG",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {message}",
    )


def validate_url(url):
    """
    Basic URL validation
    
    Args:
        url (str): URL to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    if not url.startswith(('http://', 'https://')):
        logger.warning(f"URL '{url}' does not start with http:// or https://")
        return False
    return True


def sanitize_filename(filename):
    """
    Sanitize a filename to remove invalid characters
    
    Args:
        filename (str): Filename to sanitize
        
    Returns:
        str: Sanitized filename
    """
    # Replace invalid filename characters
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    return filename


def load_environment_variables():
    """
    Load environment variables from .env file if it exists
    
    Looks for .env in the current directory and parent directories
    """
    # Try to find .env file in the current directory or parent directories
    env_path = Path(".env")
    if env_path.exists():
        logger.info(f"Loading environment variables from {env_path.absolute()}")
        load_dotenv(env_path)
    else:
        # Try project root (up to 3 levels up)
        for i in range(1, 4):
            parent_env = Path(".") / ".." * i / ".env"
            if parent_env.exists():
                logger.info(f"Loading environment variables from {parent_env.absolute()}")
                load_dotenv(parent_env)
                break
        else:
            logger.debug("No .env file found for environment variables")
    
    # Check for required environment variables
    if not os.environ.get('OPENAI_API_KEY'):
        logger.warning("OPENAI_API_KEY environment variable not set")
    
    if not os.environ.get('AZURE_OPENAI_ENDPOINT'):
        logger.warning("AZURE_OPENAI_ENDPOINT environment variable not set")
