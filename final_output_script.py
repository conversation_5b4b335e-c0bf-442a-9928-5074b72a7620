from playwright.sync_api import sync_playwright, TimeoutError

def get_top_stories():
    url = "https://www.msn.com/en-in/channel/topic/Top%20stories/tp-Y_0b495ad3-9beb-45f8-9214-c8e95aa2468f?cvid=16f3161c429c45ada7dfe946323bde43&ocid=msedgntp&pc=U531&ei=24"

    try:
        with sync_playwright() as playwright:
            # Launch browser
            browser = playwright.chromium.launch(headless=False)  # Set to True for production
            context = browser.new_context()
            page = context.new_page()

            # Navigate to the target URL
            page.goto(url, timeout=60000)
            page.wait_for_load_state("networkidle", timeout=30000)

            # Verify page title to ensure correct navigation
            assert "Top stories - MSN" in page.title(), "Page title does not match expected value."

            # Wait for the "Top Stories" section to load
            try:
                page.wait_for_selector('.top-stories', timeout=15000)  # Primary selector
            except TimeoutError:
                print("Top Stories section did not load within the expected time.")
                browser.close()
                return

            # Extract top 10 story titles using multiple selector strategies
            try:
                # Primary selector strategy
                article_cards = page.locator('.top-stories .article-card h2.title')
                if article_cards.count() == 0:
                    # Fallback selector strategy
                    article_cards = page.locator('//div[contains(@class, "top-stories")]//h2[contains(@class, "title")]')
                if article_cards.count() == 0:
                    # Final fallback strategy (text-based)
                    article_cards = page.locator('text="Top Stories" >> .. >> h2')

                # Extract and print top 10 story titles
                titles = article_cards.all_text_contents()
                if len(titles) == 0:
                    raise Exception("No article titles found.")
                print("Top 10 Stories:")
                for i, title in enumerate(titles[:10], start=1):
                    print(f"{i}. {title}")

            except Exception as e:
                print(f"Error extracting article titles: {e}")

            # Close the browser
            browser.close()

    except TimeoutError as e:
        print(f"TimeoutError: {e}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    get_top_stories()