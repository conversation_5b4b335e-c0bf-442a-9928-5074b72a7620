"""
Module for decomposing complex multi-step actions into executable steps
"""

import re
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from loguru import logger


@dataclass
class ActionStep:
    """Represents a single step in a multi-step action"""
    step_id: str
    step_type: str  # 'navigate', 'search', 'click', 'extract', 'wait', 'verify'
    description: str
    target_element: Optional[str] = None
    input_data: Optional[str] = None
    expected_output: Optional[str] = None
    depends_on: List[str] = None
    selectors: List[str] = None
    validation_criteria: Dict = None
    
    def __post_init__(self):
        if self.depends_on is None:
            self.depends_on = []
        if self.selectors is None:
            self.selectors = []
        if self.validation_criteria is None:
            self.validation_criteria = {}


class ActionDecomposer:
    """Decomposes complex actions into sequential steps"""
    
    def __init__(self):
        # Action patterns and their corresponding step types
        self.action_patterns = {
            'search': {
                'patterns': [
                    r'search\s+for\s+[\'"]([^\'\"]+)[\'"]',
                    r'search\s+[\'"]([^\'\"]+)[\'"]',
                    r'find\s+[\'"]([^\'\"]+)[\'"]',
                    r'look\s+for\s+[\'"]([^\'\"]+)[\'"]'
                ],
                'step_type': 'search',
                'requires_input': True
            },
            'navigate': {
                'patterns': [
                    r'go\s+to\s+(.+)',
                    r'navigate\s+to\s+(.+)',
                    r'visit\s+(.+)',
                    r'open\s+(.+)'
                ],
                'step_type': 'navigate',
                'requires_input': True
            },
            'click': {
                'patterns': [
                    r'click\s+(?:on\s+)?(.+)',
                    r'press\s+(.+)',
                    r'tap\s+(.+)',
                    r'select\s+(.+)'
                ],
                'step_type': 'click',
                'requires_target': True
            },
            'extract': {
                'patterns': [
                    r'get\s+(?:the\s+)?(.+)',
                    r'extract\s+(?:the\s+)?(.+)',
                    r'collect\s+(?:the\s+)?(.+)',
                    r'fetch\s+(?:the\s+)?(.+)',
                    r'scrape\s+(?:the\s+)?(.+)'
                ],
                'step_type': 'extract',
                'requires_target': True
            },
            'print': {
                'patterns': [
                    r'print\s+(?:the\s+)?(.+)',
                    r'display\s+(?:the\s+)?(.+)',
                    r'show\s+(?:the\s+)?(.+)',
                    r'output\s+(?:the\s+)?(.+)'
                ],
                'step_type': 'output',
                'requires_data': True
            },
            'wait': {
                'patterns': [
                    r'wait\s+for\s+(.+)',
                    r'pause\s+(?:for\s+)?(.+)',
                    r'delay\s+(.+)'
                ],
                'step_type': 'wait',
                'requires_condition': True
            }
        }
        
        # Common conjunctions that separate steps
        self.step_separators = [
            'and', 'then', 'after that', 'next', 'followed by', 
            'subsequently', 'afterwards', 'finally'
        ]
    
    def decompose_action(self, action_text: str, url: str) -> List[ActionStep]:
        """
        Decompose a complex action into individual steps
        
        Args:
            action_text: The complex action description
            url: Target URL for context
            
        Returns:
            List of ActionStep objects
        """
        logger.info(f"Decomposing action: {action_text}")
        
        # Split the action into potential steps
        raw_steps = self._split_into_raw_steps(action_text)
        logger.debug(f"Raw steps identified: {raw_steps}")
        
        # Parse each raw step into ActionStep objects
        action_steps = []
        for i, raw_step in enumerate(raw_steps):
            step = self._parse_raw_step(raw_step, i, url)
            if step:
                action_steps.append(step)
        
        # Establish dependencies between steps
        action_steps = self._establish_dependencies(action_steps)
        
        # Add implicit navigation step if needed
        if action_steps and not any(step.step_type == 'navigate' for step in action_steps):
            nav_step = ActionStep(
                step_id="step_0_navigate",
                step_type="navigate",
                description=f"Navigate to {url}",
                target_element=url,
                validation_criteria={'page_loaded': True}
            )
            action_steps.insert(0, nav_step)
            # Update dependencies
            for step in action_steps[1:]:
                if not step.depends_on:
                    step.depends_on = ["step_0_navigate"]
        
        logger.info(f"Decomposed into {len(action_steps)} steps")
        return action_steps
    
    def _split_into_raw_steps(self, action_text: str) -> List[str]:
        """Split action text into raw step descriptions"""
        # Create a pattern that matches step separators
        separator_pattern = r'\b(?:' + '|'.join(self.step_separators) + r')\b'
        
        # Split by separators but keep the text
        parts = re.split(separator_pattern, action_text, flags=re.IGNORECASE)
        
        # Clean up and filter empty parts
        raw_steps = []
        for part in parts:
            cleaned = part.strip().strip(',').strip()
            if cleaned:
                raw_steps.append(cleaned)
        
        return raw_steps
    
    def _parse_raw_step(self, raw_step: str, step_index: int, url: str) -> Optional[ActionStep]:
        """Parse a raw step description into an ActionStep object"""
        raw_step = raw_step.strip().lower()
        
        for action_type, config in self.action_patterns.items():
            for pattern in config['patterns']:
                match = re.search(pattern, raw_step, re.IGNORECASE)
                if match:
                    return self._create_action_step(
                        action_type, config, match, step_index, raw_step, url
                    )
        
        # If no pattern matches, create a generic step
        logger.warning(f"Could not parse step: {raw_step}")
        return ActionStep(
            step_id=f"step_{step_index + 1}_generic",
            step_type="generic",
            description=raw_step,
            validation_criteria={'manual_verification': True}
        )
    
    def _create_action_step(self, action_type: str, config: Dict, match: re.Match, 
                          step_index: int, raw_step: str, url: str) -> ActionStep:
        """Create an ActionStep from parsed information"""
        step_id = f"step_{step_index + 1}_{action_type}"
        step_type = config['step_type']
        
        # Extract the matched content
        matched_content = match.group(1) if match.groups() else ""
        
        # Create step based on type
        if step_type == 'search':
            return ActionStep(
                step_id=step_id,
                step_type=step_type,
                description=f"Search for '{matched_content}'",
                input_data=matched_content,
                target_element="search_input",
                selectors=['input[type="search"]', 'input[name="q"]', '.search-input', '#search'],
                validation_criteria={'search_executed': True, 'results_loaded': True}
            )
        
        elif step_type == 'extract':
            # Determine what to extract based on content
            extract_target = self._determine_extract_target(matched_content)
            return ActionStep(
                step_id=step_id,
                step_type=step_type,
                description=f"Extract {matched_content}",
                target_element=extract_target['element'],
                expected_output=extract_target['output_type'],
                selectors=extract_target['selectors'],
                validation_criteria={'data_extracted': True, 'minimum_items': extract_target.get('min_items', 1)}
            )
        
        elif step_type == 'output':
            return ActionStep(
                step_id=step_id,
                step_type=step_type,
                description=f"Print {matched_content}",
                expected_output="formatted_list",
                validation_criteria={'output_generated': True}
            )
        
        elif step_type == 'click':
            return ActionStep(
                step_id=step_id,
                step_type=step_type,
                description=f"Click {matched_content}",
                target_element=matched_content,
                selectors=self._generate_click_selectors(matched_content),
                validation_criteria={'element_clicked': True}
            )
        
        elif step_type == 'navigate':
            return ActionStep(
                step_id=step_id,
                step_type=step_type,
                description=f"Navigate to {matched_content}",
                target_element=matched_content,
                validation_criteria={'page_loaded': True}
            )
        
        elif step_type == 'wait':
            return ActionStep(
                step_id=step_id,
                step_type=step_type,
                description=f"Wait for {matched_content}",
                target_element=matched_content,
                validation_criteria={'condition_met': True}
            )
        
        else:
            return ActionStep(
                step_id=step_id,
                step_type=step_type,
                description=raw_step,
                validation_criteria={'step_completed': True}
            )
    
    def _determine_extract_target(self, content: str) -> Dict:
        """Determine what elements to target for extraction based on content description"""
        content_lower = content.lower()
        
        # Common extraction patterns
        if any(word in content_lower for word in ['link', 'url', 'href']):
            return {
                'element': 'links',
                'output_type': 'link_list',
                'selectors': ['a[href]', 'a', 'link'],
                'min_items': 5
            }
        
        elif any(word in content_lower for word in ['title', 'headline', 'heading']):
            return {
                'element': 'titles',
                'output_type': 'title_list',
                'selectors': ['h1', 'h2', 'h3', '.title', '.headline', '[role="heading"]'],
                'min_items': 3
            }
        
        elif any(word in content_lower for word in ['article', 'story', 'news', 'post']):
            return {
                'element': 'articles',
                'output_type': 'article_list',
                'selectors': ['article', '[role="article"]', '.article', '.story', '.news-item'],
                'min_items': 5
            }
        
        elif any(word in content_lower for word in ['result', 'item', 'entry']):
            return {
                'element': 'results',
                'output_type': 'result_list',
                'selectors': ['.result', '.item', '.entry', '[role="listitem"]'],
                'min_items': 5
            }
        
        elif 'description' in content_lower:
            return {
                'element': 'descriptions',
                'output_type': 'description_list',
                'selectors': ['.description', '.summary', '.excerpt', 'p'],
                'min_items': 3
            }
        
        else:
            # Generic content extraction
            return {
                'element': 'content',
                'output_type': 'text_list',
                'selectors': ['div', 'span', 'p', 'article', '[role="article"]'],
                'min_items': 1
            }
    
    def _generate_click_selectors(self, element_description: str) -> List[str]:
        """Generate potential selectors for clickable elements"""
        desc_lower = element_description.lower()
        selectors = []
        
        # Button-like elements
        if any(word in desc_lower for word in ['button', 'btn', 'submit']):
            selectors.extend(['button', '.btn', '.button', '[role="button"]', 'input[type="submit"]'])
        
        # Link-like elements
        if any(word in desc_lower for word in ['link', 'url']):
            selectors.extend(['a', 'a[href]', '.link'])
        
        # Search-related elements
        if 'search' in desc_lower:
            selectors.extend(['.search-btn', '#search-btn', 'button[type="submit"]', '.search-button'])
        
        # Generic clickable elements
        selectors.extend(['[onclick]', '[role="button"]', '[tabindex]'])
        
        return selectors
    
    def _establish_dependencies(self, steps: List[ActionStep]) -> List[ActionStep]:
        """Establish dependencies between steps based on their types and content"""
        for i, step in enumerate(steps):
            if i == 0:
                continue  # First step has no dependencies
            
            # Default: depend on the previous step
            if not step.depends_on:
                step.depends_on = [steps[i-1].step_id]
            
            # Special dependency rules
            if step.step_type == 'output':
                # Output steps depend on the last extract step
                extract_steps = [s for s in steps[:i] if s.step_type == 'extract']
                if extract_steps:
                    step.depends_on = [extract_steps[-1].step_id]
            
            elif step.step_type == 'extract':
                # Extract steps might depend on search or navigation
                search_steps = [s for s in steps[:i] if s.step_type == 'search']
                if search_steps:
                    step.depends_on = [search_steps[-1].step_id]
        
        return steps
    
    def get_execution_order(self, steps: List[ActionStep]) -> List[ActionStep]:
        """Get steps in proper execution order based on dependencies"""
        executed = set()
        ordered_steps = []
        
        while len(ordered_steps) < len(steps):
            for step in steps:
                if step.step_id in executed:
                    continue
                
                # Check if all dependencies are satisfied
                if all(dep in executed for dep in step.depends_on):
                    ordered_steps.append(step)
                    executed.add(step.step_id)
                    break
            else:
                # If we can't find a step to execute, there might be circular dependencies
                remaining = [s for s in steps if s.step_id not in executed]
                logger.warning(f"Possible circular dependency detected. Remaining steps: {[s.step_id for s in remaining]}")
                # Add remaining steps in order
                ordered_steps.extend(remaining)
                break
        
        return ordered_steps
