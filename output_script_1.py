from playwright.sync_api import sync_playwright, TimeoutError

def search_google(query):
    url = "https://www.google.com"
    try:
        with sync_playwright() as playwright:
            # Launch browser
            browser = playwright.chromium.launch(headless=False)  # Set to True for production
            context = browser.new_context()
            page = context.new_page()

            # Navigate to Google homepage
            page.goto(url, timeout=60000)
            page.wait_for_load_state("networkidle", timeout=30000)

            # Ensure the page title is correct
            assert "Google" in page.title(), "Page title does not match 'Google'."

            # Wait for the search input field to be visible
            try:
                page.wait_for_selector('input[name="q"]', timeout=10000)
            except TimeoutError:
                raise Exception("Search input field not found within timeout.")

            # Multiple selector strategies for the search input field
            search_input_selectors = [
                'input[name="q"]',  # Most reliable selector
                '.gLFyf',           # CSS class-based selector
                '//input[@name="q"]'  # XPath fallback
            ]

            # Locate the search input field using multiple strategies
            search_input = None
            for selector in search_input_selectors:
                try:
                    search_input = page.locator(selector)
                    if search_input.is_visible():
                        break
                except Exception:
                    continue

            if not search_input or not search_input.is_visible():
                raise Exception("Search input field could not be located using any selector strategy.")

            # Type the query into the search input field
            search_input.fill(query)

            # Wait for the "Google Search" button to be visible
            try:
                page.wait_for_selector('input[value="Google Search"]', timeout=10000)
            except TimeoutError:
                raise Exception("'Google Search' button not found within timeout.")

            # Multiple selector strategies for the "Google Search" button
            search_button_selectors = [
                'input[value="Google Search"]',  # Most reliable selector
                '.gNO89b',                       # CSS class-based selector
                '//input[@value="Google Search"]'  # XPath fallback
            ]

            # Locate the "Google Search" button using multiple strategies
            search_button = None
            for selector in search_button_selectors:
                try:
                    search_button = page.locator(selector)
                    if search_button.is_visible():
                        break
                except Exception:
                    continue

            if not search_button or not search_button.is_visible():
                raise Exception("'Google Search' button could not be located using any selector strategy.")

            # Click the "Google Search" button
            search_button.click()

            # Wait for the search results page to load
            page.wait_for_load_state("networkidle", timeout=30000)

            # Verify the URL contains the search query
            assert f"q={query.replace(' ', '+')}" in page.url(), f"Search query '{query}' not found in URL."

            # Verify search results are displayed
            try:
                page.wait_for_selector('div#search', timeout=10000)  # Example selector for search results container
            except TimeoutError:
                raise Exception("Search results not found within timeout.")

            print("Search completed successfully. Results are displayed.")

            # Close the browser
            browser.close()

    except TimeoutError as e:
        print(f"TimeoutError: {e}")
    except AssertionError as e:
        print(f"AssertionError: {e}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    search_google("playwright automation")