"""
Demonstration of the Multi-Step Action System
This script shows how the system decomposes and executes complex actions
"""

from src.action_decomposer import ActionDecomposer
from src.multi_step_generator import MultiStepScriptGenerator
from src.config import load_config

def demonstrate_action_decomposition():
    """Demonstrate how complex actions are decomposed into steps"""
    print("🔧 MULTI-STEP ACTION DECOMPOSITION DEMONSTRATION")
    print("=" * 60)
    
    decomposer = ActionDecomposer()
    
    # Complex action examples
    complex_actions = [
        {
            "action": "search for 'ai news' and print the top 10 links with description",
            "url": "https://www.google.com"
        },
        {
            "action": "search for laptops and print the top 10 name and price of the laptops", 
            "url": "https://www.amazon.com"
        },
        {
            "action": "navigate to login page and enter credentials then submit and go to dashboard",
            "url": "https://example.com"
        },
        {
            "action": "click on menu and select settings then change theme to dark mode",
            "url": "https://app.example.com"
        }
    ]
    
    for i, example in enumerate(complex_actions, 1):
        print(f"\n🎯 EXAMPLE {i}: {example['action']}")
        print(f"🌐 URL: {example['url']}")
        print("-" * 50)
        
        # Decompose the action
        steps = decomposer.decompose_action(example['action'], example['url'])
        
        print(f"✅ Decomposed into {len(steps)} sequential steps:")
        
        for j, step in enumerate(steps, 1):
            print(f"\n  📋 Step {j}: {step.description}")
            print(f"     Type: {step.step_type}")
            print(f"     Target: {step.target_element or 'N/A'}")
            print(f"     Input: {step.input_data or 'N/A'}")
            print(f"     Dependencies: {', '.join(step.depends_on) if step.depends_on else 'None'}")
            
            if step.selectors:
                print(f"     Selectors: {', '.join(step.selectors[:3])}{'...' if len(step.selectors) > 3 else ''}")
            
            if step.validation_criteria:
                print(f"     Success Criteria: {', '.join(step.validation_criteria.keys())}")
        
        print("\n" + "=" * 60)

def demonstrate_script_generation():
    """Demonstrate script generation for multi-step actions"""
    print("\n🚀 MULTI-STEP SCRIPT GENERATION DEMONSTRATION")
    print("=" * 60)
    
    # Load configuration
    try:
        config = load_config()
        print("✅ Configuration loaded successfully")
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        return
    
    # Example action
    action = "search for 'python tutorial' and print the top 5 results"
    url = "https://www.google.com"
    
    print(f"\n🎯 ACTION: {action}")
    print(f"🌐 URL: {url}")
    print("-" * 50)
    
    # Initialize generator
    generator = MultiStepScriptGenerator(config)
    
    # Mock DOM and vision analysis for demonstration
    mock_dom = """
    <html>
        <body>
            <input name="q" type="search" placeholder="Search">
            <button type="submit">Google Search</button>
            <div class="g">
                <h3><a href="#">Result 1</a></h3>
                <span>Description 1</span>
            </div>
        </body>
    </html>
    """
    
    mock_vision = "The page shows Google's search interface with a search box and search button."
    
    print("📝 Generating multi-step script...")
    
    try:
        script_content, metadata = generator.generate_multi_step_script(
            action, url, mock_dom, mock_vision, "Google"
        )
        
        if script_content:
            print("✅ Script generated successfully!")
            print(f"📊 Steps: {metadata['step_count']}")
            print(f"🔄 Multi-step: {metadata['is_multi_step']}")
            
            # Show script preview
            lines = script_content.split('\n')
            preview_lines = lines[:20]
            
            print("\n📋 SCRIPT PREVIEW:")
            print("-" * 30)
            for line in preview_lines:
                print(line)
            if len(lines) > 20:
                print("...")
                print(f"[{len(lines) - 20} more lines]")
        else:
            print("❌ Script generation failed")
            
    except Exception as e:
        print(f"❌ Error generating script: {e}")

def demonstrate_validation_system():
    """Demonstrate the validation system capabilities"""
    print("\n🔍 VALIDATION SYSTEM DEMONSTRATION")
    print("=" * 60)
    
    print("🎯 The validation system provides:")
    print("  ✅ Automated script execution testing")
    print("  🔄 Iterative correction using LLM feedback")
    print("  📊 Detailed failure analysis and reporting")
    print("  🛠️ Multiple retry attempts with improvements")
    print("  📝 Comprehensive validation reports")
    
    print("\n📋 Validation Process:")
    print("  1. Execute generated script in controlled environment")
    print("  2. Analyze output, errors, and execution results")
    print("  3. Check against success criteria")
    print("  4. If failed, generate corrected version using LLM")
    print("  5. Repeat until success or max retries reached")
    print("  6. Generate detailed report with recommendations")
    
    print("\n🎯 Success Criteria Examples:")
    print("  • Script executes without errors")
    print("  • Expected output format is produced")
    print("  • Minimum number of results extracted")
    print("  • No timeout or assertion errors")
    print("  • Proper data validation")

def main():
    """Main demonstration function"""
    print("🎉 ENHANCED PLAYWRIGHT SCRIPT GENERATOR")
    print("Multi-Step Action System Demonstration")
    print("=" * 60)
    
    # Run demonstrations
    demonstrate_action_decomposition()
    demonstrate_script_generation()
    demonstrate_validation_system()
    
    print("\n🎯 SYSTEM CAPABILITIES SUMMARY:")
    print("=" * 60)
    print("✅ Intelligent action decomposition")
    print("✅ Multi-step script orchestration")
    print("✅ Automated validation and correction")
    print("✅ Robust error handling and fallbacks")
    print("✅ Production-ready code generation")
    print("✅ Comprehensive progress tracking")
    print("✅ Detailed failure analysis")
    
    print("\n🚀 USAGE EXAMPLES:")
    print("-" * 30)
    print("python main.py --url 'https://google.com' --action 'search for ai news and print top 10 results'")
    print("python main.py --url 'https://amazon.com' --action 'search for laptops and extract names and prices'")
    print("python main.py --url 'https://example.com' --action 'login and navigate to dashboard then extract user data'")
    
    print("\n✨ The system transforms natural language into production-ready automation!")

if __name__ == "__main__":
    main()
