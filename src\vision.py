"""
Module for integrating with OpenAI's Vision API
"""

import os
import httpx
from openai import OpenAI, AzureOpenAI
from loguru import logger


def analyze_screenshot(screenshot_base64, action_text, config):
    """
    Analyze a screenshot using OpenAI's Vision model
    
    Args:
        screenshot_base64 (str): Base64-encoded screenshot
        action_text (str): Description of the action to automate
        config (dict): Configuration dictionary
    
    Returns:
        str: Analysis result from the vision model
    """
    openai_config = config.get('openai', {})
    api_key = openai_config.get('api_key')
    model = openai_config.get('model', 'gpt-4o')
    max_tokens = openai_config.get('max_tokens', 4000)
    temperature = openai_config.get('temperature', 0.2)
    use_azure = openai_config.get('use_azure', True)
    
    if not api_key:
        api_key = os.environ.get('OPENAI_API_KEY', '')
        if not api_key:
            logger.error("OpenAI API key not provided in config or environment")
            return None
    
    # Initialize the appropriate client based on configuration
    client = None
    if use_azure:
        azure_endpoint = openai_config.get('azure_endpoint', '')
        if not azure_endpoint:
            azure_endpoint = os.environ.get('AZURE_OPENAI_ENDPOINT', '')
            if not azure_endpoint:
                logger.error("Azure OpenAI endpoint not provided in config or environment")
                return None
                
        azure_api_version = openai_config.get('azure_api_version', '2023-05-15')
        azure_deployment = openai_config.get('azure_deployment_name_vision', '')
        
        if not azure_deployment:
            logger.error("Azure OpenAI deployment name for vision model not provided")
            return None
            
        logger.info(f"Using Azure OpenAI with endpoint: {azure_endpoint} and deployment: {azure_deployment}")
        # Create a clean http client to avoid inheriting any global proxy settings
        clean_http_client = httpx.Client()
        client = AzureOpenAI(
            api_key=api_key,
            api_version=azure_api_version,
            azure_endpoint=azure_endpoint,
            http_client=clean_http_client
        )
        # For Azure, we use the deployment name instead of the model name
        model = azure_deployment
    else:
        logger.info(f"Using OpenAI with model: {model}")
        client = OpenAI(api_key=api_key)
    
    try:
        logger.info("Sending screenshot to Vision API for analysis")
        
        # Construct the vision API message
        messages = [
            {
                "role": "system",
                "content": "You are a visual web page analyzer that identifies UI elements and their relationships. "
                           "Your task is to describe the page structure, identify key interactive elements, "
                           "and suggest the most reliable CSS selectors or XPath expressions for automation."
            },
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": f"I need to automate this action: '{action_text}'. "
                                f"Analyze this screenshot and help me understand the page structure. "
                                f"Identify the key elements I would need to interact with for this action, "
                                f"their visual characteristics, and suggested selectors."
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{screenshot_base64}"
                        }
                    }
                ]
            }
        ]
        
        # Call the API using the appropriate client
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            max_tokens=max_tokens,
            temperature=temperature
        )
        
        analysis = response.choices[0].message.content
        logger.debug("Successfully received vision analysis")
        
        return analysis
        
    except Exception as e:
        logger.error(f"Error analyzing screenshot with OpenAI Vision API: {str(e)}")
        return None
