from src.dom_processor import clean_and_analyze_dom, validate_selectors_with_playwright
from src.web_capture import capture_website_sync
import json

def test_dom_analysis():
    url = "https://www.msn.com/en-in/channel/topic/Top%20stories/tp-Y_0b495ad3-9beb-45f8-9214-c8e95aa2468f?cvid=16f3161c429c45ada7dfe946323bde43&ocid=msedgntp&pc=U531&ei=24"
    action = "get top 10 stories and print it"

    print("Capturing website...")
    config = {}  # Empty config for testing
    screenshot_base64, dom_content, page_title = capture_website_sync(url, "chromium", config)

    if dom_content:
        print(f"DOM content length: {len(dom_content)}")
        print(f"DOM preview: {dom_content[:500]}...")

        print("Analyzing DOM...")
        dom_analysis = clean_and_analyze_dom(dom_content, action)

        print(f"Interactive elements found: {len(dom_analysis['interactive_elements'])}")
        print(f"Content elements found: {len(dom_analysis['content_elements'])}")
        print(f"Selector suggestions: {len(dom_analysis['selector_suggestions'])}")

        # Let's also manually check for articles
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(dom_content, 'html.parser')
        articles = soup.find_all('article')
        role_articles = soup.find_all(attrs={'role': 'article'})
        print(f"Manual check - articles: {len(articles)}, role=article: {len(role_articles)}")

        print("\nSelector suggestions:")
        for suggestion in dom_analysis['selector_suggestions']:
            print(f"  - {suggestion['keyword']}: {suggestion['selector']} ({suggestion['element_count']} elements)")

        if dom_analysis['selector_suggestions']:
            print("\nValidating selectors...")
            validated = validate_selectors_with_playwright(url, dom_analysis['selector_suggestions'])
            print(f"Validated {len(validated)} selectors:")
            for v in validated:
                print(f"  ✅ {v['selector']}: {v['element_count']} elements - {v['sample_text'][:50]}...")
        else:
            print("No selector suggestions to validate!")
    else:
        print("No DOM content captured!")

if __name__ == "__main__":
    test_dom_analysis()
