"""
Module for capturing website screenshots and DOM content using Play<PERSON>
"""

import os
import asyncio
from loguru import logger
from playwright.async_api import async_playwright
from PIL import Image
import io
import base64


async def capture_website(url, browser_type="chromium", config=None):
    """
    Capture website screenshot and DOM content using Playwright
    
    Args:
        url (str): URL to capture
        browser_type (str): Browser type to use (chromium, firefox, webkit)
        config (dict): Configuration dictionary
    
    Returns:
        tuple: (screenshot_base64, dom_content, page_title)
    """
    if config is None:
        config = {}
    
    playwright_config = config.get('playwright', {})
    timeout = playwright_config.get('timeout', 30000)
    viewport = playwright_config.get('viewport', {'width': 1280, 'height': 720})
    headless = playwright_config.get('headless', True)
    
    logger.info(f"Capturing website: {url} using {browser_type}")
    
    try:
        async with async_playwright() as p:
            browser_launcher = getattr(p, browser_type)
            browser = await browser_launcher.launch(headless=headless)
            
            # Create a new page with specified viewport
            page = await browser.new_page(
                viewport={'width': viewport['width'], 'height': viewport['height']}
            )
            
            # Navigate to the URL
            await page.goto(url, timeout=timeout, wait_until='networkidle')
            logger.debug(f"Page loaded: {url}")

            # Wait additional time for dynamic content to load
            await asyncio.sleep(3)

            # Try to wait for common content indicators
            try:
                # Wait for any of these common selectors that indicate content has loaded
                await page.wait_for_selector('article, [role="article"], .news-card, .story-card, main, .content', timeout=10000)
                logger.debug("Content elements detected")
            except Exception:
                logger.debug("No specific content selectors found, proceeding with current state")

            # Additional wait for any remaining dynamic content
            await asyncio.sleep(2)

            # Capture the page title
            page_title = await page.title()

            # Capture screenshot
            screenshot_bytes = await page.screenshot(full_page=True)
            logger.debug("Screenshot captured")

            # Optimize screenshot size before encoding
            img = Image.open(io.BytesIO(screenshot_bytes))
            # Convert RGBA to RGB mode before saving as JPEG (JPEG doesn't support alpha channel)
            if img.mode == 'RGBA':
                img = img.convert('RGB')
            img_buffer = io.BytesIO()
            img.save(img_buffer, format='JPEG', quality=85, optimize=True)
            screenshot_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')

            # Extract DOM content
            dom_content = await page.content()
            logger.debug("DOM content extracted")
            
            await browser.close()
            
            return (screenshot_base64, dom_content, page_title)
    
    except Exception as e:
        logger.error(f"Error capturing website: {str(e)}")
        return None, None, None


def capture_website_sync(url, browser_type="chromium", config=None):
    """
    Synchronous wrapper for capture_website
    
    Args:
        url (str): URL to capture
        browser_type (str): Browser type to use (chromium, firefox, webkit)
        config (dict): Configuration dictionary
    
    Returns:
        tuple: (screenshot_base64, dom_content, page_title)
    """
    return asyncio.run(capture_website(url, browser_type, config))
