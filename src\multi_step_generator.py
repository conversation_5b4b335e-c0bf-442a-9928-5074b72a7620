"""
Module for generating multi-step Playwright scripts
"""

import os
import httpx
from typing import List, Dict, Optional
from loguru import logger
from openai import OpenAI, AzureOpenAI

from .action_decomposer import ActionStep, ActionDecomposer
from .dom_processor import clean_and_analyze_dom, validate_selectors_with_playwright


class MultiStepScriptGenerator:
    """Generates Playwright scripts for multi-step actions"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.openai_config = config.get('openai', {})
        self.client = self._initialize_openai_client()
        self.decomposer = ActionDecomposer()
    
    def _initialize_openai_client(self):
        """Initialize OpenAI client"""
        api_key = self.openai_config.get('api_key')
        use_azure = self.openai_config.get('use_azure', True)
        
        if not api_key:
            api_key = os.environ.get('OPENAI_API_KEY', '')
            if not api_key:
                logger.error("OpenAI API key not provided")
                return None
        
        if use_azure:
            azure_endpoint = self.openai_config.get('azure_endpoint', '')
            if not azure_endpoint:
                azure_endpoint = os.environ.get('AZURE_OPENAI_ENDPOINT', '')
            
            azure_api_version = self.openai_config.get('azure_api_version', '2023-05-15')
            
            clean_http_client = httpx.Client()
            return AzureOpenAI(
                api_key=api_key,
                api_version=azure_api_version,
                azure_endpoint=azure_endpoint,
                http_client=clean_http_client
            )
        else:
            return OpenAI(api_key=api_key)
    
    def generate_multi_step_script(self, action_text: str, url: str, dom_content: str, 
                                 vision_analysis: str, page_title: str) -> tuple[str, Dict]:
        """
        Generate a multi-step Playwright script
        
        Args:
            action_text: Complex action description
            url: Target URL
            dom_content: HTML DOM content
            vision_analysis: Vision analysis results
            page_title: Page title
            
        Returns:
            Tuple of (script_content, metadata)
        """
        logger.info(f"Generating multi-step script for: {action_text}")
        
        # Step 1: Decompose the action into steps
        action_steps = self.decomposer.decompose_action(action_text, url)
        
        # Step 2: Analyze DOM for each step
        dom_analysis = clean_and_analyze_dom(dom_content, action_text)
        
        # Step 3: Validate selectors for each step
        self._validate_step_selectors(action_steps, url)
        
        # Step 4: Generate the script
        script_content = self._generate_script_from_steps(
            action_steps, url, dom_analysis, vision_analysis, page_title, action_text
        )
        
        # Step 5: Prepare metadata
        metadata = {
            'action_steps': action_steps,
            'dom_analysis': dom_analysis,
            'step_count': len(action_steps),
            'is_multi_step': len(action_steps) > 1
        }
        
        return script_content, metadata
    
    def _validate_step_selectors(self, action_steps: List[ActionStep], url: str):
        """Validate selectors for each step against the live page"""
        logger.info("Validating selectors for multi-step action")
        
        # Collect all selectors from all steps
        all_selectors = []
        for step in action_steps:
            if step.selectors:
                for selector in step.selectors:
                    all_selectors.append({
                        'selector': selector,
                        'keyword': step.step_type,
                        'step_id': step.step_id
                    })
        
        if all_selectors:
            try:
                validated_selectors = validate_selectors_with_playwright(url, all_selectors)
                
                # Update steps with validated selectors
                for step in action_steps:
                    step_validated = [v for v in validated_selectors if v.get('step_id') == step.step_id]
                    if step_validated:
                        step.validated_selectors = step_validated
                        logger.debug(f"Step {step.step_id}: {len(step_validated)} validated selectors")
                    else:
                        step.validated_selectors = []
                        
            except Exception as e:
                logger.error(f"Error validating step selectors: {str(e)}")
                for step in action_steps:
                    step.validated_selectors = []
    
    def _generate_script_from_steps(self, action_steps: List[ActionStep], url: str, 
                                  dom_analysis: Dict, vision_analysis: str, 
                                  page_title: str, original_action: str) -> str:
        """Generate the complete multi-step script"""
        if not self.client:
            logger.error("OpenAI client not available")
            return None
        
        try:
            # Build the prompt for multi-step script generation
            prompt = self._build_multi_step_prompt(
                action_steps, url, dom_analysis, vision_analysis, page_title, original_action
            )
            
            # Get model configuration
            model = self.openai_config.get('azure_deployment_name_completion', 'gpt-4o') if self.openai_config.get('use_azure') else self.openai_config.get('model', 'gpt-4o')
            max_tokens = self.openai_config.get('max_tokens', 8000)  # Increased for multi-step scripts
            temperature = 0.1
            
            # Generate the script
            response = self.client.chat.completions.create(
                model=model,
                messages=prompt,
                max_tokens=max_tokens,
                temperature=temperature
            )
            
            script_content = response.choices[0].message.content.strip()
            
            # Clean up the response
            script_content = self._clean_script_response(script_content)
            
            logger.debug("Successfully generated multi-step script")
            return script_content
            
        except Exception as e:
            logger.error(f"Error generating multi-step script: {str(e)}")
            return None
    
    def _build_multi_step_prompt(self, action_steps: List[ActionStep], url: str, 
                               dom_analysis: Dict, vision_analysis: str, 
                               page_title: str, original_action: str) -> List[Dict]:
        """Build the prompt for multi-step script generation"""
        
        system_prompt = """You are an expert Playwright automation engineer specializing in creating complex, multi-step automation scripts. Your task is to generate a comprehensive Playwright script that executes multiple sequential steps to accomplish complex tasks.

## CORE PRINCIPLES FOR MULTI-STEP SCRIPTS:
1. **Sequential Execution**: Execute steps in the correct order with proper dependencies
2. **State Management**: Maintain data and state between steps
3. **Error Handling**: Handle failures at each step gracefully
4. **Data Flow**: Pass data from one step to the next efficiently
5. **Robust Selectors**: Use validated selectors with multiple fallback strategies
6. **Progress Tracking**: Provide clear feedback on which step is executing
7. **Result Aggregation**: Collect and format results from multiple steps

## SCRIPT STRUCTURE REQUIREMENTS:
```python
from playwright.sync_api import sync_playwright, TimeoutError
import time
import json

def execute_multi_step_action():
    url = "TARGET_URL"
    results = {}  # Store results from each step
    
    try:
        with sync_playwright() as playwright:
            browser = playwright.chromium.launch(headless=False)
            context = browser.new_context()
            page = context.new_page()
            
            # Step 1: Navigation
            print("Step 1: Navigating to page...")
            # Implementation
            
            # Step 2: Search/Interaction
            print("Step 2: Performing search...")
            # Implementation with error handling
            
            # Step 3: Data Extraction
            print("Step 3: Extracting data...")
            # Implementation with multiple selector strategies
            
            # Step 4: Output/Processing
            print("Step 4: Processing and outputting results...")
            # Implementation
            
            browser.close()
            
    except Exception as e:
        print(f"Error in multi-step execution: {e}")

if __name__ == "__main__":
    execute_multi_step_action()
```

## MANDATORY REQUIREMENTS:
- Implement each step as a separate, well-defined function or code block
- Use validated selectors when available, with fallbacks
- Add comprehensive error handling for each step
- Include progress indicators and debugging output
- Handle data flow between steps properly
- Implement proper wait conditions for dynamic content
- Provide meaningful output formatting

Your response must be ONLY valid, executable Python code with NO markdown formatting."""

        # Format the action steps for the prompt
        steps_description = self._format_steps_for_prompt(action_steps)
        
        # Format DOM analysis
        dom_context = self._format_dom_analysis_for_prompt(dom_analysis)
        
        user_prompt = f"""Generate a comprehensive multi-step Playwright script for the following complex action:

**Original Action**: {original_action}
**Target URL**: {url}
**Page Title**: {page_title}

## DECOMPOSED ACTION STEPS:
{steps_description}

## DOM ANALYSIS:
{dom_context}

## VISUAL ANALYSIS:
{vision_analysis}

## SPECIFIC REQUIREMENTS:

1. **Execute Steps Sequentially**: Implement each step in the correct order with proper error handling
2. **Use Validated Selectors**: Prioritize validated selectors for each step, with fallback strategies
3. **Maintain State**: Store intermediate results and pass data between steps
4. **Handle Dynamic Content**: Add appropriate wait conditions for each step
5. **Provide Progress Feedback**: Include clear progress indicators and debugging output
6. **Format Output Properly**: Ensure the final output meets the user's expectations
7. **Error Recovery**: Handle failures gracefully and provide meaningful error messages

## IMPLEMENTATION GUIDELINES:

- Start with navigation to the target URL
- Implement each step as a distinct code block with clear comments
- Use try-catch blocks for each major step
- Store results in a structured format (dictionary/list)
- Implement multiple selector strategies for critical elements
- Add proper wait conditions between steps
- Provide detailed logging of each step's progress
- Format the final output in a user-friendly manner

Generate a complete, production-ready multi-step Playwright script that accomplishes the specified action reliably."""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
    
    def _format_steps_for_prompt(self, action_steps: List[ActionStep]) -> str:
        """Format action steps for inclusion in the prompt"""
        formatted = ""
        
        for i, step in enumerate(action_steps, 1):
            formatted += f"""
**Step {i}: {step.description}**
- Type: {step.step_type}
- Target: {step.target_element or 'N/A'}
- Input Data: {step.input_data or 'N/A'}
- Expected Output: {step.expected_output or 'N/A'}
- Dependencies: {', '.join(step.depends_on) if step.depends_on else 'None'}
"""
            
            if hasattr(step, 'validated_selectors') and step.validated_selectors:
                formatted += f"- Validated Selectors: {', '.join([s['selector'] for s in step.validated_selectors])}\n"
            elif step.selectors:
                formatted += f"- Suggested Selectors: {', '.join(step.selectors)}\n"
            
            if step.validation_criteria:
                formatted += f"- Success Criteria: {', '.join(step.validation_criteria.keys())}\n"
            
            formatted += "\n"
        
        return formatted
    
    def _format_dom_analysis_for_prompt(self, dom_analysis: Dict) -> str:
        """Format DOM analysis for the prompt"""
        if not dom_analysis:
            return "No DOM analysis available."
        
        formatted = f"""
**Page Structure**:
- Interactive Elements: {len(dom_analysis.get('interactive_elements', []))}
- Content Elements: {len(dom_analysis.get('content_elements', []))}
- Form Elements: {len(dom_analysis.get('form_elements', []))}

**Validated Selectors**:
"""
        
        if dom_analysis.get('validated_selectors'):
            for selector in dom_analysis['validated_selectors'][:10]:
                formatted += f"✅ {selector['selector']} - {selector['element_count']} elements\n"
        else:
            formatted += "No validated selectors available.\n"
        
        return formatted
    
    def _clean_script_response(self, script_content: str) -> str:
        """Clean the LLM response to extract just the Python code"""
        script_content = script_content.strip()
        
        if script_content.startswith('```python'):
            script_content = script_content[9:]
        elif script_content.startswith('```'):
            script_content = script_content[3:]
        
        if script_content.endswith('```'):
            script_content = script_content[:-3]
        
        return script_content.strip()
