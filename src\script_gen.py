"""
Module for generating Playwright scripts based on DOM content and Vision API analysis
"""

import os
import httpx
from loguru import logger
from openai import OpenAI, AzureOpenAI
from .dom_processor import clean_and_analyze_dom, validate_selectors_with_playwright


def format_dom_analysis(dom_analysis):
    """Format DOM analysis into a structured string for the LLM"""
    formatted = f"""
## DOM ANALYSIS SUMMARY

**Page Title:** {dom_analysis['page_title']}

**Interactive Elements Found:** {len(dom_analysis['interactive_elements'])}
**Form Elements Found:** {len(dom_analysis['form_elements'])}
**Navigation Elements Found:** {len(dom_analysis['navigation_elements'])}
**Content Elements Found:** {len(dom_analysis['content_elements'])}

### KEY INTERACTIVE ELEMENTS:
"""

    # Add top interactive elements
    for i, element in enumerate(dom_analysis['interactive_elements'][:10]):
        formatted += f"""
Element {i+1}:
- Tag: {element['tag']}
- Text: "{element['text']}"
- Suggested Selectors: {', '.join(element['selector_options'][:3])}
- Attributes: {element['attributes']}
"""

    # Add form information
    if dom_analysis['form_elements']:
        formatted += "\n### FORM ELEMENTS:\n"
        for i, form in enumerate(dom_analysis['form_elements'][:3]):
            formatted += f"""
Form {i+1}:
- Action: {form['action']}
- Method: {form['method']}
- Input Count: {len(form['inputs'])}
- Selectors: {', '.join(form['selector_options'][:2])}
"""

    # Add selector suggestions
    if dom_analysis['selector_suggestions']:
        formatted += "\n### SMART SELECTOR SUGGESTIONS:\n"
        for suggestion in dom_analysis['selector_suggestions'][:5]:
            formatted += f"- {suggestion['keyword']}: {suggestion['selector']} (confidence: {suggestion['confidence']})\n"

    # Add validated selectors (most important!)
    if dom_analysis.get('validated_selectors'):
        formatted += "\n### ⭐ VALIDATED SELECTORS (USE THESE FIRST!):\n"
        for selector in dom_analysis['validated_selectors'][:10]:
            formatted += f"""
✅ WORKING SELECTOR: {selector['selector']}
   - Element Count: {selector['element_count']}
   - Keyword: {selector['keyword']}
   - Sample Text: "{selector['sample_text']}"
   - Confidence: {selector['confidence']}
"""

    return formatted


def post_process_script(script):
    """Post-process the generated script to ensure it's clean and properly formatted"""
    # Remove markdown code blocks if present
    script = script.strip()

    # Remove markdown code block markers
    if script.startswith('```python'):
        script = script[9:]
    elif script.startswith('```'):
        script = script[3:]

    if script.endswith('```'):
        script = script[:-3]

    # Clean up any extra whitespace
    script = script.strip()

    # Ensure proper imports are at the top
    lines = script.split('\n')
    cleaned_lines = []

    for line in lines:
        # Skip empty lines at the beginning
        if not cleaned_lines and not line.strip():
            continue
        cleaned_lines.append(line)

    # Remove trailing empty lines
    while cleaned_lines and not cleaned_lines[-1].strip():
        cleaned_lines.pop()

    return '\n'.join(cleaned_lines)


def generate_script(dom_content, vision_analysis, action_text, url, page_title, config):
    """
    Generate a Playwright script based on DOM content and Vision API analysis

    Args:
        dom_content (str): HTML DOM content
        vision_analysis (str): Analysis from OpenAI Vision API
        action_text (str): Description of the action to automate
        url (str): URL of the website
        page_title (str): Title of the webpage
        config (dict): Configuration dictionary

    Returns:
        str: Generated Playwright script
    """
    openai_config = config.get('openai', {})
    script_config = config.get('script', {})

    api_key = openai_config.get('api_key')
    model = openai_config.get('model', 'gpt-4o')
    max_tokens = openai_config.get('max_tokens', 6000)  # Increased for more detailed scripts
    temperature = openai_config.get('temperature', 0.1)  # Lower for more consistent output
    use_azure = openai_config.get('use_azure', True)

    add_comments = script_config.get('add_comments', True)
    include_assertions = script_config.get('include_assertions', True)

    if not api_key:
        api_key = os.environ.get('OPENAI_API_KEY', '')
        if not api_key:
            logger.error("OpenAI API key not provided in config or environment")
            return None

    # Initialize the appropriate client based on configuration
    client = None
    if use_azure:
        azure_endpoint = openai_config.get('azure_endpoint', '')
        if not azure_endpoint:
            azure_endpoint = os.environ.get('AZURE_OPENAI_ENDPOINT', '')
            if not azure_endpoint:
                logger.error("Azure OpenAI endpoint not provided in config or environment")
                return None

        azure_api_version = openai_config.get('azure_api_version', '2023-05-15')
        azure_deployment = openai_config.get('azure_deployment_name_completion', '')

        if not azure_deployment:
            logger.error("Azure OpenAI deployment name for completion model not provided")
            return None

        logger.info(f"Using Azure OpenAI with endpoint: {azure_endpoint} and deployment: {azure_deployment}")
        # Create a clean http client to avoid inheriting any global proxy settings
        clean_http_client = httpx.Client()
        client = AzureOpenAI(
            api_key=api_key,
            api_version=azure_api_version,
            azure_endpoint=azure_endpoint,
            http_client=clean_http_client
        )
        # For Azure, we use the deployment name instead of the model name
        model = azure_deployment
    else:
        logger.info(f"Using OpenAI with model: {model}")
        client = OpenAI(api_key=api_key)

    # Process DOM content for better analysis
    logger.info("Processing DOM content for enhanced analysis")
    dom_analysis = clean_and_analyze_dom(dom_content, action_text)

    # Validate selectors against the actual page
    logger.info("Validating selectors against the live page")
    if dom_analysis['selector_suggestions']:
        try:
            validated_selectors = validate_selectors_with_playwright(url, dom_analysis['selector_suggestions'])
            dom_analysis['validated_selectors'] = validated_selectors
            logger.info(f"Validated {len(validated_selectors)} selectors successfully")

            # Log the validated selectors for debugging
            for selector in validated_selectors:
                logger.info(f"✅ Validated selector: {selector['selector']} ({selector['element_count']} elements)")
        except Exception as e:
            logger.error(f"Error during selector validation: {str(e)}")
            dom_analysis['validated_selectors'] = []
    else:
        logger.warning("No selector suggestions found for validation")
        dom_analysis['validated_selectors'] = []

    # Prepare structured DOM information
    structured_dom_info = format_dom_analysis(dom_analysis)
    
    try:
        logger.info("Generating Playwright script from combined context")

        # Create enhanced system prompt
        system_prompt = """You are an expert Playwright automation engineer with deep expertise in creating robust, production-ready automation scripts. Your task is to generate highly accurate, resilient, and maintainable Playwright scripts in Python.

## CORE PRINCIPLES:
1. **Reliability First**: Always implement multiple selector strategies with fallbacks
2. **Robust Error Handling**: Handle timeouts, missing elements, and edge cases gracefully
3. **Smart Waiting**: Use appropriate wait conditions for dynamic content
4. **Selector Hierarchy**: Prefer data-testid > id > unique attributes > CSS classes > XPath > text-based selectors
5. **Maintainability**: Write clean, well-commented, modular code
6. **Real-world Resilience**: Account for loading states, dynamic content, and varying page conditions

## REQUIRED SCRIPT STRUCTURE:
```python
from playwright.sync_api import sync_playwright, TimeoutError
import time

def main_function():
    url = "TARGET_URL"

    try:
        with sync_playwright() as playwright:
            browser = playwright.chromium.launch(headless=False)  # Set to True for production
            context = browser.new_context()
            page = context.new_page()

            # Navigate with proper error handling
            page.goto(url, timeout=60000)
            page.wait_for_load_state("networkidle", timeout=30000)

            # IMPLEMENT MULTIPLE SELECTOR STRATEGIES
            # Strategy 1: Most reliable selectors
            # Strategy 2: Fallback selectors
            # Strategy 3: Text-based or XPath fallbacks

            # IMPLEMENT THE REQUESTED ACTION WITH ROBUST ERROR HANDLING

            # VERIFY SUCCESS WITH APPROPRIATE ASSERTIONS

            browser.close()

    except TimeoutError as e:
        print(f"TimeoutError: {e}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main_function()
```

## MANDATORY REQUIREMENTS:
- Implement at least 3 different selector strategies for critical elements
- Include comprehensive error handling for all interactions
- Add appropriate wait conditions (wait_for_selector, wait_for_load_state, etc.)
- Use descriptive variable names and add helpful comments
- Include success verification and meaningful error messages
- Handle dynamic content and loading states
- Provide fallback approaches when primary selectors fail

Your response must be ONLY valid, executable Python code with NO markdown formatting, explanations, or additional text."""

        # Construct the enhanced user message
        user_message = f"""Generate a robust Playwright automation script for this specific action: "{action_text}"

**TARGET WEBSITE:** {url}
**PAGE TITLE:** {page_title}

## COMPREHENSIVE VISUAL ANALYSIS:
{vision_analysis}

## STRUCTURED DOM ANALYSIS:
{structured_dom_info}

## SPECIFIC REQUIREMENTS FOR THIS SCRIPT:

1. **Primary Objective**: {action_text}

2. **Selector Strategy**: Based on the DOM analysis, implement multiple selector approaches:
   - Use the most reliable selectors identified in the DOM analysis
   - Implement fallback selectors for robustness
   - Handle cases where elements might not be immediately visible

3. **Error Handling**: Account for these potential issues:
   - Elements that load dynamically
   - Timeouts and network delays
   - Elements that might be hidden or require scrolling
   - Page structure variations

4. **Verification**: Include appropriate checks to verify the action was successful

5. **Robustness Features**:
   - Multiple selector strategies with fallbacks
   - Proper wait conditions for dynamic content
   - Graceful error handling with informative messages
   - Success verification and result reporting

## IMPLEMENTATION NOTES:
- **CRITICAL**: Use the VALIDATED SELECTORS first - these have been tested against the live page and are guaranteed to work
- If validated selectors are provided, use them as your primary selectors in the script
- Implement the fallback strategies I demonstrated in the working output_script.py
- Include debugging information (like printing actual page title, element counts, etc.)
- Make the script resilient to page structure changes
- Add appropriate timeouts and wait conditions
- Always try multiple selector strategies with proper error handling

## SELECTOR PRIORITY (MOST IMPORTANT):
1. **VALIDATED SELECTORS** (marked with ✅) - Use these FIRST as they are tested and working
2. Smart selector suggestions from DOM analysis
3. Fallback selectors (XPath, text-based)

Generate a complete, production-ready Playwright script that implements the requested action with maximum reliability and proper error handling."""

        # Add configuration-based requirements
        if add_comments:
            user_message += "\n\n**COMMENTING**: Include detailed comments explaining the logic, selector choices, and error handling strategies."

        if include_assertions:
            user_message += "\n\n**ASSERTIONS**: Include comprehensive assertions to verify each step of the automation process."
            
        # Call the API using the appropriate client
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ],
            max_tokens=max_tokens,
            temperature=temperature
        )
        
        script = response.choices[0].message.content
        logger.debug("Successfully generated Playwright script")

        # Post-process the script to ensure it's clean
        cleaned_script = post_process_script(script)

        return cleaned_script
        
    except Exception as e:
        logger.error(f"Error generating script: {str(e)}")
        return None


def save_script(script, output_path, config):
    """
    Save generated script to file
    
    Args:
        script (str): Generated script content
        output_path (str): Path to save the script
        config (dict): Configuration dictionary
        
    Returns:
        bool: True if successful, False otherwise
    """
    output_config = config.get('output', {})
    force_overwrite = output_config.get('force_overwrite', False)
    
    # Check if file exists and we're not forcing overwrite
    if os.path.exists(output_path) and not force_overwrite:
        logger.warning(f"Output file already exists: {output_path}")
        # Append a number to the filename
        base, ext = os.path.splitext(output_path)
        counter = 1
        while os.path.exists(f"{base}_{counter}{ext}"):
            counter += 1
        output_path = f"{base}_{counter}{ext}"
        logger.info(f"Using alternative output path: {output_path}")
    
    try:
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as file:
            file.write(script)
        
        logger.success(f"Script saved to: {output_path}")
        return True
    except Exception as e:
        logger.error(f"Error saving script: {str(e)}")
        return False
