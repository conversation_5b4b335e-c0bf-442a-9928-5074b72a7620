"""
Module for generating Playwright scripts based on DOM content and Vision API analysis
"""

import os
import httpx
from loguru import logger
from openai import OpenAI, AzureOpenAI


def generate_script(dom_content, vision_analysis, action_text, url, page_title, config):
    """
    Generate a Playwright script based on DOM content and Vision API analysis
    
    Args:
        dom_content (str): HTML DOM content
        vision_analysis (str): Analysis from OpenAI Vision API
        action_text (str): Description of the action to automate
        url (str): URL of the website
        page_title (str): Title of the webpage
        config (dict): Configuration dictionary
    
    Returns:
        str: Generated Playwright script
    """
    openai_config = config.get('openai', {})
    script_config = config.get('script', {})
    
    api_key = openai_config.get('api_key')
    model = openai_config.get('model', 'gpt-4o')
    max_tokens = openai_config.get('max_tokens', 4000)
    temperature = openai_config.get('temperature', 0.2)
    use_azure = openai_config.get('use_azure', True)
    
    add_comments = script_config.get('add_comments', True)
    include_assertions = script_config.get('include_assertions', True)
    
    if not api_key:
        api_key = os.environ.get('OPENAI_API_KEY', '')
        if not api_key:
            logger.error("OpenAI API key not provided in config or environment")
            return None

    # Initialize the appropriate client based on configuration
    client = None
    if use_azure:
        azure_endpoint = openai_config.get('azure_endpoint', '')
        if not azure_endpoint:
            azure_endpoint = os.environ.get('AZURE_OPENAI_ENDPOINT', '')
            if not azure_endpoint:
                logger.error("Azure OpenAI endpoint not provided in config or environment")
                return None
                
        azure_api_version = openai_config.get('azure_api_version', '2023-05-15')
        azure_deployment = openai_config.get('azure_deployment_name_completion', '')
        
        if not azure_deployment:
            logger.error("Azure OpenAI deployment name for completion model not provided")
            return None
            
        logger.info(f"Using Azure OpenAI with endpoint: {azure_endpoint} and deployment: {azure_deployment}")
        # Create a clean http client to avoid inheriting any global proxy settings
        clean_http_client = httpx.Client()
        client = AzureOpenAI(
            api_key=api_key,
            api_version=azure_api_version,
            azure_endpoint=azure_endpoint,
            http_client=clean_http_client
        )
        # For Azure, we use the deployment name instead of the model name
        model = azure_deployment
    else:
        logger.info(f"Using OpenAI with model: {model}")
        client = OpenAI(api_key=api_key)
    
    # Truncate DOM content if too large
    max_dom_chars = 50000
    if len(dom_content) > max_dom_chars:
        dom_content = dom_content[:max_dom_chars] + "... [truncated]"
        logger.warning(f"DOM content truncated to {max_dom_chars} characters")
    
    try:
        logger.info("Generating Playwright script from combined context")
        
        # Create system prompt
        system_prompt = """You are an expert Playwright automation script generator. 
Your task is to create accurate, resilient, and maintainable Playwright scripts in Python.
Generate only valid Python code with proper imports and error handling.
Focus on using the most reliable selectors based on the DOM structure and visual analysis.
Follow these best practices:
1. Use meaningful variable names
2. Add appropriate wait statements and timeouts
3. Include error handling
4. Structure the code for maintainability
5. Use the most reliable selectors (prefer data-testid, id, then CSS selectors)
6. Add assertions to verify expected results
Your response should be ONLY valid, executable Python code with no markdown formatting or explanations.
"""

        # Construct the user message
        user_message = f"""Create a Playwright script to automate this action: "{action_text}" on the website with URL: {url} (title: {page_title}).

HERE IS THE VISUAL ANALYSIS OF THE PAGE:
{vision_analysis}

HERE IS A PORTION OF THE PAGE'S DOM STRUCTURE:
{dom_content}

Using both the visual analysis and DOM structure, create a complete Playwright automation script in Python.
The script should:
1. Import necessary libraries
2. Navigate to the URL
3. Perform the requested action
4. Include appropriate assertions and error handling
5. Clean up resources properly
"""

        # Add specific requirements based on config
        if not add_comments:
            user_message += "\nGenerate the code with minimal comments, focusing on essential explanations only."
        
        if include_assertions:
            user_message += "\nInclude appropriate assertions to verify the action was successful."
            
        # Call the API using the appropriate client
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ],
            max_tokens=max_tokens,
            temperature=temperature
        )
        
        script = response.choices[0].message.content
        logger.debug("Successfully generated Playwright script")
        
        return script
        
    except Exception as e:
        logger.error(f"Error generating script: {str(e)}")
        return None


def save_script(script, output_path, config):
    """
    Save generated script to file
    
    Args:
        script (str): Generated script content
        output_path (str): Path to save the script
        config (dict): Configuration dictionary
        
    Returns:
        bool: True if successful, False otherwise
    """
    output_config = config.get('output', {})
    force_overwrite = output_config.get('force_overwrite', False)
    
    # Check if file exists and we're not forcing overwrite
    if os.path.exists(output_path) and not force_overwrite:
        logger.warning(f"Output file already exists: {output_path}")
        # Append a number to the filename
        base, ext = os.path.splitext(output_path)
        counter = 1
        while os.path.exists(f"{base}_{counter}{ext}"):
            counter += 1
        output_path = f"{base}_{counter}{ext}"
        logger.info(f"Using alternative output path: {output_path}")
    
    try:
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as file:
            file.write(script)
        
        logger.success(f"Script saved to: {output_path}")
        return True
    except Exception as e:
        logger.error(f"Error saving script: {str(e)}")
        return False
