var Mp=Object.defineProperty;var jp=(e,t,n)=>t in e?Mp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Z=(e,t,n)=>(jp(e,typeof t!="symbol"?t+"":t,n),n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const o of s.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();var Py=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ip(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Pc={exports:{}},bs={},Rc={exports:{}},O={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ni=Symbol.for("react.element"),Pp=Symbol.for("react.portal"),Rp=Symbol.for("react.fragment"),$p=Symbol.for("react.strict_mode"),Op=Symbol.for("react.profiler"),zp=Symbol.for("react.provider"),Dp=Symbol.for("react.context"),Hp=Symbol.for("react.forward_ref"),Fp=Symbol.for("react.suspense"),Up=Symbol.for("react.memo"),Vp=Symbol.for("react.lazy"),qa=Symbol.iterator;function Bp(e){return e===null||typeof e!="object"?null:(e=qa&&e[qa]||e["@@iterator"],typeof e=="function"?e:null)}var $c={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Oc=Object.assign,zc={};function Jn(e,t,n){this.props=e,this.context=t,this.refs=zc,this.updater=n||$c}Jn.prototype.isReactComponent={};Jn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Jn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Dc(){}Dc.prototype=Jn.prototype;function Hl(e,t,n){this.props=e,this.context=t,this.refs=zc,this.updater=n||$c}var Fl=Hl.prototype=new Dc;Fl.constructor=Hl;Oc(Fl,Jn.prototype);Fl.isPureReactComponent=!0;var Xa=Array.isArray,Hc=Object.prototype.hasOwnProperty,Ul={current:null},Fc={key:!0,ref:!0,__self:!0,__source:!0};function Uc(e,t,n){var r,i={},s=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(s=""+t.key),t)Hc.call(t,r)&&!Fc.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];i.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)i[r]===void 0&&(i[r]=l[r]);return{$$typeof:ni,type:e,key:s,ref:o,props:i,_owner:Ul.current}}function Wp(e,t){return{$$typeof:ni,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Vl(e){return typeof e=="object"&&e!==null&&e.$$typeof===ni}function qp(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Qa=/\/+/g;function Zs(e,t){return typeof e=="object"&&e!==null&&e.key!=null?qp(""+e.key):t.toString(36)}function Ii(e,t,n,r,i){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(s){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case ni:case Pp:o=!0}}if(o)return o=e,i=i(o),e=r===""?"."+Zs(o,0):r,Xa(i)?(n="",e!=null&&(n=e.replace(Qa,"$&/")+"/"),Ii(i,t,n,"",function(u){return u})):i!=null&&(Vl(i)&&(i=Wp(i,n+(!i.key||o&&o.key===i.key?"":(""+i.key).replace(Qa,"$&/")+"/")+e)),t.push(i)),1;if(o=0,r=r===""?".":r+":",Xa(e))for(var l=0;l<e.length;l++){s=e[l];var a=r+Zs(s,l);o+=Ii(s,t,n,a,i)}else if(a=Bp(e),typeof a=="function")for(e=a.call(e),l=0;!(s=e.next()).done;)s=s.value,a=r+Zs(s,l++),o+=Ii(s,t,n,a,i);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function di(e,t,n){if(e==null)return e;var r=[],i=0;return Ii(e,r,"","",function(s){return t.call(n,s,i++)}),r}function Xp(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Te={current:null},Pi={transition:null},Qp={ReactCurrentDispatcher:Te,ReactCurrentBatchConfig:Pi,ReactCurrentOwner:Ul};O.Children={map:di,forEach:function(e,t,n){di(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return di(e,function(){t++}),t},toArray:function(e){return di(e,function(t){return t})||[]},only:function(e){if(!Vl(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};O.Component=Jn;O.Fragment=Rp;O.Profiler=Op;O.PureComponent=Hl;O.StrictMode=$p;O.Suspense=Fp;O.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Qp;O.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Oc({},e.props),i=e.key,s=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,o=Ul.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)Hc.call(t,a)&&!Fc.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:ni,type:e.type,key:i,ref:s,props:r,_owner:o}};O.createContext=function(e){return e={$$typeof:Dp,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:zp,_context:e},e.Consumer=e};O.createElement=Uc;O.createFactory=function(e){var t=Uc.bind(null,e);return t.type=e,t};O.createRef=function(){return{current:null}};O.forwardRef=function(e){return{$$typeof:Hp,render:e}};O.isValidElement=Vl;O.lazy=function(e){return{$$typeof:Vp,_payload:{_status:-1,_result:e},_init:Xp}};O.memo=function(e,t){return{$$typeof:Up,type:e,compare:t===void 0?null:t}};O.startTransition=function(e){var t=Pi.transition;Pi.transition={};try{e()}finally{Pi.transition=t}};O.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")};O.useCallback=function(e,t){return Te.current.useCallback(e,t)};O.useContext=function(e){return Te.current.useContext(e)};O.useDebugValue=function(){};O.useDeferredValue=function(e){return Te.current.useDeferredValue(e)};O.useEffect=function(e,t){return Te.current.useEffect(e,t)};O.useId=function(){return Te.current.useId()};O.useImperativeHandle=function(e,t,n){return Te.current.useImperativeHandle(e,t,n)};O.useInsertionEffect=function(e,t){return Te.current.useInsertionEffect(e,t)};O.useLayoutEffect=function(e,t){return Te.current.useLayoutEffect(e,t)};O.useMemo=function(e,t){return Te.current.useMemo(e,t)};O.useReducer=function(e,t,n){return Te.current.useReducer(e,t,n)};O.useRef=function(e){return Te.current.useRef(e)};O.useState=function(e){return Te.current.useState(e)};O.useSyncExternalStore=function(e,t,n){return Te.current.useSyncExternalStore(e,t,n)};O.useTransition=function(){return Te.current.useTransition()};O.version="18.2.0";Rc.exports=O;var L=Rc.exports;const zt=Ip(L);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Kp=L,Gp=Symbol.for("react.element"),Yp=Symbol.for("react.fragment"),Jp=Object.prototype.hasOwnProperty,Zp=Kp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,em={key:!0,ref:!0,__self:!0,__source:!0};function Vc(e,t,n){var r,i={},s=null,o=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)Jp.call(t,r)&&!em.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Gp,type:e,key:s,ref:o,props:i,_owner:Zp.current}}bs.Fragment=Yp;bs.jsx=Vc;bs.jsxs=Vc;Pc.exports=bs;var d=Pc.exports;function tm(e,t,n,r){const[i,s]=zt.useState(n);return zt.useEffect(()=>{let o=!1;return r!==void 0&&s(r),e().then(l=>{o||s(l)}),()=>{o=!0}},t),i}function pn(){const e=zt.useRef(null),[t,n]=zt.useState(new DOMRect(0,0,10,10));return zt.useLayoutEffect(()=>{const r=e.current;if(!r)return;const i=new ResizeObserver(s=>{const o=s[s.length-1];o&&o.contentRect&&n(o.contentRect)});return i.observe(r),()=>i.disconnect()},[e]),[t,e]}function Qe(e){if(!isFinite(e))return"-";if(e===0)return"0";if(e<1e3)return e.toFixed(0)+"ms";const t=e/1e3;if(t<60)return t.toFixed(1)+"s";const n=t/60;if(n<60)return n.toFixed(1)+"m";const r=n/60;return r<24?r.toFixed(1)+"h":(r/24).toFixed(1)+"d"}function nm(e){if(e<0||!isFinite(e))return"-";if(e===0)return"0";if(e<1e3)return e.toFixed(0);const t=e/1024;if(t<1e3)return t.toFixed(1)+"K";const n=t/1024;return n<1e3?n.toFixed(1)+"M":(n/1024).toFixed(1)+"G"}function Bc(e,t,n,r,i){let s=r||0,o=i!==void 0?i:e.length;for(;s<o;){const l=s+o>>1;n(t,e[l])>=0?s=l+1:o=l}return o}function rm(e){const t=document.createElement("textarea");t.style.position="absolute",t.style.zIndex="-1000",t.value=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),t.remove()}function Ji(e,t){const n=e?Or.getObject(e,t):t,[r,i]=zt.useState(n);return[r,o=>{e&&Or.setObject(e,o),i(o)}]}class im{getString(t,n){return localStorage[t]||n}setString(t,n){localStorage[t]=n,window.saveSettings&&window.saveSettings()}getObject(t,n){if(!localStorage[t])return n;try{return JSON.parse(localStorage[t])}catch{return n}}setObject(t,n){localStorage[t]=JSON.stringify(n),window.saveSettings&&window.saveSettings()}}const Or=new im;function Ry(){if(document.playwrightThemeInitialized)return;document.playwrightThemeInitialized=!0,document.defaultView.addEventListener("focus",n=>{n.target.document.nodeType===Node.DOCUMENT_NODE&&document.body.classList.remove("inactive")},!1),document.defaultView.addEventListener("blur",n=>{document.body.classList.add("inactive")},!1);const e=Or.getString("theme","light-mode"),t=window.matchMedia("(prefers-color-scheme: dark)");(e==="dark-mode"||t.matches)&&document.body.classList.add("dark-mode")}const Bl=new Set;function $y(){const e=Or.getString("theme","light-mode");let t;e==="dark-mode"?t="light-mode":t="dark-mode",e&&document.body.classList.remove(e),document.body.classList.add(t),Or.setString("theme",t);for(const n of Bl)n(t)}function Oy(e){Bl.add(e)}function zy(e){Bl.delete(e)}function Dy(){return document.body.classList.contains("dark-mode")?"dark-mode":"light-mode"}var Wc={exports:{}},Ue={},qc={exports:{}},Xc={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(M,R){var $=M.length;M.push(R);e:for(;0<$;){var W=$-1>>>1,ne=M[W];if(0<i(ne,R))M[W]=R,M[$]=ne,$=W;else break e}}function n(M){return M.length===0?null:M[0]}function r(M){if(M.length===0)return null;var R=M[0],$=M.pop();if($!==R){M[0]=$;e:for(var W=0,ne=M.length,Je=ne>>>1;W<Je;){var pt=2*(W+1)-1,or=M[pt],mt=pt+1,vn=M[mt];if(0>i(or,$))mt<ne&&0>i(vn,or)?(M[W]=vn,M[mt]=$,W=mt):(M[W]=or,M[pt]=$,W=pt);else if(mt<ne&&0>i(vn,$))M[W]=vn,M[mt]=$,W=mt;else break e}}return R}function i(M,R){var $=M.sortIndex-R.sortIndex;return $!==0?$:M.id-R.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var o=Date,l=o.now();e.unstable_now=function(){return o.now()-l}}var a=[],u=[],c=1,f=null,p=3,v=!1,y=!1,w=!1,x=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(M){for(var R=n(u);R!==null;){if(R.callback===null)r(u);else if(R.startTime<=M)r(u),R.sortIndex=R.expirationTime,t(a,R);else break;R=n(u)}}function _(M){if(w=!1,g(M),!y)if(n(a)!==null)y=!0,Ne(N);else{var R=n(u);R!==null&&ue(_,R.startTime-M)}}function N(M,R){y=!1,w&&(w=!1,h(E),E=-1),v=!0;var $=p;try{for(g(R),f=n(a);f!==null&&(!(f.expirationTime>R)||M&&!P());){var W=f.callback;if(typeof W=="function"){f.callback=null,p=f.priorityLevel;var ne=W(f.expirationTime<=R);R=e.unstable_now(),typeof ne=="function"?f.callback=ne:f===n(a)&&r(a),g(R)}else r(a);f=n(a)}if(f!==null)var Je=!0;else{var pt=n(u);pt!==null&&ue(_,pt.startTime-R),Je=!1}return Je}finally{f=null,p=$,v=!1}}var k=!1,C=null,E=-1,S=5,A=-1;function P(){return!(e.unstable_now()-A<S)}function T(){if(C!==null){var M=e.unstable_now();A=M;var R=!0;try{R=C(!0,M)}finally{R?I():(k=!1,C=null)}}else k=!1}var I;if(typeof m=="function")I=function(){m(T)};else if(typeof MessageChannel<"u"){var z=new MessageChannel,ae=z.port2;z.port1.onmessage=T,I=function(){ae.postMessage(null)}}else I=function(){x(T,0)};function Ne(M){C=M,k||(k=!0,I())}function ue(M,R){E=x(function(){M(e.unstable_now())},R)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(M){M.callback=null},e.unstable_continueExecution=function(){y||v||(y=!0,Ne(N))},e.unstable_forceFrameRate=function(M){0>M||125<M?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):S=0<M?Math.floor(1e3/M):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(M){switch(p){case 1:case 2:case 3:var R=3;break;default:R=p}var $=p;p=R;try{return M()}finally{p=$}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(M,R){switch(M){case 1:case 2:case 3:case 4:case 5:break;default:M=3}var $=p;p=M;try{return R()}finally{p=$}},e.unstable_scheduleCallback=function(M,R,$){var W=e.unstable_now();switch(typeof $=="object"&&$!==null?($=$.delay,$=typeof $=="number"&&0<$?W+$:W):$=W,M){case 1:var ne=-1;break;case 2:ne=250;break;case 5:ne=**********;break;case 4:ne=1e4;break;default:ne=5e3}return ne=$+ne,M={id:c++,callback:R,priorityLevel:M,startTime:$,expirationTime:ne,sortIndex:-1},$>W?(M.sortIndex=$,t(u,M),n(a)===null&&M===n(u)&&(w?(h(E),E=-1):w=!0,ue(_,$-W))):(M.sortIndex=ne,t(a,M),y||v||(y=!0,Ne(N))),M},e.unstable_shouldYield=P,e.unstable_wrapCallback=function(M){var R=p;return function(){var $=p;p=R;try{return M.apply(this,arguments)}finally{p=$}}}})(Xc);qc.exports=Xc;var sm=qc.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qc=L,He=sm;function b(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Kc=new Set,zr={};function mn(e,t){Wn(e,t),Wn(e+"Capture",t)}function Wn(e,t){for(zr[e]=t,e=0;e<t.length;e++)Kc.add(t[e])}var kt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ro=Object.prototype.hasOwnProperty,om=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ka={},Ga={};function lm(e){return Ro.call(Ga,e)?!0:Ro.call(Ka,e)?!1:om.test(e)?Ga[e]=!0:(Ka[e]=!0,!1)}function am(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function um(e,t,n,r){if(t===null||typeof t>"u"||am(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ce(e,t,n,r,i,s,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=o}var me={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){me[e]=new Ce(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];me[t]=new Ce(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){me[e]=new Ce(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){me[e]=new Ce(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){me[e]=new Ce(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){me[e]=new Ce(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){me[e]=new Ce(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){me[e]=new Ce(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){me[e]=new Ce(e,5,!1,e.toLowerCase(),null,!1,!1)});var Wl=/[\-:]([a-z])/g;function ql(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Wl,ql);me[t]=new Ce(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Wl,ql);me[t]=new Ce(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Wl,ql);me[t]=new Ce(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){me[e]=new Ce(e,1,!1,e.toLowerCase(),null,!1,!1)});me.xlinkHref=new Ce("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){me[e]=new Ce(e,1,!1,e.toLowerCase(),null,!0,!0)});function Xl(e,t,n,r){var i=me.hasOwnProperty(t)?me[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(um(t,n,i,r)&&(n=null),r||i===null?lm(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var bt=Qc.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,fi=Symbol.for("react.element"),kn=Symbol.for("react.portal"),Tn=Symbol.for("react.fragment"),Ql=Symbol.for("react.strict_mode"),$o=Symbol.for("react.profiler"),Gc=Symbol.for("react.provider"),Yc=Symbol.for("react.context"),Kl=Symbol.for("react.forward_ref"),Oo=Symbol.for("react.suspense"),zo=Symbol.for("react.suspense_list"),Gl=Symbol.for("react.memo"),At=Symbol.for("react.lazy"),Jc=Symbol.for("react.offscreen"),Ya=Symbol.iterator;function ar(e){return e===null||typeof e!="object"?null:(e=Ya&&e[Ya]||e["@@iterator"],typeof e=="function"?e:null)}var G=Object.assign,eo;function _r(e){if(eo===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);eo=t&&t[1]||""}return`
`+eo+e}var to=!1;function no(e,t){if(!e||to)return"";to=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),s=r.stack.split(`
`),o=i.length-1,l=s.length-1;1<=o&&0<=l&&i[o]!==s[l];)l--;for(;1<=o&&0<=l;o--,l--)if(i[o]!==s[l]){if(o!==1||l!==1)do if(o--,l--,0>l||i[o]!==s[l]){var a=`
`+i[o].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=o&&0<=l);break}}}finally{to=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?_r(e):""}function cm(e){switch(e.tag){case 5:return _r(e.type);case 16:return _r("Lazy");case 13:return _r("Suspense");case 19:return _r("SuspenseList");case 0:case 2:case 15:return e=no(e.type,!1),e;case 11:return e=no(e.type.render,!1),e;case 1:return e=no(e.type,!0),e;default:return""}}function Do(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Tn:return"Fragment";case kn:return"Portal";case $o:return"Profiler";case Ql:return"StrictMode";case Oo:return"Suspense";case zo:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Yc:return(e.displayName||"Context")+".Consumer";case Gc:return(e._context.displayName||"Context")+".Provider";case Kl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Gl:return t=e.displayName||null,t!==null?t:Do(e.type)||"Memo";case At:t=e._payload,e=e._init;try{return Do(e(t))}catch{}}return null}function dm(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Do(t);case 8:return t===Ql?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Qt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Zc(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function fm(e){var t=Zc(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(o){r=""+o,s.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function hi(e){e._valueTracker||(e._valueTracker=fm(e))}function ed(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Zc(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Zi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ho(e,t){var n=t.checked;return G({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Ja(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Qt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function td(e,t){t=t.checked,t!=null&&Xl(e,"checked",t,!1)}function Fo(e,t){td(e,t);var n=Qt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Uo(e,t.type,n):t.hasOwnProperty("defaultValue")&&Uo(e,t.type,Qt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Za(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Uo(e,t,n){(t!=="number"||Zi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Er=Array.isArray;function zn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Qt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Vo(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(b(91));return G({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function eu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(b(92));if(Er(n)){if(1<n.length)throw Error(b(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Qt(n)}}function nd(e,t){var n=Qt(t.value),r=Qt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function tu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function rd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Bo(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?rd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var pi,id=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(pi=pi||document.createElement("div"),pi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=pi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Dr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var br={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},hm=["Webkit","ms","Moz","O"];Object.keys(br).forEach(function(e){hm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),br[t]=br[e]})});function sd(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||br.hasOwnProperty(e)&&br[e]?(""+t).trim():t+"px"}function od(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=sd(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var pm=G({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Wo(e,t){if(t){if(pm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(b(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(b(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(b(61))}if(t.style!=null&&typeof t.style!="object")throw Error(b(62))}}function qo(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Xo=null;function Yl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Qo=null,Dn=null,Hn=null;function nu(e){if(e=si(e)){if(typeof Qo!="function")throw Error(b(280));var t=e.stateNode;t&&(t=Is(t),Qo(e.stateNode,e.type,t))}}function ld(e){Dn?Hn?Hn.push(e):Hn=[e]:Dn=e}function ad(){if(Dn){var e=Dn,t=Hn;if(Hn=Dn=null,nu(e),t)for(e=0;e<t.length;e++)nu(t[e])}}function ud(e,t){return e(t)}function cd(){}var ro=!1;function dd(e,t,n){if(ro)return e(t,n);ro=!0;try{return ud(e,t,n)}finally{ro=!1,(Dn!==null||Hn!==null)&&(cd(),ad())}}function Hr(e,t){var n=e.stateNode;if(n===null)return null;var r=Is(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(b(231,t,typeof n));return n}var Ko=!1;if(kt)try{var ur={};Object.defineProperty(ur,"passive",{get:function(){Ko=!0}}),window.addEventListener("test",ur,ur),window.removeEventListener("test",ur,ur)}catch{Ko=!1}function mm(e,t,n,r,i,s,o,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Lr=!1,es=null,ts=!1,Go=null,gm={onError:function(e){Lr=!0,es=e}};function vm(e,t,n,r,i,s,o,l,a){Lr=!1,es=null,mm.apply(gm,arguments)}function ym(e,t,n,r,i,s,o,l,a){if(vm.apply(this,arguments),Lr){if(Lr){var u=es;Lr=!1,es=null}else throw Error(b(198));ts||(ts=!0,Go=u)}}function gn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function fd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function ru(e){if(gn(e)!==e)throw Error(b(188))}function wm(e){var t=e.alternate;if(!t){if(t=gn(e),t===null)throw Error(b(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return ru(i),e;if(s===r)return ru(i),t;s=s.sibling}throw Error(b(188))}if(n.return!==r.return)n=i,r=s;else{for(var o=!1,l=i.child;l;){if(l===n){o=!0,n=i,r=s;break}if(l===r){o=!0,r=i,n=s;break}l=l.sibling}if(!o){for(l=s.child;l;){if(l===n){o=!0,n=s,r=i;break}if(l===r){o=!0,r=s,n=i;break}l=l.sibling}if(!o)throw Error(b(189))}}if(n.alternate!==r)throw Error(b(190))}if(n.tag!==3)throw Error(b(188));return n.stateNode.current===n?e:t}function hd(e){return e=wm(e),e!==null?pd(e):null}function pd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=pd(e);if(t!==null)return t;e=e.sibling}return null}var md=He.unstable_scheduleCallback,iu=He.unstable_cancelCallback,xm=He.unstable_shouldYield,Sm=He.unstable_requestPaint,ee=He.unstable_now,_m=He.unstable_getCurrentPriorityLevel,Jl=He.unstable_ImmediatePriority,gd=He.unstable_UserBlockingPriority,ns=He.unstable_NormalPriority,Em=He.unstable_LowPriority,vd=He.unstable_IdlePriority,Ls=null,ft=null;function km(e){if(ft&&typeof ft.onCommitFiberRoot=="function")try{ft.onCommitFiberRoot(Ls,e,void 0,(e.current.flags&128)===128)}catch{}}var rt=Math.clz32?Math.clz32:Nm,Tm=Math.log,Cm=Math.LN2;function Nm(e){return e>>>=0,e===0?32:31-(Tm(e)/Cm|0)|0}var mi=64,gi=4194304;function kr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function rs(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,s=e.pingedLanes,o=n&268435455;if(o!==0){var l=o&~i;l!==0?r=kr(l):(s&=o,s!==0&&(r=kr(s)))}else o=n&~i,o!==0?r=kr(o):s!==0&&(r=kr(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,s=t&-t,i>=s||i===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-rt(t),i=1<<n,r|=e[n],t&=~i;return r}function bm(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Lm(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,s=e.pendingLanes;0<s;){var o=31-rt(s),l=1<<o,a=i[o];a===-1?(!(l&n)||l&r)&&(i[o]=bm(l,t)):a<=t&&(e.expiredLanes|=l),s&=~l}}function Yo(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function yd(){var e=mi;return mi<<=1,!(mi&4194240)&&(mi=64),e}function io(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ri(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-rt(t),e[t]=n}function Am(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-rt(n),s=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~s}}function Zl(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-rt(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var F=0;function wd(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var xd,ea,Sd,_d,Ed,Jo=!1,vi=[],Dt=null,Ht=null,Ft=null,Fr=new Map,Ur=new Map,Pt=[],Mm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function su(e,t){switch(e){case"focusin":case"focusout":Dt=null;break;case"dragenter":case"dragleave":Ht=null;break;case"mouseover":case"mouseout":Ft=null;break;case"pointerover":case"pointerout":Fr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ur.delete(t.pointerId)}}function cr(e,t,n,r,i,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},t!==null&&(t=si(t),t!==null&&ea(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function jm(e,t,n,r,i){switch(t){case"focusin":return Dt=cr(Dt,e,t,n,r,i),!0;case"dragenter":return Ht=cr(Ht,e,t,n,r,i),!0;case"mouseover":return Ft=cr(Ft,e,t,n,r,i),!0;case"pointerover":var s=i.pointerId;return Fr.set(s,cr(Fr.get(s)||null,e,t,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,Ur.set(s,cr(Ur.get(s)||null,e,t,n,r,i)),!0}return!1}function kd(e){var t=tn(e.target);if(t!==null){var n=gn(t);if(n!==null){if(t=n.tag,t===13){if(t=fd(n),t!==null){e.blockedOn=t,Ed(e.priority,function(){Sd(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ri(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Zo(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Xo=r,n.target.dispatchEvent(r),Xo=null}else return t=si(n),t!==null&&ea(t),e.blockedOn=n,!1;t.shift()}return!0}function ou(e,t,n){Ri(e)&&n.delete(t)}function Im(){Jo=!1,Dt!==null&&Ri(Dt)&&(Dt=null),Ht!==null&&Ri(Ht)&&(Ht=null),Ft!==null&&Ri(Ft)&&(Ft=null),Fr.forEach(ou),Ur.forEach(ou)}function dr(e,t){e.blockedOn===t&&(e.blockedOn=null,Jo||(Jo=!0,He.unstable_scheduleCallback(He.unstable_NormalPriority,Im)))}function Vr(e){function t(i){return dr(i,e)}if(0<vi.length){dr(vi[0],e);for(var n=1;n<vi.length;n++){var r=vi[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Dt!==null&&dr(Dt,e),Ht!==null&&dr(Ht,e),Ft!==null&&dr(Ft,e),Fr.forEach(t),Ur.forEach(t),n=0;n<Pt.length;n++)r=Pt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Pt.length&&(n=Pt[0],n.blockedOn===null);)kd(n),n.blockedOn===null&&Pt.shift()}var Fn=bt.ReactCurrentBatchConfig,is=!0;function Pm(e,t,n,r){var i=F,s=Fn.transition;Fn.transition=null;try{F=1,ta(e,t,n,r)}finally{F=i,Fn.transition=s}}function Rm(e,t,n,r){var i=F,s=Fn.transition;Fn.transition=null;try{F=4,ta(e,t,n,r)}finally{F=i,Fn.transition=s}}function ta(e,t,n,r){if(is){var i=Zo(e,t,n,r);if(i===null)mo(e,t,r,ss,n),su(e,r);else if(jm(i,e,t,n,r))r.stopPropagation();else if(su(e,r),t&4&&-1<Mm.indexOf(e)){for(;i!==null;){var s=si(i);if(s!==null&&xd(s),s=Zo(e,t,n,r),s===null&&mo(e,t,r,ss,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else mo(e,t,r,null,n)}}var ss=null;function Zo(e,t,n,r){if(ss=null,e=Yl(r),e=tn(e),e!==null)if(t=gn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=fd(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ss=e,null}function Td(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(_m()){case Jl:return 1;case gd:return 4;case ns:case Em:return 16;case vd:return 536870912;default:return 16}default:return 16}}var $t=null,na=null,$i=null;function Cd(){if($i)return $i;var e,t=na,n=t.length,r,i="value"in $t?$t.value:$t.textContent,s=i.length;for(e=0;e<n&&t[e]===i[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===i[s-r];r++);return $i=i.slice(e,1<r?1-r:void 0)}function Oi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function yi(){return!0}function lu(){return!1}function Ve(e){function t(n,r,i,s,o){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=o,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(s):s[l]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?yi:lu,this.isPropagationStopped=lu,this}return G(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=yi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=yi)},persist:function(){},isPersistent:yi}),t}var Zn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ra=Ve(Zn),ii=G({},Zn,{view:0,detail:0}),$m=Ve(ii),so,oo,fr,As=G({},ii,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ia,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==fr&&(fr&&e.type==="mousemove"?(so=e.screenX-fr.screenX,oo=e.screenY-fr.screenY):oo=so=0,fr=e),so)},movementY:function(e){return"movementY"in e?e.movementY:oo}}),au=Ve(As),Om=G({},As,{dataTransfer:0}),zm=Ve(Om),Dm=G({},ii,{relatedTarget:0}),lo=Ve(Dm),Hm=G({},Zn,{animationName:0,elapsedTime:0,pseudoElement:0}),Fm=Ve(Hm),Um=G({},Zn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Vm=Ve(Um),Bm=G({},Zn,{data:0}),uu=Ve(Bm),Wm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},qm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Xm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Qm(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Xm[e])?!!t[e]:!1}function ia(){return Qm}var Km=G({},ii,{key:function(e){if(e.key){var t=Wm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Oi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?qm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ia,charCode:function(e){return e.type==="keypress"?Oi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Oi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Gm=Ve(Km),Ym=G({},As,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),cu=Ve(Ym),Jm=G({},ii,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ia}),Zm=Ve(Jm),eg=G({},Zn,{propertyName:0,elapsedTime:0,pseudoElement:0}),tg=Ve(eg),ng=G({},As,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),rg=Ve(ng),ig=[9,13,27,32],sa=kt&&"CompositionEvent"in window,Ar=null;kt&&"documentMode"in document&&(Ar=document.documentMode);var sg=kt&&"TextEvent"in window&&!Ar,Nd=kt&&(!sa||Ar&&8<Ar&&11>=Ar),du=" ",fu=!1;function bd(e,t){switch(e){case"keyup":return ig.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ld(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Cn=!1;function og(e,t){switch(e){case"compositionend":return Ld(t);case"keypress":return t.which!==32?null:(fu=!0,du);case"textInput":return e=t.data,e===du&&fu?null:e;default:return null}}function lg(e,t){if(Cn)return e==="compositionend"||!sa&&bd(e,t)?(e=Cd(),$i=na=$t=null,Cn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Nd&&t.locale!=="ko"?null:t.data;default:return null}}var ag={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function hu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!ag[e.type]:t==="textarea"}function Ad(e,t,n,r){ld(r),t=os(t,"onChange"),0<t.length&&(n=new ra("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Mr=null,Br=null;function ug(e){Fd(e,0)}function Ms(e){var t=Ln(e);if(ed(t))return e}function cg(e,t){if(e==="change")return t}var Md=!1;if(kt){var ao;if(kt){var uo="oninput"in document;if(!uo){var pu=document.createElement("div");pu.setAttribute("oninput","return;"),uo=typeof pu.oninput=="function"}ao=uo}else ao=!1;Md=ao&&(!document.documentMode||9<document.documentMode)}function mu(){Mr&&(Mr.detachEvent("onpropertychange",jd),Br=Mr=null)}function jd(e){if(e.propertyName==="value"&&Ms(Br)){var t=[];Ad(t,Br,e,Yl(e)),dd(ug,t)}}function dg(e,t,n){e==="focusin"?(mu(),Mr=t,Br=n,Mr.attachEvent("onpropertychange",jd)):e==="focusout"&&mu()}function fg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ms(Br)}function hg(e,t){if(e==="click")return Ms(t)}function pg(e,t){if(e==="input"||e==="change")return Ms(t)}function mg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ot=typeof Object.is=="function"?Object.is:mg;function Wr(e,t){if(ot(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!Ro.call(t,i)||!ot(e[i],t[i]))return!1}return!0}function gu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function vu(e,t){var n=gu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=gu(n)}}function Id(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Id(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Pd(){for(var e=window,t=Zi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Zi(e.document)}return t}function oa(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function gg(e){var t=Pd(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Id(n.ownerDocument.documentElement,n)){if(r!==null&&oa(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!e.extend&&s>r&&(i=r,r=s,s=i),i=vu(n,s);var o=vu(n,r);i&&o&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var vg=kt&&"documentMode"in document&&11>=document.documentMode,Nn=null,el=null,jr=null,tl=!1;function yu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;tl||Nn==null||Nn!==Zi(r)||(r=Nn,"selectionStart"in r&&oa(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),jr&&Wr(jr,r)||(jr=r,r=os(el,"onSelect"),0<r.length&&(t=new ra("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Nn)))}function wi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var bn={animationend:wi("Animation","AnimationEnd"),animationiteration:wi("Animation","AnimationIteration"),animationstart:wi("Animation","AnimationStart"),transitionend:wi("Transition","TransitionEnd")},co={},Rd={};kt&&(Rd=document.createElement("div").style,"AnimationEvent"in window||(delete bn.animationend.animation,delete bn.animationiteration.animation,delete bn.animationstart.animation),"TransitionEvent"in window||delete bn.transitionend.transition);function js(e){if(co[e])return co[e];if(!bn[e])return e;var t=bn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Rd)return co[e]=t[n];return e}var $d=js("animationend"),Od=js("animationiteration"),zd=js("animationstart"),Dd=js("transitionend"),Hd=new Map,wu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Gt(e,t){Hd.set(e,t),mn(t,[e])}for(var fo=0;fo<wu.length;fo++){var ho=wu[fo],yg=ho.toLowerCase(),wg=ho[0].toUpperCase()+ho.slice(1);Gt(yg,"on"+wg)}Gt($d,"onAnimationEnd");Gt(Od,"onAnimationIteration");Gt(zd,"onAnimationStart");Gt("dblclick","onDoubleClick");Gt("focusin","onFocus");Gt("focusout","onBlur");Gt(Dd,"onTransitionEnd");Wn("onMouseEnter",["mouseout","mouseover"]);Wn("onMouseLeave",["mouseout","mouseover"]);Wn("onPointerEnter",["pointerout","pointerover"]);Wn("onPointerLeave",["pointerout","pointerover"]);mn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));mn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));mn("onBeforeInput",["compositionend","keypress","textInput","paste"]);mn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));mn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));mn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Tr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),xg=new Set("cancel close invalid load scroll toggle".split(" ").concat(Tr));function xu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,ym(r,t,void 0,e),e.currentTarget=null}function Fd(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var o=r.length-1;0<=o;o--){var l=r[o],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==s&&i.isPropagationStopped())break e;xu(i,l,u),s=a}else for(o=0;o<r.length;o++){if(l=r[o],a=l.instance,u=l.currentTarget,l=l.listener,a!==s&&i.isPropagationStopped())break e;xu(i,l,u),s=a}}}if(ts)throw e=Go,ts=!1,Go=null,e}function V(e,t){var n=t[ol];n===void 0&&(n=t[ol]=new Set);var r=e+"__bubble";n.has(r)||(Ud(t,e,2,!1),n.add(r))}function po(e,t,n){var r=0;t&&(r|=4),Ud(n,e,r,t)}var xi="_reactListening"+Math.random().toString(36).slice(2);function qr(e){if(!e[xi]){e[xi]=!0,Kc.forEach(function(n){n!=="selectionchange"&&(xg.has(n)||po(n,!1,e),po(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[xi]||(t[xi]=!0,po("selectionchange",!1,t))}}function Ud(e,t,n,r){switch(Td(t)){case 1:var i=Pm;break;case 4:i=Rm;break;default:i=ta}n=i.bind(null,t,n,e),i=void 0,!Ko||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function mo(e,t,n,r,i){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var l=r.stateNode.containerInfo;if(l===i||l.nodeType===8&&l.parentNode===i)break;if(o===4)for(o=r.return;o!==null;){var a=o.tag;if((a===3||a===4)&&(a=o.stateNode.containerInfo,a===i||a.nodeType===8&&a.parentNode===i))return;o=o.return}for(;l!==null;){if(o=tn(l),o===null)return;if(a=o.tag,a===5||a===6){r=s=o;continue e}l=l.parentNode}}r=r.return}dd(function(){var u=s,c=Yl(n),f=[];e:{var p=Hd.get(e);if(p!==void 0){var v=ra,y=e;switch(e){case"keypress":if(Oi(n)===0)break e;case"keydown":case"keyup":v=Gm;break;case"focusin":y="focus",v=lo;break;case"focusout":y="blur",v=lo;break;case"beforeblur":case"afterblur":v=lo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":v=au;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":v=zm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":v=Zm;break;case $d:case Od:case zd:v=Fm;break;case Dd:v=tg;break;case"scroll":v=$m;break;case"wheel":v=rg;break;case"copy":case"cut":case"paste":v=Vm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":v=cu}var w=(t&4)!==0,x=!w&&e==="scroll",h=w?p!==null?p+"Capture":null:p;w=[];for(var m=u,g;m!==null;){g=m;var _=g.stateNode;if(g.tag===5&&_!==null&&(g=_,h!==null&&(_=Hr(m,h),_!=null&&w.push(Xr(m,_,g)))),x)break;m=m.return}0<w.length&&(p=new v(p,y,null,n,c),f.push({event:p,listeners:w}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",v=e==="mouseout"||e==="pointerout",p&&n!==Xo&&(y=n.relatedTarget||n.fromElement)&&(tn(y)||y[Tt]))break e;if((v||p)&&(p=c.window===c?c:(p=c.ownerDocument)?p.defaultView||p.parentWindow:window,v?(y=n.relatedTarget||n.toElement,v=u,y=y?tn(y):null,y!==null&&(x=gn(y),y!==x||y.tag!==5&&y.tag!==6)&&(y=null)):(v=null,y=u),v!==y)){if(w=au,_="onMouseLeave",h="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(w=cu,_="onPointerLeave",h="onPointerEnter",m="pointer"),x=v==null?p:Ln(v),g=y==null?p:Ln(y),p=new w(_,m+"leave",v,n,c),p.target=x,p.relatedTarget=g,_=null,tn(c)===u&&(w=new w(h,m+"enter",y,n,c),w.target=g,w.relatedTarget=x,_=w),x=_,v&&y)t:{for(w=v,h=y,m=0,g=w;g;g=yn(g))m++;for(g=0,_=h;_;_=yn(_))g++;for(;0<m-g;)w=yn(w),m--;for(;0<g-m;)h=yn(h),g--;for(;m--;){if(w===h||h!==null&&w===h.alternate)break t;w=yn(w),h=yn(h)}w=null}else w=null;v!==null&&Su(f,p,v,w,!1),y!==null&&x!==null&&Su(f,x,y,w,!0)}}e:{if(p=u?Ln(u):window,v=p.nodeName&&p.nodeName.toLowerCase(),v==="select"||v==="input"&&p.type==="file")var N=cg;else if(hu(p))if(Md)N=pg;else{N=fg;var k=dg}else(v=p.nodeName)&&v.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(N=hg);if(N&&(N=N(e,u))){Ad(f,N,n,c);break e}k&&k(e,p,u),e==="focusout"&&(k=p._wrapperState)&&k.controlled&&p.type==="number"&&Uo(p,"number",p.value)}switch(k=u?Ln(u):window,e){case"focusin":(hu(k)||k.contentEditable==="true")&&(Nn=k,el=u,jr=null);break;case"focusout":jr=el=Nn=null;break;case"mousedown":tl=!0;break;case"contextmenu":case"mouseup":case"dragend":tl=!1,yu(f,n,c);break;case"selectionchange":if(vg)break;case"keydown":case"keyup":yu(f,n,c)}var C;if(sa)e:{switch(e){case"compositionstart":var E="onCompositionStart";break e;case"compositionend":E="onCompositionEnd";break e;case"compositionupdate":E="onCompositionUpdate";break e}E=void 0}else Cn?bd(e,n)&&(E="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(E="onCompositionStart");E&&(Nd&&n.locale!=="ko"&&(Cn||E!=="onCompositionStart"?E==="onCompositionEnd"&&Cn&&(C=Cd()):($t=c,na="value"in $t?$t.value:$t.textContent,Cn=!0)),k=os(u,E),0<k.length&&(E=new uu(E,e,null,n,c),f.push({event:E,listeners:k}),C?E.data=C:(C=Ld(n),C!==null&&(E.data=C)))),(C=sg?og(e,n):lg(e,n))&&(u=os(u,"onBeforeInput"),0<u.length&&(c=new uu("onBeforeInput","beforeinput",null,n,c),f.push({event:c,listeners:u}),c.data=C))}Fd(f,t)})}function Xr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function os(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=Hr(e,n),s!=null&&r.unshift(Xr(e,s,i)),s=Hr(e,t),s!=null&&r.push(Xr(e,s,i))),e=e.return}return r}function yn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Su(e,t,n,r,i){for(var s=t._reactName,o=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,i?(a=Hr(n,s),a!=null&&o.unshift(Xr(n,a,l))):i||(a=Hr(n,s),a!=null&&o.push(Xr(n,a,l)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Sg=/\r\n?/g,_g=/\u0000|\uFFFD/g;function _u(e){return(typeof e=="string"?e:""+e).replace(Sg,`
`).replace(_g,"")}function Si(e,t,n){if(t=_u(t),_u(e)!==t&&n)throw Error(b(425))}function ls(){}var nl=null,rl=null;function il(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var sl=typeof setTimeout=="function"?setTimeout:void 0,Eg=typeof clearTimeout=="function"?clearTimeout:void 0,Eu=typeof Promise=="function"?Promise:void 0,kg=typeof queueMicrotask=="function"?queueMicrotask:typeof Eu<"u"?function(e){return Eu.resolve(null).then(e).catch(Tg)}:sl;function Tg(e){setTimeout(function(){throw e})}function go(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Vr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Vr(t)}function Ut(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function ku(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var er=Math.random().toString(36).slice(2),dt="__reactFiber$"+er,Qr="__reactProps$"+er,Tt="__reactContainer$"+er,ol="__reactEvents$"+er,Cg="__reactListeners$"+er,Ng="__reactHandles$"+er;function tn(e){var t=e[dt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Tt]||n[dt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=ku(e);e!==null;){if(n=e[dt])return n;e=ku(e)}return t}e=n,n=e.parentNode}return null}function si(e){return e=e[dt]||e[Tt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Ln(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(b(33))}function Is(e){return e[Qr]||null}var ll=[],An=-1;function Yt(e){return{current:e}}function B(e){0>An||(e.current=ll[An],ll[An]=null,An--)}function U(e,t){An++,ll[An]=e.current,e.current=t}var Kt={},xe=Yt(Kt),Ie=Yt(!1),an=Kt;function qn(e,t){var n=e.type.contextTypes;if(!n)return Kt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Pe(e){return e=e.childContextTypes,e!=null}function as(){B(Ie),B(xe)}function Tu(e,t,n){if(xe.current!==Kt)throw Error(b(168));U(xe,t),U(Ie,n)}function Vd(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(b(108,dm(e)||"Unknown",i));return G({},n,r)}function us(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Kt,an=xe.current,U(xe,e),U(Ie,Ie.current),!0}function Cu(e,t,n){var r=e.stateNode;if(!r)throw Error(b(169));n?(e=Vd(e,t,an),r.__reactInternalMemoizedMergedChildContext=e,B(Ie),B(xe),U(xe,e)):B(Ie),U(Ie,n)}var xt=null,Ps=!1,vo=!1;function Bd(e){xt===null?xt=[e]:xt.push(e)}function bg(e){Ps=!0,Bd(e)}function Jt(){if(!vo&&xt!==null){vo=!0;var e=0,t=F;try{var n=xt;for(F=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}xt=null,Ps=!1}catch(i){throw xt!==null&&(xt=xt.slice(e+1)),md(Jl,Jt),i}finally{F=t,vo=!1}}return null}var Mn=[],jn=0,cs=null,ds=0,Be=[],We=0,un=null,St=1,_t="";function Zt(e,t){Mn[jn++]=ds,Mn[jn++]=cs,cs=e,ds=t}function Wd(e,t,n){Be[We++]=St,Be[We++]=_t,Be[We++]=un,un=e;var r=St;e=_t;var i=32-rt(r)-1;r&=~(1<<i),n+=1;var s=32-rt(t)+i;if(30<s){var o=i-i%5;s=(r&(1<<o)-1).toString(32),r>>=o,i-=o,St=1<<32-rt(t)+i|n<<i|r,_t=s+e}else St=1<<s|n<<i|r,_t=e}function la(e){e.return!==null&&(Zt(e,1),Wd(e,1,0))}function aa(e){for(;e===cs;)cs=Mn[--jn],Mn[jn]=null,ds=Mn[--jn],Mn[jn]=null;for(;e===un;)un=Be[--We],Be[We]=null,_t=Be[--We],Be[We]=null,St=Be[--We],Be[We]=null}var De=null,ze=null,X=!1,nt=null;function qd(e,t){var n=Xe(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Nu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,De=e,ze=Ut(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,De=e,ze=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=un!==null?{id:St,overflow:_t}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Xe(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,De=e,ze=null,!0):!1;default:return!1}}function al(e){return(e.mode&1)!==0&&(e.flags&128)===0}function ul(e){if(X){var t=ze;if(t){var n=t;if(!Nu(e,t)){if(al(e))throw Error(b(418));t=Ut(n.nextSibling);var r=De;t&&Nu(e,t)?qd(r,n):(e.flags=e.flags&-4097|2,X=!1,De=e)}}else{if(al(e))throw Error(b(418));e.flags=e.flags&-4097|2,X=!1,De=e}}}function bu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;De=e}function _i(e){if(e!==De)return!1;if(!X)return bu(e),X=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!il(e.type,e.memoizedProps)),t&&(t=ze)){if(al(e))throw Xd(),Error(b(418));for(;t;)qd(e,t),t=Ut(t.nextSibling)}if(bu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(b(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){ze=Ut(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}ze=null}}else ze=De?Ut(e.stateNode.nextSibling):null;return!0}function Xd(){for(var e=ze;e;)e=Ut(e.nextSibling)}function Xn(){ze=De=null,X=!1}function ua(e){nt===null?nt=[e]:nt.push(e)}var Lg=bt.ReactCurrentBatchConfig;function et(e,t){if(e&&e.defaultProps){t=G({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}var fs=Yt(null),hs=null,In=null,ca=null;function da(){ca=In=hs=null}function fa(e){var t=fs.current;B(fs),e._currentValue=t}function cl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Un(e,t){hs=e,ca=In=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(je=!0),e.firstContext=null)}function Ge(e){var t=e._currentValue;if(ca!==e)if(e={context:e,memoizedValue:t,next:null},In===null){if(hs===null)throw Error(b(308));In=e,hs.dependencies={lanes:0,firstContext:e}}else In=In.next=e;return t}var nn=null;function ha(e){nn===null?nn=[e]:nn.push(e)}function Qd(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,ha(t)):(n.next=i.next,i.next=n),t.interleaved=n,Ct(e,r)}function Ct(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Mt=!1;function pa(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Kd(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Et(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Vt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,D&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,Ct(e,n)}return i=r.interleaved,i===null?(t.next=t,ha(r)):(t.next=i.next,i.next=t),r.interleaved=t,Ct(e,n)}function zi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Zl(e,n)}}function Lu(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=o:s=s.next=o,n=n.next}while(n!==null);s===null?i=s=t:s=s.next=t}else i=s=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ps(e,t,n,r){var i=e.updateQueue;Mt=!1;var s=i.firstBaseUpdate,o=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var a=l,u=a.next;a.next=null,o===null?s=u:o.next=u,o=a;var c=e.alternate;c!==null&&(c=c.updateQueue,l=c.lastBaseUpdate,l!==o&&(l===null?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=a))}if(s!==null){var f=i.baseState;o=0,c=u=a=null,l=s;do{var p=l.lane,v=l.eventTime;if((r&p)===p){c!==null&&(c=c.next={eventTime:v,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var y=e,w=l;switch(p=t,v=n,w.tag){case 1:if(y=w.payload,typeof y=="function"){f=y.call(v,f,p);break e}f=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=w.payload,p=typeof y=="function"?y.call(v,f,p):y,p==null)break e;f=G({},f,p);break e;case 2:Mt=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,p=i.effects,p===null?i.effects=[l]:p.push(l))}else v={eventTime:v,lane:p,tag:l.tag,payload:l.payload,callback:l.callback,next:null},c===null?(u=c=v,a=f):c=c.next=v,o|=p;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;p=l,l=p.next,p.next=null,i.lastBaseUpdate=p,i.shared.pending=null}}while(!0);if(c===null&&(a=f),i.baseState=a,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do o|=i.lane,i=i.next;while(i!==t)}else s===null&&(i.shared.lanes=0);dn|=o,e.lanes=o,e.memoizedState=f}}function Au(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(b(191,i));i.call(r)}}}var Gd=new Qc.Component().refs;function dl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:G({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Rs={isMounted:function(e){return(e=e._reactInternals)?gn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ke(),i=Wt(e),s=Et(r,i);s.payload=t,n!=null&&(s.callback=n),t=Vt(e,s,i),t!==null&&(it(t,e,i,r),zi(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ke(),i=Wt(e),s=Et(r,i);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=Vt(e,s,i),t!==null&&(it(t,e,i,r),zi(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ke(),r=Wt(e),i=Et(n,r);i.tag=2,t!=null&&(i.callback=t),t=Vt(e,i,r),t!==null&&(it(t,e,r,n),zi(t,e,r))}};function Mu(e,t,n,r,i,s,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,o):t.prototype&&t.prototype.isPureReactComponent?!Wr(n,r)||!Wr(i,s):!0}function Yd(e,t,n){var r=!1,i=Kt,s=t.contextType;return typeof s=="object"&&s!==null?s=Ge(s):(i=Pe(t)?an:xe.current,r=t.contextTypes,s=(r=r!=null)?qn(e,i):Kt),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Rs,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=s),t}function ju(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Rs.enqueueReplaceState(t,t.state,null)}function fl(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs=Gd,pa(e);var s=t.contextType;typeof s=="object"&&s!==null?i.context=Ge(s):(s=Pe(t)?an:xe.current,i.context=qn(e,s)),i.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(dl(e,t,s,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Rs.enqueueReplaceState(i,i.state,null),ps(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function hr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(b(309));var r=n.stateNode}if(!r)throw Error(b(147,e));var i=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(o){var l=i.refs;l===Gd&&(l=i.refs={}),o===null?delete l[s]:l[s]=o},t._stringRef=s,t)}if(typeof e!="string")throw Error(b(284));if(!n._owner)throw Error(b(290,e))}return e}function Ei(e,t){throw e=Object.prototype.toString.call(t),Error(b(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Iu(e){var t=e._init;return t(e._payload)}function Jd(e){function t(h,m){if(e){var g=h.deletions;g===null?(h.deletions=[m],h.flags|=16):g.push(m)}}function n(h,m){if(!e)return null;for(;m!==null;)t(h,m),m=m.sibling;return null}function r(h,m){for(h=new Map;m!==null;)m.key!==null?h.set(m.key,m):h.set(m.index,m),m=m.sibling;return h}function i(h,m){return h=qt(h,m),h.index=0,h.sibling=null,h}function s(h,m,g){return h.index=g,e?(g=h.alternate,g!==null?(g=g.index,g<m?(h.flags|=2,m):g):(h.flags|=2,m)):(h.flags|=1048576,m)}function o(h){return e&&h.alternate===null&&(h.flags|=2),h}function l(h,m,g,_){return m===null||m.tag!==6?(m=ko(g,h.mode,_),m.return=h,m):(m=i(m,g),m.return=h,m)}function a(h,m,g,_){var N=g.type;return N===Tn?c(h,m,g.props.children,_,g.key):m!==null&&(m.elementType===N||typeof N=="object"&&N!==null&&N.$$typeof===At&&Iu(N)===m.type)?(_=i(m,g.props),_.ref=hr(h,m,g),_.return=h,_):(_=Bi(g.type,g.key,g.props,null,h.mode,_),_.ref=hr(h,m,g),_.return=h,_)}function u(h,m,g,_){return m===null||m.tag!==4||m.stateNode.containerInfo!==g.containerInfo||m.stateNode.implementation!==g.implementation?(m=To(g,h.mode,_),m.return=h,m):(m=i(m,g.children||[]),m.return=h,m)}function c(h,m,g,_,N){return m===null||m.tag!==7?(m=on(g,h.mode,_,N),m.return=h,m):(m=i(m,g),m.return=h,m)}function f(h,m,g){if(typeof m=="string"&&m!==""||typeof m=="number")return m=ko(""+m,h.mode,g),m.return=h,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case fi:return g=Bi(m.type,m.key,m.props,null,h.mode,g),g.ref=hr(h,null,m),g.return=h,g;case kn:return m=To(m,h.mode,g),m.return=h,m;case At:var _=m._init;return f(h,_(m._payload),g)}if(Er(m)||ar(m))return m=on(m,h.mode,g,null),m.return=h,m;Ei(h,m)}return null}function p(h,m,g,_){var N=m!==null?m.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return N!==null?null:l(h,m,""+g,_);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case fi:return g.key===N?a(h,m,g,_):null;case kn:return g.key===N?u(h,m,g,_):null;case At:return N=g._init,p(h,m,N(g._payload),_)}if(Er(g)||ar(g))return N!==null?null:c(h,m,g,_,null);Ei(h,g)}return null}function v(h,m,g,_,N){if(typeof _=="string"&&_!==""||typeof _=="number")return h=h.get(g)||null,l(m,h,""+_,N);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case fi:return h=h.get(_.key===null?g:_.key)||null,a(m,h,_,N);case kn:return h=h.get(_.key===null?g:_.key)||null,u(m,h,_,N);case At:var k=_._init;return v(h,m,g,k(_._payload),N)}if(Er(_)||ar(_))return h=h.get(g)||null,c(m,h,_,N,null);Ei(m,_)}return null}function y(h,m,g,_){for(var N=null,k=null,C=m,E=m=0,S=null;C!==null&&E<g.length;E++){C.index>E?(S=C,C=null):S=C.sibling;var A=p(h,C,g[E],_);if(A===null){C===null&&(C=S);break}e&&C&&A.alternate===null&&t(h,C),m=s(A,m,E),k===null?N=A:k.sibling=A,k=A,C=S}if(E===g.length)return n(h,C),X&&Zt(h,E),N;if(C===null){for(;E<g.length;E++)C=f(h,g[E],_),C!==null&&(m=s(C,m,E),k===null?N=C:k.sibling=C,k=C);return X&&Zt(h,E),N}for(C=r(h,C);E<g.length;E++)S=v(C,h,E,g[E],_),S!==null&&(e&&S.alternate!==null&&C.delete(S.key===null?E:S.key),m=s(S,m,E),k===null?N=S:k.sibling=S,k=S);return e&&C.forEach(function(P){return t(h,P)}),X&&Zt(h,E),N}function w(h,m,g,_){var N=ar(g);if(typeof N!="function")throw Error(b(150));if(g=N.call(g),g==null)throw Error(b(151));for(var k=N=null,C=m,E=m=0,S=null,A=g.next();C!==null&&!A.done;E++,A=g.next()){C.index>E?(S=C,C=null):S=C.sibling;var P=p(h,C,A.value,_);if(P===null){C===null&&(C=S);break}e&&C&&P.alternate===null&&t(h,C),m=s(P,m,E),k===null?N=P:k.sibling=P,k=P,C=S}if(A.done)return n(h,C),X&&Zt(h,E),N;if(C===null){for(;!A.done;E++,A=g.next())A=f(h,A.value,_),A!==null&&(m=s(A,m,E),k===null?N=A:k.sibling=A,k=A);return X&&Zt(h,E),N}for(C=r(h,C);!A.done;E++,A=g.next())A=v(C,h,E,A.value,_),A!==null&&(e&&A.alternate!==null&&C.delete(A.key===null?E:A.key),m=s(A,m,E),k===null?N=A:k.sibling=A,k=A);return e&&C.forEach(function(T){return t(h,T)}),X&&Zt(h,E),N}function x(h,m,g,_){if(typeof g=="object"&&g!==null&&g.type===Tn&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case fi:e:{for(var N=g.key,k=m;k!==null;){if(k.key===N){if(N=g.type,N===Tn){if(k.tag===7){n(h,k.sibling),m=i(k,g.props.children),m.return=h,h=m;break e}}else if(k.elementType===N||typeof N=="object"&&N!==null&&N.$$typeof===At&&Iu(N)===k.type){n(h,k.sibling),m=i(k,g.props),m.ref=hr(h,k,g),m.return=h,h=m;break e}n(h,k);break}else t(h,k);k=k.sibling}g.type===Tn?(m=on(g.props.children,h.mode,_,g.key),m.return=h,h=m):(_=Bi(g.type,g.key,g.props,null,h.mode,_),_.ref=hr(h,m,g),_.return=h,h=_)}return o(h);case kn:e:{for(k=g.key;m!==null;){if(m.key===k)if(m.tag===4&&m.stateNode.containerInfo===g.containerInfo&&m.stateNode.implementation===g.implementation){n(h,m.sibling),m=i(m,g.children||[]),m.return=h,h=m;break e}else{n(h,m);break}else t(h,m);m=m.sibling}m=To(g,h.mode,_),m.return=h,h=m}return o(h);case At:return k=g._init,x(h,m,k(g._payload),_)}if(Er(g))return y(h,m,g,_);if(ar(g))return w(h,m,g,_);Ei(h,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,m!==null&&m.tag===6?(n(h,m.sibling),m=i(m,g),m.return=h,h=m):(n(h,m),m=ko(g,h.mode,_),m.return=h,h=m),o(h)):n(h,m)}return x}var Qn=Jd(!0),Zd=Jd(!1),oi={},ht=Yt(oi),Kr=Yt(oi),Gr=Yt(oi);function rn(e){if(e===oi)throw Error(b(174));return e}function ma(e,t){switch(U(Gr,t),U(Kr,e),U(ht,oi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Bo(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Bo(t,e)}B(ht),U(ht,t)}function Kn(){B(ht),B(Kr),B(Gr)}function ef(e){rn(Gr.current);var t=rn(ht.current),n=Bo(t,e.type);t!==n&&(U(Kr,e),U(ht,n))}function ga(e){Kr.current===e&&(B(ht),B(Kr))}var Q=Yt(0);function ms(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var yo=[];function va(){for(var e=0;e<yo.length;e++)yo[e]._workInProgressVersionPrimary=null;yo.length=0}var Di=bt.ReactCurrentDispatcher,wo=bt.ReactCurrentBatchConfig,cn=0,K=null,oe=null,ce=null,gs=!1,Ir=!1,Yr=0,Ag=0;function ge(){throw Error(b(321))}function ya(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ot(e[n],t[n]))return!1;return!0}function wa(e,t,n,r,i,s){if(cn=s,K=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Di.current=e===null||e.memoizedState===null?Pg:Rg,e=n(r,i),Ir){s=0;do{if(Ir=!1,Yr=0,25<=s)throw Error(b(301));s+=1,ce=oe=null,t.updateQueue=null,Di.current=$g,e=n(r,i)}while(Ir)}if(Di.current=vs,t=oe!==null&&oe.next!==null,cn=0,ce=oe=K=null,gs=!1,t)throw Error(b(300));return e}function xa(){var e=Yr!==0;return Yr=0,e}function ct(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ce===null?K.memoizedState=ce=e:ce=ce.next=e,ce}function Ye(){if(oe===null){var e=K.alternate;e=e!==null?e.memoizedState:null}else e=oe.next;var t=ce===null?K.memoizedState:ce.next;if(t!==null)ce=t,oe=e;else{if(e===null)throw Error(b(310));oe=e,e={memoizedState:oe.memoizedState,baseState:oe.baseState,baseQueue:oe.baseQueue,queue:oe.queue,next:null},ce===null?K.memoizedState=ce=e:ce=ce.next=e}return ce}function Jr(e,t){return typeof t=="function"?t(e):t}function xo(e){var t=Ye(),n=t.queue;if(n===null)throw Error(b(311));n.lastRenderedReducer=e;var r=oe,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var o=i.next;i.next=s.next,s.next=o}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var l=o=null,a=null,u=s;do{var c=u.lane;if((cn&c)===c)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=f,o=r):a=a.next=f,K.lanes|=c,dn|=c}u=u.next}while(u!==null&&u!==s);a===null?o=r:a.next=l,ot(r,t.memoizedState)||(je=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do s=i.lane,K.lanes|=s,dn|=s,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function So(e){var t=Ye(),n=t.queue;if(n===null)throw Error(b(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,s=t.memoizedState;if(i!==null){n.pending=null;var o=i=i.next;do s=e(s,o.action),o=o.next;while(o!==i);ot(s,t.memoizedState)||(je=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function tf(){}function nf(e,t){var n=K,r=Ye(),i=t(),s=!ot(r.memoizedState,i);if(s&&(r.memoizedState=i,je=!0),r=r.queue,Sa(of.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||ce!==null&&ce.memoizedState.tag&1){if(n.flags|=2048,Zr(9,sf.bind(null,n,r,i,t),void 0,null),de===null)throw Error(b(349));cn&30||rf(n,t,i)}return i}function rf(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function sf(e,t,n,r){t.value=n,t.getSnapshot=r,lf(t)&&af(e)}function of(e,t,n){return n(function(){lf(t)&&af(e)})}function lf(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ot(e,n)}catch{return!0}}function af(e){var t=Ct(e,1);t!==null&&it(t,e,1,-1)}function Pu(e){var t=ct();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Jr,lastRenderedState:e},t.queue=e,e=e.dispatch=Ig.bind(null,K,e),[t.memoizedState,e]}function Zr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function uf(){return Ye().memoizedState}function Hi(e,t,n,r){var i=ct();K.flags|=e,i.memoizedState=Zr(1|t,n,void 0,r===void 0?null:r)}function $s(e,t,n,r){var i=Ye();r=r===void 0?null:r;var s=void 0;if(oe!==null){var o=oe.memoizedState;if(s=o.destroy,r!==null&&ya(r,o.deps)){i.memoizedState=Zr(t,n,s,r);return}}K.flags|=e,i.memoizedState=Zr(1|t,n,s,r)}function Ru(e,t){return Hi(8390656,8,e,t)}function Sa(e,t){return $s(2048,8,e,t)}function cf(e,t){return $s(4,2,e,t)}function df(e,t){return $s(4,4,e,t)}function ff(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function hf(e,t,n){return n=n!=null?n.concat([e]):null,$s(4,4,ff.bind(null,t,e),n)}function _a(){}function pf(e,t){var n=Ye();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ya(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function mf(e,t){var n=Ye();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ya(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function gf(e,t,n){return cn&21?(ot(n,t)||(n=yd(),K.lanes|=n,dn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,je=!0),e.memoizedState=n)}function Mg(e,t){var n=F;F=n!==0&&4>n?n:4,e(!0);var r=wo.transition;wo.transition={};try{e(!1),t()}finally{F=n,wo.transition=r}}function vf(){return Ye().memoizedState}function jg(e,t,n){var r=Wt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},yf(e))wf(t,n);else if(n=Qd(e,t,n,r),n!==null){var i=ke();it(n,e,r,i),xf(n,t,r)}}function Ig(e,t,n){var r=Wt(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(yf(e))wf(t,i);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var o=t.lastRenderedState,l=s(o,n);if(i.hasEagerState=!0,i.eagerState=l,ot(l,o)){var a=t.interleaved;a===null?(i.next=i,ha(t)):(i.next=a.next,a.next=i),t.interleaved=i;return}}catch{}finally{}n=Qd(e,t,i,r),n!==null&&(i=ke(),it(n,e,r,i),xf(n,t,r))}}function yf(e){var t=e.alternate;return e===K||t!==null&&t===K}function wf(e,t){Ir=gs=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function xf(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Zl(e,n)}}var vs={readContext:Ge,useCallback:ge,useContext:ge,useEffect:ge,useImperativeHandle:ge,useInsertionEffect:ge,useLayoutEffect:ge,useMemo:ge,useReducer:ge,useRef:ge,useState:ge,useDebugValue:ge,useDeferredValue:ge,useTransition:ge,useMutableSource:ge,useSyncExternalStore:ge,useId:ge,unstable_isNewReconciler:!1},Pg={readContext:Ge,useCallback:function(e,t){return ct().memoizedState=[e,t===void 0?null:t],e},useContext:Ge,useEffect:Ru,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Hi(4194308,4,ff.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Hi(4194308,4,e,t)},useInsertionEffect:function(e,t){return Hi(4,2,e,t)},useMemo:function(e,t){var n=ct();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=ct();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=jg.bind(null,K,e),[r.memoizedState,e]},useRef:function(e){var t=ct();return e={current:e},t.memoizedState=e},useState:Pu,useDebugValue:_a,useDeferredValue:function(e){return ct().memoizedState=e},useTransition:function(){var e=Pu(!1),t=e[0];return e=Mg.bind(null,e[1]),ct().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=K,i=ct();if(X){if(n===void 0)throw Error(b(407));n=n()}else{if(n=t(),de===null)throw Error(b(349));cn&30||rf(r,t,n)}i.memoizedState=n;var s={value:n,getSnapshot:t};return i.queue=s,Ru(of.bind(null,r,s,e),[e]),r.flags|=2048,Zr(9,sf.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=ct(),t=de.identifierPrefix;if(X){var n=_t,r=St;n=(r&~(1<<32-rt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Yr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Ag++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Rg={readContext:Ge,useCallback:pf,useContext:Ge,useEffect:Sa,useImperativeHandle:hf,useInsertionEffect:cf,useLayoutEffect:df,useMemo:mf,useReducer:xo,useRef:uf,useState:function(){return xo(Jr)},useDebugValue:_a,useDeferredValue:function(e){var t=Ye();return gf(t,oe.memoizedState,e)},useTransition:function(){var e=xo(Jr)[0],t=Ye().memoizedState;return[e,t]},useMutableSource:tf,useSyncExternalStore:nf,useId:vf,unstable_isNewReconciler:!1},$g={readContext:Ge,useCallback:pf,useContext:Ge,useEffect:Sa,useImperativeHandle:hf,useInsertionEffect:cf,useLayoutEffect:df,useMemo:mf,useReducer:So,useRef:uf,useState:function(){return So(Jr)},useDebugValue:_a,useDeferredValue:function(e){var t=Ye();return oe===null?t.memoizedState=e:gf(t,oe.memoizedState,e)},useTransition:function(){var e=So(Jr)[0],t=Ye().memoizedState;return[e,t]},useMutableSource:tf,useSyncExternalStore:nf,useId:vf,unstable_isNewReconciler:!1};function Gn(e,t){try{var n="",r=t;do n+=cm(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:i,digest:null}}function _o(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function hl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Og=typeof WeakMap=="function"?WeakMap:Map;function Sf(e,t,n){n=Et(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ws||(ws=!0,El=r),hl(e,t)},n}function _f(e,t,n){n=Et(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){hl(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){hl(e,t),typeof r!="function"&&(Bt===null?Bt=new Set([this]):Bt.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function $u(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Og;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=Yg.bind(null,e,t,n),t.then(e,e))}function Ou(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function zu(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Et(-1,1),t.tag=2,Vt(n,t,1))),n.lanes|=1),e)}var zg=bt.ReactCurrentOwner,je=!1;function Se(e,t,n,r){t.child=e===null?Zd(t,null,n,r):Qn(t,e.child,n,r)}function Du(e,t,n,r,i){n=n.render;var s=t.ref;return Un(t,i),r=wa(e,t,n,r,s,i),n=xa(),e!==null&&!je?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Nt(e,t,i)):(X&&n&&la(t),t.flags|=1,Se(e,t,r,i),t.child)}function Hu(e,t,n,r,i){if(e===null){var s=n.type;return typeof s=="function"&&!Aa(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,Ef(e,t,s,r,i)):(e=Bi(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&i)){var o=s.memoizedProps;if(n=n.compare,n=n!==null?n:Wr,n(o,r)&&e.ref===t.ref)return Nt(e,t,i)}return t.flags|=1,e=qt(s,r),e.ref=t.ref,e.return=t,t.child=e}function Ef(e,t,n,r,i){if(e!==null){var s=e.memoizedProps;if(Wr(s,r)&&e.ref===t.ref)if(je=!1,t.pendingProps=r=s,(e.lanes&i)!==0)e.flags&131072&&(je=!0);else return t.lanes=e.lanes,Nt(e,t,i)}return pl(e,t,n,r,i)}function kf(e,t,n){var r=t.pendingProps,i=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},U(Rn,Oe),Oe|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,U(Rn,Oe),Oe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,U(Rn,Oe),Oe|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,U(Rn,Oe),Oe|=r;return Se(e,t,i,n),t.child}function Tf(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function pl(e,t,n,r,i){var s=Pe(n)?an:xe.current;return s=qn(t,s),Un(t,i),n=wa(e,t,n,r,s,i),r=xa(),e!==null&&!je?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Nt(e,t,i)):(X&&r&&la(t),t.flags|=1,Se(e,t,n,i),t.child)}function Fu(e,t,n,r,i){if(Pe(n)){var s=!0;us(t)}else s=!1;if(Un(t,i),t.stateNode===null)Fi(e,t),Yd(t,n,r),fl(t,n,r,i),r=!0;else if(e===null){var o=t.stateNode,l=t.memoizedProps;o.props=l;var a=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=Ge(u):(u=Pe(n)?an:xe.current,u=qn(t,u));var c=n.getDerivedStateFromProps,f=typeof c=="function"||typeof o.getSnapshotBeforeUpdate=="function";f||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==r||a!==u)&&ju(t,o,r,u),Mt=!1;var p=t.memoizedState;o.state=p,ps(t,r,o,i),a=t.memoizedState,l!==r||p!==a||Ie.current||Mt?(typeof c=="function"&&(dl(t,n,c,r),a=t.memoizedState),(l=Mt||Mu(t,n,l,r,p,a,u))?(f||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),o.props=r,o.state=a,o.context=u,r=l):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Kd(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:et(t.type,l),o.props=u,f=t.pendingProps,p=o.context,a=n.contextType,typeof a=="object"&&a!==null?a=Ge(a):(a=Pe(n)?an:xe.current,a=qn(t,a));var v=n.getDerivedStateFromProps;(c=typeof v=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==f||p!==a)&&ju(t,o,r,a),Mt=!1,p=t.memoizedState,o.state=p,ps(t,r,o,i);var y=t.memoizedState;l!==f||p!==y||Ie.current||Mt?(typeof v=="function"&&(dl(t,n,v,r),y=t.memoizedState),(u=Mt||Mu(t,n,u,r,p,y,a)||!1)?(c||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,y,a),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,y,a)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),o.props=r,o.state=y,o.context=a,r=u):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return ml(e,t,n,r,s,i)}function ml(e,t,n,r,i,s){Tf(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return i&&Cu(t,n,!1),Nt(e,t,s);r=t.stateNode,zg.current=t;var l=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=Qn(t,e.child,null,s),t.child=Qn(t,null,l,s)):Se(e,t,l,s),t.memoizedState=r.state,i&&Cu(t,n,!0),t.child}function Cf(e){var t=e.stateNode;t.pendingContext?Tu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Tu(e,t.context,!1),ma(e,t.containerInfo)}function Uu(e,t,n,r,i){return Xn(),ua(i),t.flags|=256,Se(e,t,n,r),t.child}var gl={dehydrated:null,treeContext:null,retryLane:0};function vl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Nf(e,t,n){var r=t.pendingProps,i=Q.current,s=!1,o=(t.flags&128)!==0,l;if((l=o)||(l=e!==null&&e.memoizedState===null?!1:(i&2)!==0),l?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),U(Q,i&1),e===null)return ul(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,s?(r=t.mode,s=t.child,o={mode:"hidden",children:o},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=o):s=Ds(o,r,0,null),e=on(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=vl(n),t.memoizedState=gl,e):Ea(t,o));if(i=e.memoizedState,i!==null&&(l=i.dehydrated,l!==null))return Dg(e,t,o,r,l,i,n);if(s){s=r.fallback,o=t.mode,i=e.child,l=i.sibling;var a={mode:"hidden",children:r.children};return!(o&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=qt(i,a),r.subtreeFlags=i.subtreeFlags&14680064),l!==null?s=qt(l,s):(s=on(s,o,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,o=e.child.memoizedState,o=o===null?vl(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},s.memoizedState=o,s.childLanes=e.childLanes&~n,t.memoizedState=gl,r}return s=e.child,e=s.sibling,r=qt(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Ea(e,t){return t=Ds({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ki(e,t,n,r){return r!==null&&ua(r),Qn(t,e.child,null,n),e=Ea(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Dg(e,t,n,r,i,s,o){if(n)return t.flags&256?(t.flags&=-257,r=_o(Error(b(422))),ki(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,i=t.mode,r=Ds({mode:"visible",children:r.children},i,0,null),s=on(s,i,o,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&Qn(t,e.child,null,o),t.child.memoizedState=vl(o),t.memoizedState=gl,s);if(!(t.mode&1))return ki(e,t,o,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var l=r.dgst;return r=l,s=Error(b(419)),r=_o(s,r,void 0),ki(e,t,o,r)}if(l=(o&e.childLanes)!==0,je||l){if(r=de,r!==null){switch(o&-o){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|o)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,Ct(e,i),it(r,e,i,-1))}return La(),r=_o(Error(b(421))),ki(e,t,o,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=Jg.bind(null,e),i._reactRetry=t,null):(e=s.treeContext,ze=Ut(i.nextSibling),De=t,X=!0,nt=null,e!==null&&(Be[We++]=St,Be[We++]=_t,Be[We++]=un,St=e.id,_t=e.overflow,un=t),t=Ea(t,r.children),t.flags|=4096,t)}function Vu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),cl(e.return,t,n)}function Eo(e,t,n,r,i){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function bf(e,t,n){var r=t.pendingProps,i=r.revealOrder,s=r.tail;if(Se(e,t,r.children,n),r=Q.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Vu(e,n,t);else if(e.tag===19)Vu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(U(Q,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&ms(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Eo(t,!1,i,n,s);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&ms(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Eo(t,!0,n,null,s);break;case"together":Eo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Fi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Nt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),dn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(b(153));if(t.child!==null){for(e=t.child,n=qt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=qt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Hg(e,t,n){switch(t.tag){case 3:Cf(t),Xn();break;case 5:ef(t);break;case 1:Pe(t.type)&&us(t);break;case 4:ma(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;U(fs,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(U(Q,Q.current&1),t.flags|=128,null):n&t.child.childLanes?Nf(e,t,n):(U(Q,Q.current&1),e=Nt(e,t,n),e!==null?e.sibling:null);U(Q,Q.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return bf(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),U(Q,Q.current),r)break;return null;case 22:case 23:return t.lanes=0,kf(e,t,n)}return Nt(e,t,n)}var Lf,yl,Af,Mf;Lf=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};yl=function(){};Af=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,rn(ht.current);var s=null;switch(n){case"input":i=Ho(e,i),r=Ho(e,r),s=[];break;case"select":i=G({},i,{value:void 0}),r=G({},r,{value:void 0}),s=[];break;case"textarea":i=Vo(e,i),r=Vo(e,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ls)}Wo(n,r);var o;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var l=i[u];for(o in l)l.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(zr.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var a=r[u];if(l=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(o in l)!l.hasOwnProperty(o)||a&&a.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in a)a.hasOwnProperty(o)&&l[o]!==a[o]&&(n||(n={}),n[o]=a[o])}else n||(s||(s=[]),s.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(s=s||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(s=s||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(zr.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&V("scroll",e),s||l===a||(s=[])):(s=s||[]).push(u,a))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}};Mf=function(e,t,n,r){n!==r&&(t.flags|=4)};function pr(e,t){if(!X)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ve(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Fg(e,t,n){var r=t.pendingProps;switch(aa(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ve(t),null;case 1:return Pe(t.type)&&as(),ve(t),null;case 3:return r=t.stateNode,Kn(),B(Ie),B(xe),va(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(_i(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,nt!==null&&(Cl(nt),nt=null))),yl(e,t),ve(t),null;case 5:ga(t);var i=rn(Gr.current);if(n=t.type,e!==null&&t.stateNode!=null)Af(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(b(166));return ve(t),null}if(e=rn(ht.current),_i(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[dt]=t,r[Qr]=s,e=(t.mode&1)!==0,n){case"dialog":V("cancel",r),V("close",r);break;case"iframe":case"object":case"embed":V("load",r);break;case"video":case"audio":for(i=0;i<Tr.length;i++)V(Tr[i],r);break;case"source":V("error",r);break;case"img":case"image":case"link":V("error",r),V("load",r);break;case"details":V("toggle",r);break;case"input":Ja(r,s),V("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},V("invalid",r);break;case"textarea":eu(r,s),V("invalid",r)}Wo(n,s),i=null;for(var o in s)if(s.hasOwnProperty(o)){var l=s[o];o==="children"?typeof l=="string"?r.textContent!==l&&(s.suppressHydrationWarning!==!0&&Si(r.textContent,l,e),i=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(s.suppressHydrationWarning!==!0&&Si(r.textContent,l,e),i=["children",""+l]):zr.hasOwnProperty(o)&&l!=null&&o==="onScroll"&&V("scroll",r)}switch(n){case"input":hi(r),Za(r,s,!0);break;case"textarea":hi(r),tu(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=ls)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=rd(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[dt]=t,e[Qr]=r,Lf(e,t,!1,!1),t.stateNode=e;e:{switch(o=qo(n,r),n){case"dialog":V("cancel",e),V("close",e),i=r;break;case"iframe":case"object":case"embed":V("load",e),i=r;break;case"video":case"audio":for(i=0;i<Tr.length;i++)V(Tr[i],e);i=r;break;case"source":V("error",e),i=r;break;case"img":case"image":case"link":V("error",e),V("load",e),i=r;break;case"details":V("toggle",e),i=r;break;case"input":Ja(e,r),i=Ho(e,r),V("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=G({},r,{value:void 0}),V("invalid",e);break;case"textarea":eu(e,r),i=Vo(e,r),V("invalid",e);break;default:i=r}Wo(n,i),l=i;for(s in l)if(l.hasOwnProperty(s)){var a=l[s];s==="style"?od(e,a):s==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&id(e,a)):s==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Dr(e,a):typeof a=="number"&&Dr(e,""+a):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(zr.hasOwnProperty(s)?a!=null&&s==="onScroll"&&V("scroll",e):a!=null&&Xl(e,s,a,o))}switch(n){case"input":hi(e),Za(e,r,!1);break;case"textarea":hi(e),tu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Qt(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?zn(e,!!r.multiple,s,!1):r.defaultValue!=null&&zn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=ls)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ve(t),null;case 6:if(e&&t.stateNode!=null)Mf(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(b(166));if(n=rn(Gr.current),rn(ht.current),_i(t)){if(r=t.stateNode,n=t.memoizedProps,r[dt]=t,(s=r.nodeValue!==n)&&(e=De,e!==null))switch(e.tag){case 3:Si(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Si(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[dt]=t,t.stateNode=r}return ve(t),null;case 13:if(B(Q),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(X&&ze!==null&&t.mode&1&&!(t.flags&128))Xd(),Xn(),t.flags|=98560,s=!1;else if(s=_i(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(b(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(b(317));s[dt]=t}else Xn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ve(t),s=!1}else nt!==null&&(Cl(nt),nt=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||Q.current&1?le===0&&(le=3):La())),t.updateQueue!==null&&(t.flags|=4),ve(t),null);case 4:return Kn(),yl(e,t),e===null&&qr(t.stateNode.containerInfo),ve(t),null;case 10:return fa(t.type._context),ve(t),null;case 17:return Pe(t.type)&&as(),ve(t),null;case 19:if(B(Q),s=t.memoizedState,s===null)return ve(t),null;if(r=(t.flags&128)!==0,o=s.rendering,o===null)if(r)pr(s,!1);else{if(le!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=ms(e),o!==null){for(t.flags|=128,pr(s,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,o=s.alternate,o===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=o.childLanes,s.lanes=o.lanes,s.child=o.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=o.memoizedProps,s.memoizedState=o.memoizedState,s.updateQueue=o.updateQueue,s.type=o.type,e=o.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return U(Q,Q.current&1|2),t.child}e=e.sibling}s.tail!==null&&ee()>Yn&&(t.flags|=128,r=!0,pr(s,!1),t.lanes=4194304)}else{if(!r)if(e=ms(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),pr(s,!0),s.tail===null&&s.tailMode==="hidden"&&!o.alternate&&!X)return ve(t),null}else 2*ee()-s.renderingStartTime>Yn&&n!==1073741824&&(t.flags|=128,r=!0,pr(s,!1),t.lanes=4194304);s.isBackwards?(o.sibling=t.child,t.child=o):(n=s.last,n!==null?n.sibling=o:t.child=o,s.last=o)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=ee(),t.sibling=null,n=Q.current,U(Q,r?n&1|2:n&1),t):(ve(t),null);case 22:case 23:return ba(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Oe&1073741824&&(ve(t),t.subtreeFlags&6&&(t.flags|=8192)):ve(t),null;case 24:return null;case 25:return null}throw Error(b(156,t.tag))}function Ug(e,t){switch(aa(t),t.tag){case 1:return Pe(t.type)&&as(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Kn(),B(Ie),B(xe),va(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return ga(t),null;case 13:if(B(Q),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(b(340));Xn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return B(Q),null;case 4:return Kn(),null;case 10:return fa(t.type._context),null;case 22:case 23:return ba(),null;case 24:return null;default:return null}}var Ti=!1,we=!1,Vg=typeof WeakSet=="function"?WeakSet:Set,j=null;function Pn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){J(e,t,r)}else n.current=null}function wl(e,t,n){try{n()}catch(r){J(e,t,r)}}var Bu=!1;function Bg(e,t){if(nl=is,e=Pd(),oa(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var o=0,l=-1,a=-1,u=0,c=0,f=e,p=null;t:for(;;){for(var v;f!==n||i!==0&&f.nodeType!==3||(l=o+i),f!==s||r!==0&&f.nodeType!==3||(a=o+r),f.nodeType===3&&(o+=f.nodeValue.length),(v=f.firstChild)!==null;)p=f,f=v;for(;;){if(f===e)break t;if(p===n&&++u===i&&(l=o),p===s&&++c===r&&(a=o),(v=f.nextSibling)!==null)break;f=p,p=f.parentNode}f=v}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(rl={focusedElem:e,selectionRange:n},is=!1,j=t;j!==null;)if(t=j,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,j=e;else for(;j!==null;){t=j;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var w=y.memoizedProps,x=y.memoizedState,h=t.stateNode,m=h.getSnapshotBeforeUpdate(t.elementType===t.type?w:et(t.type,w),x);h.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(b(163))}}catch(_){J(t,t.return,_)}if(e=t.sibling,e!==null){e.return=t.return,j=e;break}j=t.return}return y=Bu,Bu=!1,y}function Pr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var s=i.destroy;i.destroy=void 0,s!==void 0&&wl(t,n,s)}i=i.next}while(i!==r)}}function Os(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function xl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function jf(e){var t=e.alternate;t!==null&&(e.alternate=null,jf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[dt],delete t[Qr],delete t[ol],delete t[Cg],delete t[Ng])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function If(e){return e.tag===5||e.tag===3||e.tag===4}function Wu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||If(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Sl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ls));else if(r!==4&&(e=e.child,e!==null))for(Sl(e,t,n),e=e.sibling;e!==null;)Sl(e,t,n),e=e.sibling}function _l(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(_l(e,t,n),e=e.sibling;e!==null;)_l(e,t,n),e=e.sibling}var fe=null,tt=!1;function Lt(e,t,n){for(n=n.child;n!==null;)Pf(e,t,n),n=n.sibling}function Pf(e,t,n){if(ft&&typeof ft.onCommitFiberUnmount=="function")try{ft.onCommitFiberUnmount(Ls,n)}catch{}switch(n.tag){case 5:we||Pn(n,t);case 6:var r=fe,i=tt;fe=null,Lt(e,t,n),fe=r,tt=i,fe!==null&&(tt?(e=fe,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):fe.removeChild(n.stateNode));break;case 18:fe!==null&&(tt?(e=fe,n=n.stateNode,e.nodeType===8?go(e.parentNode,n):e.nodeType===1&&go(e,n),Vr(e)):go(fe,n.stateNode));break;case 4:r=fe,i=tt,fe=n.stateNode.containerInfo,tt=!0,Lt(e,t,n),fe=r,tt=i;break;case 0:case 11:case 14:case 15:if(!we&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,o=s.destroy;s=s.tag,o!==void 0&&(s&2||s&4)&&wl(n,t,o),i=i.next}while(i!==r)}Lt(e,t,n);break;case 1:if(!we&&(Pn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){J(n,t,l)}Lt(e,t,n);break;case 21:Lt(e,t,n);break;case 22:n.mode&1?(we=(r=we)||n.memoizedState!==null,Lt(e,t,n),we=r):Lt(e,t,n);break;default:Lt(e,t,n)}}function qu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Vg),t.forEach(function(r){var i=Zg.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Ze(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=e,o=t,l=o;e:for(;l!==null;){switch(l.tag){case 5:fe=l.stateNode,tt=!1;break e;case 3:fe=l.stateNode.containerInfo,tt=!0;break e;case 4:fe=l.stateNode.containerInfo,tt=!0;break e}l=l.return}if(fe===null)throw Error(b(160));Pf(s,o,i),fe=null,tt=!1;var a=i.alternate;a!==null&&(a.return=null),i.return=null}catch(u){J(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Rf(t,e),t=t.sibling}function Rf(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ze(t,e),lt(e),r&4){try{Pr(3,e,e.return),Os(3,e)}catch(w){J(e,e.return,w)}try{Pr(5,e,e.return)}catch(w){J(e,e.return,w)}}break;case 1:Ze(t,e),lt(e),r&512&&n!==null&&Pn(n,n.return);break;case 5:if(Ze(t,e),lt(e),r&512&&n!==null&&Pn(n,n.return),e.flags&32){var i=e.stateNode;try{Dr(i,"")}catch(w){J(e,e.return,w)}}if(r&4&&(i=e.stateNode,i!=null)){var s=e.memoizedProps,o=n!==null?n.memoizedProps:s,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&s.type==="radio"&&s.name!=null&&td(i,s),qo(l,o);var u=qo(l,s);for(o=0;o<a.length;o+=2){var c=a[o],f=a[o+1];c==="style"?od(i,f):c==="dangerouslySetInnerHTML"?id(i,f):c==="children"?Dr(i,f):Xl(i,c,f,u)}switch(l){case"input":Fo(i,s);break;case"textarea":nd(i,s);break;case"select":var p=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var v=s.value;v!=null?zn(i,!!s.multiple,v,!1):p!==!!s.multiple&&(s.defaultValue!=null?zn(i,!!s.multiple,s.defaultValue,!0):zn(i,!!s.multiple,s.multiple?[]:"",!1))}i[Qr]=s}catch(w){J(e,e.return,w)}}break;case 6:if(Ze(t,e),lt(e),r&4){if(e.stateNode===null)throw Error(b(162));i=e.stateNode,s=e.memoizedProps;try{i.nodeValue=s}catch(w){J(e,e.return,w)}}break;case 3:if(Ze(t,e),lt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Vr(t.containerInfo)}catch(w){J(e,e.return,w)}break;case 4:Ze(t,e),lt(e);break;case 13:Ze(t,e),lt(e),i=e.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||(Ca=ee())),r&4&&qu(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(we=(u=we)||c,Ze(t,e),we=u):Ze(t,e),lt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(j=e,c=e.child;c!==null;){for(f=j=c;j!==null;){switch(p=j,v=p.child,p.tag){case 0:case 11:case 14:case 15:Pr(4,p,p.return);break;case 1:Pn(p,p.return);var y=p.stateNode;if(typeof y.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(w){J(r,n,w)}}break;case 5:Pn(p,p.return);break;case 22:if(p.memoizedState!==null){Qu(f);continue}}v!==null?(v.return=p,j=v):Qu(f)}c=c.sibling}e:for(c=null,f=e;;){if(f.tag===5){if(c===null){c=f;try{i=f.stateNode,u?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(l=f.stateNode,a=f.memoizedProps.style,o=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=sd("display",o))}catch(w){J(e,e.return,w)}}}else if(f.tag===6){if(c===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(w){J(e,e.return,w)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;c===f&&(c=null),f=f.return}c===f&&(c=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:Ze(t,e),lt(e),r&4&&qu(e);break;case 21:break;default:Ze(t,e),lt(e)}}function lt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(If(n)){var r=n;break e}n=n.return}throw Error(b(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Dr(i,""),r.flags&=-33);var s=Wu(e);_l(e,s,i);break;case 3:case 4:var o=r.stateNode.containerInfo,l=Wu(e);Sl(e,l,o);break;default:throw Error(b(161))}}catch(a){J(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Wg(e,t,n){j=e,$f(e)}function $f(e,t,n){for(var r=(e.mode&1)!==0;j!==null;){var i=j,s=i.child;if(i.tag===22&&r){var o=i.memoizedState!==null||Ti;if(!o){var l=i.alternate,a=l!==null&&l.memoizedState!==null||we;l=Ti;var u=we;if(Ti=o,(we=a)&&!u)for(j=i;j!==null;)o=j,a=o.child,o.tag===22&&o.memoizedState!==null?Ku(i):a!==null?(a.return=o,j=a):Ku(i);for(;s!==null;)j=s,$f(s),s=s.sibling;j=i,Ti=l,we=u}Xu(e)}else i.subtreeFlags&8772&&s!==null?(s.return=i,j=s):Xu(e)}}function Xu(e){for(;j!==null;){var t=j;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:we||Os(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!we)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:et(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&Au(t,s,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Au(t,o,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var f=c.dehydrated;f!==null&&Vr(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(b(163))}we||t.flags&512&&xl(t)}catch(p){J(t,t.return,p)}}if(t===e){j=null;break}if(n=t.sibling,n!==null){n.return=t.return,j=n;break}j=t.return}}function Qu(e){for(;j!==null;){var t=j;if(t===e){j=null;break}var n=t.sibling;if(n!==null){n.return=t.return,j=n;break}j=t.return}}function Ku(e){for(;j!==null;){var t=j;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Os(4,t)}catch(a){J(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(a){J(t,i,a)}}var s=t.return;try{xl(t)}catch(a){J(t,s,a)}break;case 5:var o=t.return;try{xl(t)}catch(a){J(t,o,a)}}}catch(a){J(t,t.return,a)}if(t===e){j=null;break}var l=t.sibling;if(l!==null){l.return=t.return,j=l;break}j=t.return}}var qg=Math.ceil,ys=bt.ReactCurrentDispatcher,ka=bt.ReactCurrentOwner,Ke=bt.ReactCurrentBatchConfig,D=0,de=null,ie=null,pe=0,Oe=0,Rn=Yt(0),le=0,ei=null,dn=0,zs=0,Ta=0,Rr=null,Le=null,Ca=0,Yn=1/0,yt=null,ws=!1,El=null,Bt=null,Ci=!1,Ot=null,xs=0,$r=0,kl=null,Ui=-1,Vi=0;function ke(){return D&6?ee():Ui!==-1?Ui:Ui=ee()}function Wt(e){return e.mode&1?D&2&&pe!==0?pe&-pe:Lg.transition!==null?(Vi===0&&(Vi=yd()),Vi):(e=F,e!==0||(e=window.event,e=e===void 0?16:Td(e.type)),e):1}function it(e,t,n,r){if(50<$r)throw $r=0,kl=null,Error(b(185));ri(e,n,r),(!(D&2)||e!==de)&&(e===de&&(!(D&2)&&(zs|=n),le===4&&Rt(e,pe)),Re(e,r),n===1&&D===0&&!(t.mode&1)&&(Yn=ee()+500,Ps&&Jt()))}function Re(e,t){var n=e.callbackNode;Lm(e,t);var r=rs(e,e===de?pe:0);if(r===0)n!==null&&iu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&iu(n),t===1)e.tag===0?bg(Gu.bind(null,e)):Bd(Gu.bind(null,e)),kg(function(){!(D&6)&&Jt()}),n=null;else{switch(wd(r)){case 1:n=Jl;break;case 4:n=gd;break;case 16:n=ns;break;case 536870912:n=vd;break;default:n=ns}n=Bf(n,Of.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Of(e,t){if(Ui=-1,Vi=0,D&6)throw Error(b(327));var n=e.callbackNode;if(Vn()&&e.callbackNode!==n)return null;var r=rs(e,e===de?pe:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ss(e,r);else{t=r;var i=D;D|=2;var s=Df();(de!==e||pe!==t)&&(yt=null,Yn=ee()+500,sn(e,t));do try{Kg();break}catch(l){zf(e,l)}while(!0);da(),ys.current=s,D=i,ie!==null?t=0:(de=null,pe=0,t=le)}if(t!==0){if(t===2&&(i=Yo(e),i!==0&&(r=i,t=Tl(e,i))),t===1)throw n=ei,sn(e,0),Rt(e,r),Re(e,ee()),n;if(t===6)Rt(e,r);else{if(i=e.current.alternate,!(r&30)&&!Xg(i)&&(t=Ss(e,r),t===2&&(s=Yo(e),s!==0&&(r=s,t=Tl(e,s))),t===1))throw n=ei,sn(e,0),Rt(e,r),Re(e,ee()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(b(345));case 2:en(e,Le,yt);break;case 3:if(Rt(e,r),(r&130023424)===r&&(t=Ca+500-ee(),10<t)){if(rs(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){ke(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=sl(en.bind(null,e,Le,yt),t);break}en(e,Le,yt);break;case 4:if(Rt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var o=31-rt(r);s=1<<o,o=t[o],o>i&&(i=o),r&=~s}if(r=i,r=ee()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*qg(r/1960))-r,10<r){e.timeoutHandle=sl(en.bind(null,e,Le,yt),r);break}en(e,Le,yt);break;case 5:en(e,Le,yt);break;default:throw Error(b(329))}}}return Re(e,ee()),e.callbackNode===n?Of.bind(null,e):null}function Tl(e,t){var n=Rr;return e.current.memoizedState.isDehydrated&&(sn(e,t).flags|=256),e=Ss(e,t),e!==2&&(t=Le,Le=n,t!==null&&Cl(t)),e}function Cl(e){Le===null?Le=e:Le.push.apply(Le,e)}function Xg(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!ot(s(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Rt(e,t){for(t&=~Ta,t&=~zs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-rt(t),r=1<<n;e[n]=-1,t&=~r}}function Gu(e){if(D&6)throw Error(b(327));Vn();var t=rs(e,0);if(!(t&1))return Re(e,ee()),null;var n=Ss(e,t);if(e.tag!==0&&n===2){var r=Yo(e);r!==0&&(t=r,n=Tl(e,r))}if(n===1)throw n=ei,sn(e,0),Rt(e,t),Re(e,ee()),n;if(n===6)throw Error(b(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,en(e,Le,yt),Re(e,ee()),null}function Na(e,t){var n=D;D|=1;try{return e(t)}finally{D=n,D===0&&(Yn=ee()+500,Ps&&Jt())}}function fn(e){Ot!==null&&Ot.tag===0&&!(D&6)&&Vn();var t=D;D|=1;var n=Ke.transition,r=F;try{if(Ke.transition=null,F=1,e)return e()}finally{F=r,Ke.transition=n,D=t,!(D&6)&&Jt()}}function ba(){Oe=Rn.current,B(Rn)}function sn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Eg(n)),ie!==null)for(n=ie.return;n!==null;){var r=n;switch(aa(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&as();break;case 3:Kn(),B(Ie),B(xe),va();break;case 5:ga(r);break;case 4:Kn();break;case 13:B(Q);break;case 19:B(Q);break;case 10:fa(r.type._context);break;case 22:case 23:ba()}n=n.return}if(de=e,ie=e=qt(e.current,null),pe=Oe=t,le=0,ei=null,Ta=zs=dn=0,Le=Rr=null,nn!==null){for(t=0;t<nn.length;t++)if(n=nn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var o=s.next;s.next=i,r.next=o}n.pending=r}nn=null}return e}function zf(e,t){do{var n=ie;try{if(da(),Di.current=vs,gs){for(var r=K.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}gs=!1}if(cn=0,ce=oe=K=null,Ir=!1,Yr=0,ka.current=null,n===null||n.return===null){le=1,ei=t,ie=null;break}e:{var s=e,o=n.return,l=n,a=t;if(t=pe,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,c=l,f=c.tag;if(!(c.mode&1)&&(f===0||f===11||f===15)){var p=c.alternate;p?(c.updateQueue=p.updateQueue,c.memoizedState=p.memoizedState,c.lanes=p.lanes):(c.updateQueue=null,c.memoizedState=null)}var v=Ou(o);if(v!==null){v.flags&=-257,zu(v,o,l,s,t),v.mode&1&&$u(s,u,t),t=v,a=u;var y=t.updateQueue;if(y===null){var w=new Set;w.add(a),t.updateQueue=w}else y.add(a);break e}else{if(!(t&1)){$u(s,u,t),La();break e}a=Error(b(426))}}else if(X&&l.mode&1){var x=Ou(o);if(x!==null){!(x.flags&65536)&&(x.flags|=256),zu(x,o,l,s,t),ua(Gn(a,l));break e}}s=a=Gn(a,l),le!==4&&(le=2),Rr===null?Rr=[s]:Rr.push(s),s=o;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var h=Sf(s,a,t);Lu(s,h);break e;case 1:l=a;var m=s.type,g=s.stateNode;if(!(s.flags&128)&&(typeof m.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(Bt===null||!Bt.has(g)))){s.flags|=65536,t&=-t,s.lanes|=t;var _=_f(s,l,t);Lu(s,_);break e}}s=s.return}while(s!==null)}Ff(n)}catch(N){t=N,ie===n&&n!==null&&(ie=n=n.return);continue}break}while(!0)}function Df(){var e=ys.current;return ys.current=vs,e===null?vs:e}function La(){(le===0||le===3||le===2)&&(le=4),de===null||!(dn&268435455)&&!(zs&268435455)||Rt(de,pe)}function Ss(e,t){var n=D;D|=2;var r=Df();(de!==e||pe!==t)&&(yt=null,sn(e,t));do try{Qg();break}catch(i){zf(e,i)}while(!0);if(da(),D=n,ys.current=r,ie!==null)throw Error(b(261));return de=null,pe=0,le}function Qg(){for(;ie!==null;)Hf(ie)}function Kg(){for(;ie!==null&&!xm();)Hf(ie)}function Hf(e){var t=Vf(e.alternate,e,Oe);e.memoizedProps=e.pendingProps,t===null?Ff(e):ie=t,ka.current=null}function Ff(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Ug(n,t),n!==null){n.flags&=32767,ie=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{le=6,ie=null;return}}else if(n=Fg(n,t,Oe),n!==null){ie=n;return}if(t=t.sibling,t!==null){ie=t;return}ie=t=e}while(t!==null);le===0&&(le=5)}function en(e,t,n){var r=F,i=Ke.transition;try{Ke.transition=null,F=1,Gg(e,t,n,r)}finally{Ke.transition=i,F=r}return null}function Gg(e,t,n,r){do Vn();while(Ot!==null);if(D&6)throw Error(b(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(b(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(Am(e,s),e===de&&(ie=de=null,pe=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ci||(Ci=!0,Bf(ns,function(){return Vn(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=Ke.transition,Ke.transition=null;var o=F;F=1;var l=D;D|=4,ka.current=null,Bg(e,n),Rf(n,e),gg(rl),is=!!nl,rl=nl=null,e.current=n,Wg(n),Sm(),D=l,F=o,Ke.transition=s}else e.current=n;if(Ci&&(Ci=!1,Ot=e,xs=i),s=e.pendingLanes,s===0&&(Bt=null),km(n.stateNode),Re(e,ee()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(ws)throw ws=!1,e=El,El=null,e;return xs&1&&e.tag!==0&&Vn(),s=e.pendingLanes,s&1?e===kl?$r++:($r=0,kl=e):$r=0,Jt(),null}function Vn(){if(Ot!==null){var e=wd(xs),t=Ke.transition,n=F;try{if(Ke.transition=null,F=16>e?16:e,Ot===null)var r=!1;else{if(e=Ot,Ot=null,xs=0,D&6)throw Error(b(331));var i=D;for(D|=4,j=e.current;j!==null;){var s=j,o=s.child;if(j.flags&16){var l=s.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(j=u;j!==null;){var c=j;switch(c.tag){case 0:case 11:case 15:Pr(8,c,s)}var f=c.child;if(f!==null)f.return=c,j=f;else for(;j!==null;){c=j;var p=c.sibling,v=c.return;if(jf(c),c===u){j=null;break}if(p!==null){p.return=v,j=p;break}j=v}}}var y=s.alternate;if(y!==null){var w=y.child;if(w!==null){y.child=null;do{var x=w.sibling;w.sibling=null,w=x}while(w!==null)}}j=s}}if(s.subtreeFlags&2064&&o!==null)o.return=s,j=o;else e:for(;j!==null;){if(s=j,s.flags&2048)switch(s.tag){case 0:case 11:case 15:Pr(9,s,s.return)}var h=s.sibling;if(h!==null){h.return=s.return,j=h;break e}j=s.return}}var m=e.current;for(j=m;j!==null;){o=j;var g=o.child;if(o.subtreeFlags&2064&&g!==null)g.return=o,j=g;else e:for(o=m;j!==null;){if(l=j,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Os(9,l)}}catch(N){J(l,l.return,N)}if(l===o){j=null;break e}var _=l.sibling;if(_!==null){_.return=l.return,j=_;break e}j=l.return}}if(D=i,Jt(),ft&&typeof ft.onPostCommitFiberRoot=="function")try{ft.onPostCommitFiberRoot(Ls,e)}catch{}r=!0}return r}finally{F=n,Ke.transition=t}}return!1}function Yu(e,t,n){t=Gn(n,t),t=Sf(e,t,1),e=Vt(e,t,1),t=ke(),e!==null&&(ri(e,1,t),Re(e,t))}function J(e,t,n){if(e.tag===3)Yu(e,e,n);else for(;t!==null;){if(t.tag===3){Yu(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Bt===null||!Bt.has(r))){e=Gn(n,e),e=_f(t,e,1),t=Vt(t,e,1),e=ke(),t!==null&&(ri(t,1,e),Re(t,e));break}}t=t.return}}function Yg(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ke(),e.pingedLanes|=e.suspendedLanes&n,de===e&&(pe&n)===n&&(le===4||le===3&&(pe&130023424)===pe&&500>ee()-Ca?sn(e,0):Ta|=n),Re(e,t)}function Uf(e,t){t===0&&(e.mode&1?(t=gi,gi<<=1,!(gi&130023424)&&(gi=4194304)):t=1);var n=ke();e=Ct(e,t),e!==null&&(ri(e,t,n),Re(e,n))}function Jg(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Uf(e,n)}function Zg(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(b(314))}r!==null&&r.delete(t),Uf(e,n)}var Vf;Vf=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ie.current)je=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return je=!1,Hg(e,t,n);je=!!(e.flags&131072)}else je=!1,X&&t.flags&1048576&&Wd(t,ds,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Fi(e,t),e=t.pendingProps;var i=qn(t,xe.current);Un(t,n),i=wa(null,t,r,e,i,n);var s=xa();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Pe(r)?(s=!0,us(t)):s=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,pa(t),i.updater=Rs,t.stateNode=i,i._reactInternals=t,fl(t,r,e,n),t=ml(null,t,r,!0,s,n)):(t.tag=0,X&&s&&la(t),Se(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Fi(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=t0(r),e=et(r,e),i){case 0:t=pl(null,t,r,e,n);break e;case 1:t=Fu(null,t,r,e,n);break e;case 11:t=Du(null,t,r,e,n);break e;case 14:t=Hu(null,t,r,et(r.type,e),n);break e}throw Error(b(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:et(r,i),pl(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:et(r,i),Fu(e,t,r,i,n);case 3:e:{if(Cf(t),e===null)throw Error(b(387));r=t.pendingProps,s=t.memoizedState,i=s.element,Kd(e,t),ps(t,r,null,n);var o=t.memoizedState;if(r=o.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){i=Gn(Error(b(423)),t),t=Uu(e,t,r,n,i);break e}else if(r!==i){i=Gn(Error(b(424)),t),t=Uu(e,t,r,n,i);break e}else for(ze=Ut(t.stateNode.containerInfo.firstChild),De=t,X=!0,nt=null,n=Zd(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Xn(),r===i){t=Nt(e,t,n);break e}Se(e,t,r,n)}t=t.child}return t;case 5:return ef(t),e===null&&ul(t),r=t.type,i=t.pendingProps,s=e!==null?e.memoizedProps:null,o=i.children,il(r,i)?o=null:s!==null&&il(r,s)&&(t.flags|=32),Tf(e,t),Se(e,t,o,n),t.child;case 6:return e===null&&ul(t),null;case 13:return Nf(e,t,n);case 4:return ma(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Qn(t,null,r,n):Se(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:et(r,i),Du(e,t,r,i,n);case 7:return Se(e,t,t.pendingProps,n),t.child;case 8:return Se(e,t,t.pendingProps.children,n),t.child;case 12:return Se(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,s=t.memoizedProps,o=i.value,U(fs,r._currentValue),r._currentValue=o,s!==null)if(ot(s.value,o)){if(s.children===i.children&&!Ie.current){t=Nt(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var l=s.dependencies;if(l!==null){o=s.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(s.tag===1){a=Et(-1,n&-n),a.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?a.next=a:(a.next=c.next,c.next=a),u.pending=a}}s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),cl(s.return,n,t),l.lanes|=n;break}a=a.next}}else if(s.tag===10)o=s.type===t.type?null:s.child;else if(s.tag===18){if(o=s.return,o===null)throw Error(b(341));o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),cl(o,n,t),o=s.sibling}else o=s.child;if(o!==null)o.return=s;else for(o=s;o!==null;){if(o===t){o=null;break}if(s=o.sibling,s!==null){s.return=o.return,o=s;break}o=o.return}s=o}Se(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Un(t,n),i=Ge(i),r=r(i),t.flags|=1,Se(e,t,r,n),t.child;case 14:return r=t.type,i=et(r,t.pendingProps),i=et(r.type,i),Hu(e,t,r,i,n);case 15:return Ef(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:et(r,i),Fi(e,t),t.tag=1,Pe(r)?(e=!0,us(t)):e=!1,Un(t,n),Yd(t,r,i),fl(t,r,i,n),ml(null,t,r,!0,e,n);case 19:return bf(e,t,n);case 22:return kf(e,t,n)}throw Error(b(156,t.tag))};function Bf(e,t){return md(e,t)}function e0(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Xe(e,t,n,r){return new e0(e,t,n,r)}function Aa(e){return e=e.prototype,!(!e||!e.isReactComponent)}function t0(e){if(typeof e=="function")return Aa(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Kl)return 11;if(e===Gl)return 14}return 2}function qt(e,t){var n=e.alternate;return n===null?(n=Xe(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Bi(e,t,n,r,i,s){var o=2;if(r=e,typeof e=="function")Aa(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case Tn:return on(n.children,i,s,t);case Ql:o=8,i|=8;break;case $o:return e=Xe(12,n,t,i|2),e.elementType=$o,e.lanes=s,e;case Oo:return e=Xe(13,n,t,i),e.elementType=Oo,e.lanes=s,e;case zo:return e=Xe(19,n,t,i),e.elementType=zo,e.lanes=s,e;case Jc:return Ds(n,i,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Gc:o=10;break e;case Yc:o=9;break e;case Kl:o=11;break e;case Gl:o=14;break e;case At:o=16,r=null;break e}throw Error(b(130,e==null?e:typeof e,""))}return t=Xe(o,n,t,i),t.elementType=e,t.type=r,t.lanes=s,t}function on(e,t,n,r){return e=Xe(7,e,r,t),e.lanes=n,e}function Ds(e,t,n,r){return e=Xe(22,e,r,t),e.elementType=Jc,e.lanes=n,e.stateNode={isHidden:!1},e}function ko(e,t,n){return e=Xe(6,e,null,t),e.lanes=n,e}function To(e,t,n){return t=Xe(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function n0(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=io(0),this.expirationTimes=io(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=io(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Ma(e,t,n,r,i,s,o,l,a){return e=new n0(e,t,n,l,a),t===1?(t=1,s===!0&&(t|=8)):t=0,s=Xe(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},pa(s),e}function r0(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:kn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Wf(e){if(!e)return Kt;e=e._reactInternals;e:{if(gn(e)!==e||e.tag!==1)throw Error(b(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Pe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(b(171))}if(e.tag===1){var n=e.type;if(Pe(n))return Vd(e,n,t)}return t}function qf(e,t,n,r,i,s,o,l,a){return e=Ma(n,r,!0,e,i,s,o,l,a),e.context=Wf(null),n=e.current,r=ke(),i=Wt(n),s=Et(r,i),s.callback=t??null,Vt(n,s,i),e.current.lanes=i,ri(e,i,r),Re(e,r),e}function Hs(e,t,n,r){var i=t.current,s=ke(),o=Wt(i);return n=Wf(n),t.context===null?t.context=n:t.pendingContext=n,t=Et(s,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Vt(i,t,o),e!==null&&(it(e,i,o,s),zi(e,i,o)),o}function _s(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Ju(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ja(e,t){Ju(e,t),(e=e.alternate)&&Ju(e,t)}function i0(){return null}var Xf=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ia(e){this._internalRoot=e}Fs.prototype.render=Ia.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(b(409));Hs(e,t,null,null)};Fs.prototype.unmount=Ia.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;fn(function(){Hs(null,e,null,null)}),t[Tt]=null}};function Fs(e){this._internalRoot=e}Fs.prototype.unstable_scheduleHydration=function(e){if(e){var t=_d();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Pt.length&&t!==0&&t<Pt[n].priority;n++);Pt.splice(n,0,e),n===0&&kd(e)}};function Pa(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Us(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Zu(){}function s0(e,t,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var u=_s(o);s.call(u)}}var o=qf(t,r,e,0,null,!1,!1,"",Zu);return e._reactRootContainer=o,e[Tt]=o.current,qr(e.nodeType===8?e.parentNode:e),fn(),o}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var l=r;r=function(){var u=_s(a);l.call(u)}}var a=Ma(e,0,!1,null,null,!1,!1,"",Zu);return e._reactRootContainer=a,e[Tt]=a.current,qr(e.nodeType===8?e.parentNode:e),fn(function(){Hs(t,a,n,r)}),a}function Vs(e,t,n,r,i){var s=n._reactRootContainer;if(s){var o=s;if(typeof i=="function"){var l=i;i=function(){var a=_s(o);l.call(a)}}Hs(t,o,e,i)}else o=s0(n,t,e,i,r);return _s(o)}xd=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=kr(t.pendingLanes);n!==0&&(Zl(t,n|1),Re(t,ee()),!(D&6)&&(Yn=ee()+500,Jt()))}break;case 13:fn(function(){var r=Ct(e,1);if(r!==null){var i=ke();it(r,e,1,i)}}),ja(e,1)}};ea=function(e){if(e.tag===13){var t=Ct(e,134217728);if(t!==null){var n=ke();it(t,e,134217728,n)}ja(e,134217728)}};Sd=function(e){if(e.tag===13){var t=Wt(e),n=Ct(e,t);if(n!==null){var r=ke();it(n,e,t,r)}ja(e,t)}};_d=function(){return F};Ed=function(e,t){var n=F;try{return F=e,t()}finally{F=n}};Qo=function(e,t,n){switch(t){case"input":if(Fo(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Is(r);if(!i)throw Error(b(90));ed(r),Fo(r,i)}}}break;case"textarea":nd(e,n);break;case"select":t=n.value,t!=null&&zn(e,!!n.multiple,t,!1)}};ud=Na;cd=fn;var o0={usingClientEntryPoint:!1,Events:[si,Ln,Is,ld,ad,Na]},mr={findFiberByHostInstance:tn,bundleType:0,version:"18.2.0",rendererPackageName:"react-dom"},l0={bundleType:mr.bundleType,version:mr.version,rendererPackageName:mr.rendererPackageName,rendererConfig:mr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:bt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=hd(e),e===null?null:e.stateNode},findFiberByHostInstance:mr.findFiberByHostInstance||i0,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.2.0-next-9e3b772b8-20220608"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ni=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ni.isDisabled&&Ni.supportsFiber)try{Ls=Ni.inject(l0),ft=Ni}catch{}}Ue.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=o0;Ue.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Pa(t))throw Error(b(200));return r0(e,t,null,n)};Ue.createRoot=function(e,t){if(!Pa(e))throw Error(b(299));var n=!1,r="",i=Xf;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=Ma(e,1,!1,null,null,n,!1,r,i),e[Tt]=t.current,qr(e.nodeType===8?e.parentNode:e),new Ia(t)};Ue.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(b(188)):(e=Object.keys(e).join(","),Error(b(268,e)));return e=hd(t),e=e===null?null:e.stateNode,e};Ue.flushSync=function(e){return fn(e)};Ue.hydrate=function(e,t,n){if(!Us(t))throw Error(b(200));return Vs(null,e,t,!0,n)};Ue.hydrateRoot=function(e,t,n){if(!Pa(e))throw Error(b(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",o=Xf;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=qf(t,null,e,1,n??null,i,!1,s,o),e[Tt]=t.current,qr(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Fs(t)};Ue.render=function(e,t,n){if(!Us(t))throw Error(b(200));return Vs(null,e,t,!1,n)};Ue.unmountComponentAtNode=function(e){if(!Us(e))throw Error(b(40));return e._reactRootContainer?(fn(function(){Vs(null,null,e,!1,function(){e._reactRootContainer=null,e[Tt]=null})}),!0):!1};Ue.unstable_batchedUpdates=Na;Ue.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Us(n))throw Error(b(200));if(e==null||e._reactInternals===void 0)throw Error(b(38));return Vs(e,t,n,!1,r)};Ue.version="18.2.0-next-9e3b772b8-20220608";function Qf(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Qf)}catch(e){console.error(e)}}Qf(),Wc.exports=Ue;var Hy=Wc.exports;const Bn=({children:e,title:t="",icon:n,disabled:r=!1,toggled:i=!1,onClick:s=()=>{},style:o})=>{let l=`toolbar-button ${n}`;return i&&(l+=" toggled"),d.jsxs("button",{className:l,onMouseDown:ec,onClick:s,onDoubleClick:ec,title:t,disabled:!!r,style:o,children:[n&&d.jsx("span",{className:`codicon codicon-${n}`,style:e?{marginRight:5}:{}}),e]})},ec=e=>{e.stopPropagation(),e.preventDefault()},Wi=Symbol("context"),Kf=Symbol("next"),Gf=Symbol("prev"),tc=Symbol("events");class Fy{constructor(t){Z(this,"startTime");Z(this,"endTime");Z(this,"browserName");Z(this,"channel");Z(this,"platform");Z(this,"wallTime");Z(this,"title");Z(this,"options");Z(this,"pages");Z(this,"actions");Z(this,"events");Z(this,"stdio");Z(this,"errors");Z(this,"errorDescriptors");Z(this,"hasSource");Z(this,"hasStepData");Z(this,"sdkLanguage");Z(this,"testIdAttributeName");Z(this,"sources");Z(this,"resources");t.forEach(r=>a0(r));const n=t.find(r=>r.isPrimary);this.browserName=(n==null?void 0:n.browserName)||"",this.sdkLanguage=n==null?void 0:n.sdkLanguage,this.channel=n==null?void 0:n.channel,this.testIdAttributeName=n==null?void 0:n.testIdAttributeName,this.platform=(n==null?void 0:n.platform)||"",this.title=(n==null?void 0:n.title)||"",this.options=(n==null?void 0:n.options)||{},this.wallTime=t.map(r=>r.wallTime).reduce((r,i)=>Math.min(r||Number.MAX_VALUE,i),Number.MAX_VALUE),this.startTime=t.map(r=>r.startTime).reduce((r,i)=>Math.min(r,i),Number.MAX_VALUE),this.endTime=t.map(r=>r.endTime).reduce((r,i)=>Math.max(r,i),Number.MIN_VALUE),this.pages=[].concat(...t.map(r=>r.pages)),this.actions=u0(t),this.events=[].concat(...t.map(r=>r.events)),this.stdio=[].concat(...t.map(r=>r.stdio)),this.errors=[].concat(...t.map(r=>r.errors)),this.hasSource=t.some(r=>r.hasSource),this.hasStepData=t.some(r=>!r.isPrimary),this.resources=[...t.map(r=>r.resources)].flat(),this.events.sort((r,i)=>r.time-i.time),this.resources.sort((r,i)=>r._monotonicTime-i._monotonicTime),this.errorDescriptors=this.hasStepData?this._errorDescriptorsFromTestRunner():this._errorDescriptorsFromActions(),this.sources=m0(this.actions,this.errorDescriptors)}failedAction(){return this.actions.findLast(t=>t.error)}_errorDescriptorsFromActions(){var n;const t=[];for(const r of this.actions||[])(n=r.error)!=null&&n.message&&t.push({action:r,stack:r.stack,message:r.error.message});return t}_errorDescriptorsFromTestRunner(){const t=[];for(const n of this.errors||[])n.message&&t.push({stack:n.stack,message:n.message});return t}}function a0(e){for(const n of e.pages)n[Wi]=e;for(let n=0;n<e.actions.length;++n){const r=e.actions[n];r[Wi]=e}let t;for(let n=e.actions.length-1;n>=0;n--){const r=e.actions[n];r[Kf]=t,r.apiName.includes("route.")||(t=r)}for(const n of e.events)n[Wi]=e}function u0(e){const t=new Map;let n=0;const r=e.filter(l=>l.isPrimary),i=e.filter(l=>!l.isPrimary);for(const l of r){for(const a of l.actions)t.set(`${a.apiName}@${a.wallTime}`,{...a,context:l});!n&&l.actions.length&&(n=l.actions[0].startTime-l.actions[0].wallTime)}const s=new Map;for(const l of i)for(const a of l.actions){if(n){const f=a.endTime-a.startTime;a.startTime&&(a.startTime=a.wallTime+n),a.endTime&&(a.endTime=a.startTime+f)}const u=`${a.apiName}@${a.wallTime}`,c=t.get(u);if(c&&c.apiName===a.apiName){s.set(a.callId,c.callId),a.error&&(c.error=a.error),a.attachments&&(c.attachments=a.attachments),a.parentId&&(c.parentId=s.get(a.parentId)??a.parentId);continue}a.parentId&&(a.parentId=s.get(a.parentId)??a.parentId),t.set(u,{...a,context:l})}const o=[...t.values()];o.sort((l,a)=>a.parentId===l.callId?-1:l.parentId===a.callId?1:l.wallTime-a.wallTime||l.startTime-a.startTime);for(let l=1;l<o.length;++l)o[l][Gf]=o[l-1];return o}function c0(e){const t=new Map;for(const r of e)t.set(r.callId,{id:r.callId,parent:void 0,children:[],action:r});const n={id:"",parent:void 0,children:[]};for(const r of t.values()){const i=r.action.parentId&&t.get(r.action.parentId)||n;i.children.push(r),r.parent=i}return{rootItem:n,itemMap:t}}function Uy(e){return`${e.pageId||"none"}:${e.callId}`}function Nl(e){return e[Wi]}function d0(e){return e[Kf]}function f0(e){return e[Gf]}function h0(e){let t=0,n=0;for(const r of p0(e)){if(r.type==="console"){const i=r.messageType;i==="warning"?++n:i==="error"&&++t}r.type==="event"&&r.method==="pageError"&&++t}return{errors:t,warnings:n}}function p0(e){let t=e[tc];if(t)return t;const n=d0(e);return t=Nl(e).events.filter(r=>r.time>=e.startTime&&(!n||r.time<n.startTime)),e[tc]=t,t}function m0(e,t){var r;const n=new Map;for(const i of e)for(const s of i.stack||[]){let o=n.get(s.file);o||(o={errors:[],content:void 0},n.set(s.file,o))}for(const i of t){const{action:s,stack:o,message:l}=i;!s||!o||(r=n.get(o[0].file))==null||r.errors.push({line:o[0].line||0,message:l})}return n}const g0=50,Es=({sidebarSize:e,sidebarHidden:t=!1,sidebarIsFirst:n=!1,orientation:r="vertical",minSidebarSize:i=g0,settingName:s,children:o})=>{const[l,a]=Ji(s?s+"."+r+":size":void 0,Math.max(i,e)*window.devicePixelRatio),[u,c]=Ji(s?s+"."+r+":size":void 0,Math.max(i,e)*window.devicePixelRatio),[f,p]=L.useState(null),[v,y]=pn();let w;r==="vertical"?(w=u/window.devicePixelRatio,v&&v.height<w&&(w=v.height-10)):(w=l/window.devicePixelRatio,v&&v.width<w&&(w=v.width-10));const x=L.Children.toArray(o);document.body.style.userSelect=f?"none":"inherit";let h={};return r==="vertical"?n?h={top:f?0:w-4,bottom:f?0:void 0,height:f?"initial":8}:h={bottom:f?0:w-4,top:f?0:void 0,height:f?"initial":8}:n?h={left:f?0:w-4,right:f?0:void 0,width:f?"initial":8}:h={right:f?0:w-4,left:f?0:void 0,width:f?"initial":8},d.jsxs("div",{className:"split-view "+r+(n?" sidebar-first":""),ref:y,children:[d.jsx("div",{className:"split-view-main",children:x[0]}),!t&&d.jsx("div",{style:{flexBasis:w},className:"split-view-sidebar",children:x[1]}),!t&&d.jsx("div",{style:h,className:"split-view-resizer",onMouseDown:m=>p({offset:r==="vertical"?m.clientY:m.clientX,size:w}),onMouseUp:()=>p(null),onMouseMove:m=>{if(!m.buttons)p(null);else if(f){const _=(r==="vertical"?m.clientY:m.clientX)-f.offset,N=n?f.size+_:f.size-_,C=m.target.parentElement.getBoundingClientRect(),E=Math.min(Math.max(i,N),(r==="vertical"?C.height:C.width)-i);r==="vertical"?c(E*window.devicePixelRatio):a(E*window.devicePixelRatio)}}})]})};function Bs(e,t="'"){const n=JSON.stringify(e),r=n.substring(1,n.length-1).replace(/\\"/g,'"');if(t==="'")return t+r.replace(/[']/g,"\\'")+t;if(t==='"')return t+r.replace(/["]/g,'\\"')+t;if(t==="`")return t+r.replace(/[`]/g,"`")+t;throw new Error("Invalid escape char")}function ks(e){return e.charAt(0).toUpperCase()+e.substring(1)}function Yf(e){return e.replace(/([a-z0-9])([A-Z])/g,"$1_$2").replace(/([A-Z])([A-Z][a-z])/g,"$1_$2").toLowerCase()}function qe(e){let t="";for(let n=0;n<e.length;n++)t+=v0(e,n);return t}function gr(e){return`"${qe(e).replace(/\\ /g," ")}"`}function v0(e,t){const n=e.charCodeAt(t);return n===0?"�":n>=1&&n<=31||n>=48&&n<=57&&(t===0||t===1&&e.charCodeAt(0)===45)?"\\"+n.toString(16)+" ":t===0&&n===45&&e.length===1?"\\"+e.charAt(t):n>=128||n===45||n===95||n>=48&&n<=57||n>=65&&n<=90||n>=97&&n<=122?e.charAt(t):"\\"+e.charAt(t)}function Fe(e){return e.replace(/\u200b/g,"").trim().replace(/\s+/g," ")}function Ws(e){return e.replace(/(^|[^\\])(\\\\)*\\(['"`])/g,"$1$2$3")}function Jf(e){return e.unicode||e.unicodeSets?String(e):String(e).replace(/(^|[^\\])(\\\\)*(["'`])/g,"$1$2\\$3").replace(/>>/g,"\\>\\>")}function st(e,t){return typeof e!="string"?Jf(e):`${JSON.stringify(e)}${t?"s":"i"}`}function _e(e,t){return typeof e!="string"?Jf(e):`"${e.replace(/\\/g,"\\\\").replace(/["]/g,'\\"')}"${t?"s":"i"}`}function y0(e,t,n=""){if(e.length<=t)return e;const r=[...e];return r.length>t?r.slice(0,t-n.length).join("")+n:r.join("")}function nc(e,t){return y0(e,t,"…")}function w0(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}const re=function(e,t,n){return e>=t&&e<=n};function be(e){return re(e,48,57)}function rc(e){return be(e)||re(e,65,70)||re(e,97,102)}function x0(e){return re(e,65,90)}function S0(e){return re(e,97,122)}function _0(e){return x0(e)||S0(e)}function E0(e){return e>=128}function qi(e){return _0(e)||E0(e)||e===95}function ic(e){return qi(e)||be(e)||e===45}function k0(e){return re(e,0,8)||e===11||re(e,14,31)||e===127}function Xi(e){return e===10}function gt(e){return Xi(e)||e===9||e===32}const T0=1114111;class Ra extends Error{constructor(t){super(t),this.name="InvalidCharacterError"}}function C0(e){const t=[];for(let n=0;n<e.length;n++){let r=e.charCodeAt(n);if(r===13&&e.charCodeAt(n+1)===10&&(r=10,n++),(r===13||r===12)&&(r=10),r===0&&(r=65533),re(r,55296,56319)&&re(e.charCodeAt(n+1),56320,57343)){const i=r-55296,s=e.charCodeAt(n+1)-56320;r=Math.pow(2,16)+i*Math.pow(2,10)+s,n++}t.push(r)}return t}function se(e){if(e<=65535)return String.fromCharCode(e);e-=Math.pow(2,16);const t=Math.floor(e/Math.pow(2,10))+55296,n=e%Math.pow(2,10)+56320;return String.fromCharCode(t)+String.fromCharCode(n)}function N0(e){const t=C0(e);let n=-1;const r=[];let i;const s=function(T){return T>=t.length?-1:t[T]},o=function(T){if(T===void 0&&(T=1),T>3)throw"Spec Error: no more than three codepoints of lookahead.";return s(n+T)},l=function(T){return T===void 0&&(T=1),n+=T,i=s(n),!0},a=function(){return n-=1,!0},u=function(T){return T===void 0&&(T=i),T===-1},c=function(){if(f(),l(),gt(i)){for(;gt(o());)l();return new bl}else{if(i===34)return y();if(i===35)if(ic(o())||h(o(1),o(2))){const T=new hh("");return g(o(1),o(2),o(3))&&(T.type="id"),T.value=C(),T}else return new ye(i);else return i===36?o()===61?(l(),new M0):new ye(i):i===39?y():i===40?new ah:i===41?new uh:i===42?o()===61?(l(),new j0):new ye(i):i===43?k()?(a(),p()):new ye(i):i===44?new ih:i===45?k()?(a(),p()):o(1)===45&&o(2)===62?(l(2),new th):_()?(a(),v()):new ye(i):i===46?k()?(a(),p()):new ye(i):i===58?new nh:i===59?new rh:i===60?o(1)===33&&o(2)===45&&o(3)===45?(l(3),new eh):new ye(i):i===64?g(o(1),o(2),o(3))?new fh(C()):new ye(i):i===91?new lh:i===92?m()?(a(),v()):new ye(i):i===93?new Ll:i===94?o()===61?(l(),new A0):new ye(i):i===123?new sh:i===124?o()===61?(l(),new L0):o()===124?(l(),new ch):new ye(i):i===125?new oh:i===126?o()===61?(l(),new b0):new ye(i):be(i)?(a(),p()):qi(i)?(a(),v()):u()?new Ki:new ye(i)}},f=function(){for(;o(1)===47&&o(2)===42;)for(l(2);;)if(l(),i===42&&o()===47){l();break}else if(u())return},p=function(){const T=E();if(g(o(1),o(2),o(3))){const I=new I0;return I.value=T.value,I.repr=T.repr,I.type=T.type,I.unit=C(),I}else if(o()===37){l();const I=new vh;return I.value=T.value,I.repr=T.repr,I}else{const I=new gh;return I.value=T.value,I.repr=T.repr,I.type=T.type,I}},v=function(){const T=C();if(T.toLowerCase()==="url"&&o()===40){for(l();gt(o(1))&&gt(o(2));)l();return o()===34||o()===39?new Gi(T):gt(o())&&(o(2)===34||o(2)===39)?new Gi(T):w()}else return o()===40?(l(),new Gi(T)):new dh(T)},y=function(T){T===void 0&&(T=i);let I="";for(;l();){if(i===T||u())return new ph(I);if(Xi(i))return a(),new Zf;i===92?u(o())||(Xi(o())?l():I+=se(x())):I+=se(i)}throw new Error("Internal error")},w=function(){const T=new mh("");for(;gt(o());)l();if(u(o()))return T;for(;l();){if(i===41||u())return T;if(gt(i)){for(;gt(o());)l();return o()===41||u(o())?(l(),T):(A(),new Qi)}else{if(i===34||i===39||i===40||k0(i))return A(),new Qi;if(i===92)if(m())T.value+=se(x());else return A(),new Qi;else T.value+=se(i)}}throw new Error("Internal error")},x=function(){if(l(),rc(i)){const T=[i];for(let z=0;z<5&&rc(o());z++)l(),T.push(i);gt(o())&&l();let I=parseInt(T.map(function(z){return String.fromCharCode(z)}).join(""),16);return I>T0&&(I=65533),I}else return u()?65533:i},h=function(T,I){return!(T!==92||Xi(I))},m=function(){return h(i,o())},g=function(T,I,z){return T===45?qi(I)||I===45||h(I,z):qi(T)?!0:T===92?h(T,I):!1},_=function(){return g(i,o(1),o(2))},N=function(T,I,z){return T===43||T===45?!!(be(I)||I===46&&be(z)):T===46?!!be(I):!!be(T)},k=function(){return N(i,o(1),o(2))},C=function(){let T="";for(;l();)if(ic(i))T+=se(i);else if(m())T+=se(x());else return a(),T;throw new Error("Internal parse error")},E=function(){let T="",I="integer";for((o()===43||o()===45)&&(l(),T+=se(i));be(o());)l(),T+=se(i);if(o(1)===46&&be(o(2)))for(l(),T+=se(i),l(),T+=se(i),I="number";be(o());)l(),T+=se(i);const z=o(1),ae=o(2),Ne=o(3);if((z===69||z===101)&&be(ae))for(l(),T+=se(i),l(),T+=se(i),I="number";be(o());)l(),T+=se(i);else if((z===69||z===101)&&(ae===43||ae===45)&&be(Ne))for(l(),T+=se(i),l(),T+=se(i),l(),T+=se(i),I="number";be(o());)l(),T+=se(i);const ue=S(T);return{type:I,value:ue,repr:T}},S=function(T){return+T},A=function(){for(;l();){if(i===41||u())return;m()&&x()}};let P=0;for(;!u(o());)if(r.push(c()),P++,P>t.length*2)throw new Error("I'm infinite-looping!");return r}class te{constructor(){this.tokenType=""}toJSON(){return{token:this.tokenType}}toString(){return this.tokenType}toSource(){return""+this}}class Zf extends te{constructor(){super(...arguments),this.tokenType="BADSTRING"}}class Qi extends te{constructor(){super(...arguments),this.tokenType="BADURL"}}class bl extends te{constructor(){super(...arguments),this.tokenType="WHITESPACE"}toString(){return"WS"}toSource(){return" "}}class eh extends te{constructor(){super(...arguments),this.tokenType="CDO"}toSource(){return"<!--"}}class th extends te{constructor(){super(...arguments),this.tokenType="CDC"}toSource(){return"-->"}}class nh extends te{constructor(){super(...arguments),this.tokenType=":"}}class rh extends te{constructor(){super(...arguments),this.tokenType=";"}}class ih extends te{constructor(){super(...arguments),this.tokenType=","}}class tr extends te{constructor(){super(...arguments),this.value="",this.mirror=""}}class sh extends tr{constructor(){super(),this.tokenType="{",this.value="{",this.mirror="}"}}class oh extends tr{constructor(){super(),this.tokenType="}",this.value="}",this.mirror="{"}}class lh extends tr{constructor(){super(),this.tokenType="[",this.value="[",this.mirror="]"}}class Ll extends tr{constructor(){super(),this.tokenType="]",this.value="]",this.mirror="["}}class ah extends tr{constructor(){super(),this.tokenType="(",this.value="(",this.mirror=")"}}class uh extends tr{constructor(){super(),this.tokenType=")",this.value=")",this.mirror="("}}class b0 extends te{constructor(){super(...arguments),this.tokenType="~="}}class L0 extends te{constructor(){super(...arguments),this.tokenType="|="}}class A0 extends te{constructor(){super(...arguments),this.tokenType="^="}}class M0 extends te{constructor(){super(...arguments),this.tokenType="$="}}class j0 extends te{constructor(){super(...arguments),this.tokenType="*="}}class ch extends te{constructor(){super(...arguments),this.tokenType="||"}}class Ki extends te{constructor(){super(...arguments),this.tokenType="EOF"}toSource(){return""}}class ye extends te{constructor(t){super(),this.tokenType="DELIM",this.value="",this.value=se(t)}toString(){return"DELIM("+this.value+")"}toJSON(){const t=this.constructor.prototype.constructor.prototype.toJSON.call(this);return t.value=this.value,t}toSource(){return this.value==="\\"?`\\
`:this.value}}class nr extends te{constructor(){super(...arguments),this.value=""}ASCIIMatch(t){return this.value.toLowerCase()===t.toLowerCase()}toJSON(){const t=this.constructor.prototype.constructor.prototype.toJSON.call(this);return t.value=this.value,t}}class dh extends nr{constructor(t){super(),this.tokenType="IDENT",this.value=t}toString(){return"IDENT("+this.value+")"}toSource(){return li(this.value)}}class Gi extends nr{constructor(t){super(),this.tokenType="FUNCTION",this.value=t,this.mirror=")"}toString(){return"FUNCTION("+this.value+")"}toSource(){return li(this.value)+"("}}class fh extends nr{constructor(t){super(),this.tokenType="AT-KEYWORD",this.value=t}toString(){return"AT("+this.value+")"}toSource(){return"@"+li(this.value)}}class hh extends nr{constructor(t){super(),this.tokenType="HASH",this.value=t,this.type="unrestricted"}toString(){return"HASH("+this.value+")"}toJSON(){const t=this.constructor.prototype.constructor.prototype.toJSON.call(this);return t.value=this.value,t.type=this.type,t}toSource(){return this.type==="id"?"#"+li(this.value):"#"+P0(this.value)}}class ph extends nr{constructor(t){super(),this.tokenType="STRING",this.value=t}toString(){return'"'+yh(this.value)+'"'}}class mh extends nr{constructor(t){super(),this.tokenType="URL",this.value=t}toString(){return"URL("+this.value+")"}toSource(){return'url("'+yh(this.value)+'")'}}class gh extends te{constructor(){super(),this.tokenType="NUMBER",this.type="integer",this.repr=""}toString(){return this.type==="integer"?"INT("+this.value+")":"NUMBER("+this.value+")"}toJSON(){const t=super.toJSON();return t.value=this.value,t.type=this.type,t.repr=this.repr,t}toSource(){return this.repr}}class vh extends te{constructor(){super(),this.tokenType="PERCENTAGE",this.repr=""}toString(){return"PERCENTAGE("+this.value+")"}toJSON(){const t=this.constructor.prototype.constructor.prototype.toJSON.call(this);return t.value=this.value,t.repr=this.repr,t}toSource(){return this.repr+"%"}}class I0 extends te{constructor(){super(),this.tokenType="DIMENSION",this.type="integer",this.repr="",this.unit=""}toString(){return"DIM("+this.value+","+this.unit+")"}toJSON(){const t=this.constructor.prototype.constructor.prototype.toJSON.call(this);return t.value=this.value,t.type=this.type,t.repr=this.repr,t.unit=this.unit,t}toSource(){const t=this.repr;let n=li(this.unit);return n[0].toLowerCase()==="e"&&(n[1]==="-"||re(n.charCodeAt(1),48,57))&&(n="\\65 "+n.slice(1,n.length)),t+n}}function li(e){e=""+e;let t="";const n=e.charCodeAt(0);for(let r=0;r<e.length;r++){const i=e.charCodeAt(r);if(i===0)throw new Ra("Invalid character: the input contains U+0000.");re(i,1,31)||i===127||r===0&&re(i,48,57)||r===1&&re(i,48,57)&&n===45?t+="\\"+i.toString(16)+" ":i>=128||i===45||i===95||re(i,48,57)||re(i,65,90)||re(i,97,122)?t+=e[r]:t+="\\"+e[r]}return t}function P0(e){e=""+e;let t="";for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);if(r===0)throw new Ra("Invalid character: the input contains U+0000.");r>=128||r===45||r===95||re(r,48,57)||re(r,65,90)||re(r,97,122)?t+=e[n]:t+="\\"+r.toString(16)+" "}return t}function yh(e){e=""+e;let t="";for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);if(r===0)throw new Ra("Invalid character: the input contains U+0000.");re(r,1,31)||r===127?t+="\\"+r.toString(16)+" ":r===34||r===92?t+="\\"+e[n]:t+=e[n]}return t}class Ae extends Error{}function R0(e,t){let n;try{n=N0(e),n[n.length-1]instanceof Ki||n.push(new Ki)}catch(S){const A=S.message+` while parsing selector "${e}"`,P=(S.stack||"").indexOf(S.message);throw P!==-1&&(S.stack=S.stack.substring(0,P)+A+S.stack.substring(P+S.message.length)),S.message=A,S}const r=n.find(S=>S instanceof fh||S instanceof Zf||S instanceof Qi||S instanceof ch||S instanceof eh||S instanceof th||S instanceof rh||S instanceof sh||S instanceof oh||S instanceof mh||S instanceof vh);if(r)throw new Ae(`Unsupported token "${r.toSource()}" while parsing selector "${e}"`);let i=0;const s=new Set;function o(){return new Ae(`Unexpected token "${n[i].toSource()}" while parsing selector "${e}"`)}function l(){for(;n[i]instanceof bl;)i++}function a(S=i){return n[S]instanceof dh}function u(S=i){return n[S]instanceof ph}function c(S=i){return n[S]instanceof gh}function f(S=i){return n[S]instanceof ih}function p(S=i){return n[S]instanceof ah}function v(S=i){return n[S]instanceof uh}function y(S=i){return n[S]instanceof Gi}function w(S=i){return n[S]instanceof ye&&n[S].value==="*"}function x(S=i){return n[S]instanceof Ki}function h(S=i){return n[S]instanceof ye&&[">","+","~"].includes(n[S].value)}function m(S=i){return f(S)||v(S)||x(S)||h(S)||n[S]instanceof bl}function g(){const S=[_()];for(;l(),!!f();)i++,S.push(_());return S}function _(){return l(),c()||u()?n[i++].value:N()}function N(){const S={simples:[]};for(l(),h()?S.simples.push({selector:{functions:[{name:"scope",args:[]}]},combinator:""}):S.simples.push({selector:k(),combinator:""});;){if(l(),h())S.simples[S.simples.length-1].combinator=n[i++].value,l();else if(m())break;S.simples.push({combinator:"",selector:k()})}return S}function k(){let S="";const A=[];for(;!m();)if(a()||w())S+=n[i++].toSource();else if(n[i]instanceof hh)S+=n[i++].toSource();else if(n[i]instanceof ye&&n[i].value===".")if(i++,a())S+="."+n[i++].toSource();else throw o();else if(n[i]instanceof nh)if(i++,a())if(!t.has(n[i].value.toLowerCase()))S+=":"+n[i++].toSource();else{const P=n[i++].value.toLowerCase();A.push({name:P,args:[]}),s.add(P)}else if(y()){const P=n[i++].value.toLowerCase();if(t.has(P)?(A.push({name:P,args:g()}),s.add(P)):S+=`:${P}(${C()})`,l(),!v())throw o();i++}else throw o();else if(n[i]instanceof lh){for(S+="[",i++;!(n[i]instanceof Ll)&&!x();)S+=n[i++].toSource();if(!(n[i]instanceof Ll))throw o();S+="]",i++}else throw o();if(!S&&!A.length)throw o();return{css:S||void 0,functions:A}}function C(){let S="",A=1;for(;!x()&&((p()||y())&&A++,v()&&A--,!!A);)S+=n[i++].toSource();return S}const E=g();if(!x())throw o();if(E.some(S=>typeof S!="object"||!("simples"in S)))throw new Ae(`Error while parsing selector "${e}"`);return{selector:E,names:Array.from(s)}}const Al=new Set(["internal:has","internal:has-not","internal:and","internal:or","internal:chain","left-of","right-of","above","below","near"]),$0=new Set(["left-of","right-of","above","below","near"]),wh=new Set(["not","is","where","has","scope","light","visible","text","text-matches","text-is","has-text","above","below","right-of","left-of","near","nth-match"]);function qs(e){const t=D0(e),n=[];for(const r of t.parts){if(r.name==="css"||r.name==="css:light"){r.name==="css:light"&&(r.body=":light("+r.body+")");const i=R0(r.body,wh);n.push({name:"css",body:i.selector,source:r.body});continue}if(Al.has(r.name)){let i,s;try{const u=JSON.parse("["+r.body+"]");if(!Array.isArray(u)||u.length<1||u.length>2||typeof u[0]!="string")throw new Ae(`Malformed selector: ${r.name}=`+r.body);if(i=u[0],u.length===2){if(typeof u[1]!="number"||!$0.has(r.name))throw new Ae(`Malformed selector: ${r.name}=`+r.body);s=u[1]}}catch{throw new Ae(`Malformed selector: ${r.name}=`+r.body)}const o={name:r.name,source:r.body,body:{parsed:qs(i),distance:s}},l=[...o.body.parsed.parts].reverse().find(u=>u.name==="internal:control"&&u.body==="enter-frame"),a=l?o.body.parsed.parts.indexOf(l):-1;a!==-1&&O0(o.body.parsed.parts.slice(0,a+1),n.slice(0,a+1))&&o.body.parsed.parts.splice(0,a+1),n.push(o);continue}n.push({...r,source:r.body})}if(Al.has(n[0].name))throw new Ae(`"${n[0].name}" selector cannot be first`);return{capture:t.capture,parts:n}}function O0(e,t){return hn({parts:e})===hn({parts:t})}function hn(e,t){return typeof e=="string"?e:e.parts.map((n,r)=>{let i=!0;!t&&r!==e.capture&&(n.name==="css"||n.name==="xpath"&&n.source.startsWith("//")||n.source.startsWith(".."))&&(i=!1);const s=i?n.name+"=":"";return`${r===e.capture?"*":""}${s}${n.source}`}).join(" >> ")}function z0(e,t){const n=(r,i)=>{for(const s of r.parts)t(s,i),Al.has(s.name)&&n(s.body.parsed,!0)};n(e,!1)}function D0(e){let t=0,n,r=0;const i={parts:[]},s=()=>{const l=e.substring(r,t).trim(),a=l.indexOf("=");let u,c;a!==-1&&l.substring(0,a).trim().match(/^[a-zA-Z_0-9-+:*]+$/)?(u=l.substring(0,a).trim(),c=l.substring(a+1)):l.length>1&&l[0]==='"'&&l[l.length-1]==='"'||l.length>1&&l[0]==="'"&&l[l.length-1]==="'"?(u="text",c=l):/^\(*\/\//.test(l)||l.startsWith("..")?(u="xpath",c=l):(u="css",c=l);let f=!1;if(u[0]==="*"&&(f=!0,u=u.substring(1)),i.parts.push({name:u,body:c}),f){if(i.capture!==void 0)throw new Ae("Only one of the selectors can capture using * modifier");i.capture=i.parts.length-1}};if(!e.includes(">>"))return t=e.length,s(),i;const o=()=>{const a=e.substring(r,t).match(/^\s*text\s*=(.*)$/);return!!a&&!!a[1]};for(;t<e.length;){const l=e[t];l==="\\"&&t+1<e.length?t+=2:l===n?(n=void 0,t++):!n&&(l==='"'||l==="'"||l==="`")&&!o()?(n=l,t++):!n&&l===">"&&e[t+1]===">"?(s(),t+=2,r=t):t++}return s(),i}function ln(e,t){let n=0,r=e.length===0;const i=()=>e[n]||"",s=()=>{const x=i();return++n,r=n>=e.length,x},o=x=>{throw r?new Ae(`Unexpected end of selector while parsing selector \`${e}\``):new Ae(`Error while parsing selector \`${e}\` - unexpected symbol "${i()}" at position ${n}`+(x?" during "+x:""))};function l(){for(;!r&&/\s/.test(i());)s()}function a(x){return x>=""||x>="0"&&x<="9"||x>="A"&&x<="Z"||x>="a"&&x<="z"||x>="0"&&x<="9"||x==="_"||x==="-"}function u(){let x="";for(l();!r&&a(i());)x+=s();return x}function c(x){let h=s();for(h!==x&&o("parsing quoted string");!r&&i()!==x;)i()==="\\"&&s(),h+=s();return i()!==x&&o("parsing quoted string"),h+=s(),h}function f(){s()!=="/"&&o("parsing regular expression");let x="",h=!1;for(;!r;){if(i()==="\\")x+=s(),r&&o("parsing regular expression");else if(h&&i()==="]")h=!1;else if(!h&&i()==="[")h=!0;else if(!h&&i()==="/")break;x+=s()}s()!=="/"&&o("parsing regular expression");let m="";for(;!r&&i().match(/[dgimsuy]/);)m+=s();try{return new RegExp(x,m)}catch(g){throw new Ae(`Error while parsing selector \`${e}\`: ${g.message}`)}}function p(){let x="";return l(),i()==="'"||i()==='"'?x=c(i()).slice(1,-1):x=u(),x||o("parsing property path"),x}function v(){l();let x="";return r||(x+=s()),!r&&x!=="="&&(x+=s()),["=","*=","^=","$=","|=","~="].includes(x)||o("parsing operator"),x}function y(){s();const x=[];for(x.push(p()),l();i()===".";)s(),x.push(p()),l();if(i()==="]")return s(),{name:x.join("."),jsonPath:x,op:"<truthy>",value:null,caseSensitive:!1};const h=v();let m,g=!0;if(l(),i()==="/"){if(h!=="=")throw new Ae(`Error while parsing selector \`${e}\` - cannot use ${h} in attribute with regular expression`);m=f()}else if(i()==="'"||i()==='"')m=c(i()).slice(1,-1),l(),i()==="i"||i()==="I"?(g=!1,s()):(i()==="s"||i()==="S")&&(g=!0,s());else{for(m="";!r&&(a(i())||i()==="+"||i()===".");)m+=s();m==="true"?m=!0:m==="false"?m=!1:t||(m=+m,Number.isNaN(m)&&o("parsing attribute value"))}if(l(),i()!=="]"&&o("parsing attribute value"),s(),h!=="="&&typeof m!="string")throw new Ae(`Error while parsing selector \`${e}\` - cannot use ${h} in attribute with non-string matching value - ${m}`);return{name:x.join("."),jsonPath:x,op:h,value:m,caseSensitive:g}}const w={name:"",attributes:[]};for(w.name=u(),l();i()==="[";)w.attributes.push(y()),l();if(r||o(void 0),!w.name&&!w.attributes.length)throw new Ae(`Error while parsing selector \`${e}\` - selector cannot be empty`);return w}function Xt(e,t,n=!1){return xh(e,t,n)[0]}function xh(e,t,n=!1,r=20,i){try{return _n(new q0[e](i),qs(t),n,r)}catch{return[t]}}function _n(e,t,n=!1,r=20){const i=[...t.parts];for(let l=0;l<i.length-1;l++)if(i[l].name==="nth"&&i[l+1].name==="internal:control"&&i[l+1].body==="enter-frame"){const[a]=i.splice(l,1);i.splice(l+1,0,a)}const s=[];let o=n?"frame-locator":"page";for(let l=0;l<i.length;l++){const a=i[l],u=o;if(o="locator",a.name==="nth"){a.body==="0"?s.push([e.generateLocator(u,"first",""),e.generateLocator(u,"nth","0")]):a.body==="-1"?s.push([e.generateLocator(u,"last",""),e.generateLocator(u,"nth","-1")]):s.push([e.generateLocator(u,"nth",a.body)]);continue}if(a.name==="internal:text"){const{exact:w,text:x}=vr(a.body);s.push([e.generateLocator(u,"text",x,{exact:w})]);continue}if(a.name==="internal:has-text"){const{exact:w,text:x}=vr(a.body);if(!w){s.push([e.generateLocator(u,"has-text",x,{exact:w})]);continue}}if(a.name==="internal:has-not-text"){const{exact:w,text:x}=vr(a.body);if(!w){s.push([e.generateLocator(u,"has-not-text",x,{exact:w})]);continue}}if(a.name==="internal:has"){const w=_n(e,a.body.parsed,!1,r);s.push(w.map(x=>e.generateLocator(u,"has",x)));continue}if(a.name==="internal:has-not"){const w=_n(e,a.body.parsed,!1,r);s.push(w.map(x=>e.generateLocator(u,"hasNot",x)));continue}if(a.name==="internal:and"){const w=_n(e,a.body.parsed,!1,r);s.push(w.map(x=>e.generateLocator(u,"and",x)));continue}if(a.name==="internal:or"){const w=_n(e,a.body.parsed,!1,r);s.push(w.map(x=>e.generateLocator(u,"or",x)));continue}if(a.name==="internal:chain"){const w=_n(e,a.body.parsed,!1,r);s.push(w.map(x=>e.generateLocator(u,"chain",x)));continue}if(a.name==="internal:label"){const{exact:w,text:x}=vr(a.body);s.push([e.generateLocator(u,"label",x,{exact:w})]);continue}if(a.name==="internal:role"){const w=ln(a.body,!0),x={attrs:[]};for(const h of w.attributes)h.name==="name"?(x.exact=h.caseSensitive,x.name=h.value):(h.name==="level"&&typeof h.value=="string"&&(h.value=+h.value),x.attrs.push({name:h.name==="include-hidden"?"includeHidden":h.name,value:h.value}));s.push([e.generateLocator(u,"role",w.name,x)]);continue}if(a.name==="internal:testid"){const w=ln(a.body,!0),{value:x}=w.attributes[0];s.push([e.generateLocator(u,"test-id",x)]);continue}if(a.name==="internal:attr"){const w=ln(a.body,!0),{name:x,value:h,caseSensitive:m}=w.attributes[0],g=h,_=!!m;if(x==="placeholder"){s.push([e.generateLocator(u,"placeholder",g,{exact:_})]);continue}if(x==="alt"){s.push([e.generateLocator(u,"alt",g,{exact:_})]);continue}if(x==="title"){s.push([e.generateLocator(u,"title",g,{exact:_})]);continue}}let c="default";const f=i[l+1];f&&f.name==="internal:control"&&f.body==="enter-frame"&&(c="frame",o="frame-locator",l++);const p=hn({parts:[a]}),v=e.generateLocator(u,c,p);if(c==="default"&&f&&["internal:has-text","internal:has-not-text"].includes(f.name)){const{exact:w,text:x}=vr(f.body);if(!w){const h=e.generateLocator("locator",f.name==="internal:has-text"?"has-text":"has-not-text",x,{exact:w}),m={};f.name==="internal:has-text"?m.hasText=x:m.hasNotText=x;const g=e.generateLocator(u,"default",p,m);s.push([e.chainLocators([v,h]),g]),l++;continue}}let y;if(["xpath","css"].includes(a.name)){const w=hn({parts:[a]},!0);y=e.generateLocator(u,c,w)}s.push([v,y].filter(Boolean))}return H0(e,s,r)}function H0(e,t,n){const r=t.map(()=>""),i=[],s=o=>{if(o===t.length)return i.push(e.chainLocators(r)),r.length<n;for(const l of t[o])if(r[o]=l,!s(o+1))return!1;return!0};return s(0),i}function vr(e){let t=!1;const n=e.match(/^\/(.*)\/([igm]*)$/);return n?{text:new RegExp(n[1],n[2])}:(e.endsWith('"')?(e=JSON.parse(e),t=!0):e.endsWith('"s')?(e=JSON.parse(e.substring(0,e.length-1)),t=!0):e.endsWith('"i')&&(e=JSON.parse(e.substring(0,e.length-1)),t=!1),{exact:t,text:e})}class F0{constructor(t){this.preferredQuote=t}generateLocator(t,n,r,i={}){switch(n){case"default":return i.hasText!==void 0?`locator(${this.quote(r)}, { hasText: ${this.toHasText(i.hasText)} })`:i.hasNotText!==void 0?`locator(${this.quote(r)}, { hasNotText: ${this.toHasText(i.hasNotText)} })`:`locator(${this.quote(r)})`;case"frame":return`frameLocator(${this.quote(r)})`;case"nth":return`nth(${r})`;case"first":return"first()";case"last":return"last()";case"role":const s=[];he(i.name)?s.push(`name: ${this.regexToSourceString(i.name)}`):typeof i.name=="string"&&(s.push(`name: ${this.quote(i.name)}`),i.exact&&s.push("exact: true"));for(const{name:l,value:a}of i.attrs)s.push(`${l}: ${typeof a=="string"?this.quote(a):a}`);const o=s.length?`, { ${s.join(", ")} }`:"";return`getByRole(${this.quote(r)}${o})`;case"has-text":return`filter({ hasText: ${this.toHasText(r)} })`;case"has-not-text":return`filter({ hasNotText: ${this.toHasText(r)} })`;case"has":return`filter({ has: ${r} })`;case"hasNot":return`filter({ hasNot: ${r} })`;case"and":return`and(${r})`;case"or":return`or(${r})`;case"chain":return`locator(${r})`;case"test-id":return`getByTestId(${this.toTestIdValue(r)})`;case"text":return this.toCallWithExact("getByText",r,!!i.exact);case"alt":return this.toCallWithExact("getByAltText",r,!!i.exact);case"placeholder":return this.toCallWithExact("getByPlaceholder",r,!!i.exact);case"label":return this.toCallWithExact("getByLabel",r,!!i.exact);case"title":return this.toCallWithExact("getByTitle",r,!!i.exact);default:throw new Error("Unknown selector kind "+n)}}chainLocators(t){return t.join(".")}regexToSourceString(t){return Ws(String(t))}toCallWithExact(t,n,r){return he(n)?`${t}(${this.regexToSourceString(n)})`:r?`${t}(${this.quote(n)}, { exact: true })`:`${t}(${this.quote(n)})`}toHasText(t){return he(t)?this.regexToSourceString(t):this.quote(t)}toTestIdValue(t){return he(t)?this.regexToSourceString(t):this.quote(t)}quote(t){return Bs(t,this.preferredQuote??"'")}}class U0{generateLocator(t,n,r,i={}){switch(n){case"default":return i.hasText!==void 0?`locator(${this.quote(r)}, has_text=${this.toHasText(i.hasText)})`:i.hasNotText!==void 0?`locator(${this.quote(r)}, has_not_text=${this.toHasText(i.hasNotText)})`:`locator(${this.quote(r)})`;case"frame":return`frame_locator(${this.quote(r)})`;case"nth":return`nth(${r})`;case"first":return"first";case"last":return"last";case"role":const s=[];he(i.name)?s.push(`name=${this.regexToString(i.name)}`):typeof i.name=="string"&&(s.push(`name=${this.quote(i.name)}`),i.exact&&s.push("exact=True"));for(const{name:l,value:a}of i.attrs){let u=typeof a=="string"?this.quote(a):a;typeof a=="boolean"&&(u=a?"True":"False"),s.push(`${Yf(l)}=${u}`)}const o=s.length?`, ${s.join(", ")}`:"";return`get_by_role(${this.quote(r)}${o})`;case"has-text":return`filter(has_text=${this.toHasText(r)})`;case"has-not-text":return`filter(has_not_text=${this.toHasText(r)})`;case"has":return`filter(has=${r})`;case"hasNot":return`filter(has_not=${r})`;case"and":return`and_(${r})`;case"or":return`or_(${r})`;case"chain":return`locator(${r})`;case"test-id":return`get_by_test_id(${this.toTestIdValue(r)})`;case"text":return this.toCallWithExact("get_by_text",r,!!i.exact);case"alt":return this.toCallWithExact("get_by_alt_text",r,!!i.exact);case"placeholder":return this.toCallWithExact("get_by_placeholder",r,!!i.exact);case"label":return this.toCallWithExact("get_by_label",r,!!i.exact);case"title":return this.toCallWithExact("get_by_title",r,!!i.exact);default:throw new Error("Unknown selector kind "+n)}}chainLocators(t){return t.join(".")}regexToString(t){const n=t.flags.includes("i")?", re.IGNORECASE":"";return`re.compile(r"${Ws(t.source).replace(/\\\//,"/").replace(/"/g,'\\"')}"${n})`}toCallWithExact(t,n,r){return he(n)?`${t}(${this.regexToString(n)})`:r?`${t}(${this.quote(n)}, exact=True)`:`${t}(${this.quote(n)})`}toHasText(t){return he(t)?this.regexToString(t):`${this.quote(t)}`}toTestIdValue(t){return he(t)?this.regexToString(t):this.quote(t)}quote(t){return Bs(t,'"')}}class V0{generateLocator(t,n,r,i={}){let s;switch(t){case"page":s="Page";break;case"frame-locator":s="FrameLocator";break;case"locator":s="Locator";break}switch(n){case"default":return i.hasText!==void 0?`locator(${this.quote(r)}, new ${s}.LocatorOptions().setHasText(${this.toHasText(i.hasText)}))`:i.hasNotText!==void 0?`locator(${this.quote(r)}, new ${s}.LocatorOptions().setHasNotText(${this.toHasText(i.hasNotText)}))`:`locator(${this.quote(r)})`;case"frame":return`frameLocator(${this.quote(r)})`;case"nth":return`nth(${r})`;case"first":return"first()";case"last":return"last()";case"role":const o=[];he(i.name)?o.push(`.setName(${this.regexToString(i.name)})`):typeof i.name=="string"&&(o.push(`.setName(${this.quote(i.name)})`),i.exact&&o.push(".setExact(true)"));for(const{name:a,value:u}of i.attrs)o.push(`.set${ks(a)}(${typeof u=="string"?this.quote(u):u})`);const l=o.length?`, new ${s}.GetByRoleOptions()${o.join("")}`:"";return`getByRole(AriaRole.${Yf(r).toUpperCase()}${l})`;case"has-text":return`filter(new ${s}.FilterOptions().setHasText(${this.toHasText(r)}))`;case"has-not-text":return`filter(new ${s}.FilterOptions().setHasNotText(${this.toHasText(r)}))`;case"has":return`filter(new ${s}.FilterOptions().setHas(${r}))`;case"hasNot":return`filter(new ${s}.FilterOptions().setHasNot(${r}))`;case"and":return`and(${r})`;case"or":return`or(${r})`;case"chain":return`locator(${r})`;case"test-id":return`getByTestId(${this.toTestIdValue(r)})`;case"text":return this.toCallWithExact(s,"getByText",r,!!i.exact);case"alt":return this.toCallWithExact(s,"getByAltText",r,!!i.exact);case"placeholder":return this.toCallWithExact(s,"getByPlaceholder",r,!!i.exact);case"label":return this.toCallWithExact(s,"getByLabel",r,!!i.exact);case"title":return this.toCallWithExact(s,"getByTitle",r,!!i.exact);default:throw new Error("Unknown selector kind "+n)}}chainLocators(t){return t.join(".")}regexToString(t){const n=t.flags.includes("i")?", Pattern.CASE_INSENSITIVE":"";return`Pattern.compile(${this.quote(Ws(t.source))}${n})`}toCallWithExact(t,n,r,i){return he(r)?`${n}(${this.regexToString(r)})`:i?`${n}(${this.quote(r)}, new ${t}.${ks(n)}Options().setExact(true))`:`${n}(${this.quote(r)})`}toHasText(t){return he(t)?this.regexToString(t):this.quote(t)}toTestIdValue(t){return he(t)?this.regexToString(t):this.quote(t)}quote(t){return Bs(t,'"')}}class B0{generateLocator(t,n,r,i={}){switch(n){case"default":return i.hasText!==void 0?`Locator(${this.quote(r)}, new() { ${this.toHasText(i.hasText)} })`:i.hasNotText!==void 0?`Locator(${this.quote(r)}, new() { ${this.toHasNotText(i.hasNotText)} })`:`Locator(${this.quote(r)})`;case"frame":return`FrameLocator(${this.quote(r)})`;case"nth":return`Nth(${r})`;case"first":return"First";case"last":return"Last";case"role":const s=[];he(i.name)?s.push(`NameRegex = ${this.regexToString(i.name)}`):typeof i.name=="string"&&(s.push(`Name = ${this.quote(i.name)}`),i.exact&&s.push("Exact = true"));for(const{name:l,value:a}of i.attrs)s.push(`${ks(l)} = ${typeof a=="string"?this.quote(a):a}`);const o=s.length?`, new() { ${s.join(", ")} }`:"";return`GetByRole(AriaRole.${ks(r)}${o})`;case"has-text":return`Filter(new() { ${this.toHasText(r)} })`;case"has-not-text":return`Filter(new() { ${this.toHasNotText(r)} })`;case"has":return`Filter(new() { Has = ${r} })`;case"hasNot":return`Filter(new() { HasNot = ${r} })`;case"and":return`And(${r})`;case"or":return`Or(${r})`;case"chain":return`Locator(${r})`;case"test-id":return`GetByTestId(${this.toTestIdValue(r)})`;case"text":return this.toCallWithExact("GetByText",r,!!i.exact);case"alt":return this.toCallWithExact("GetByAltText",r,!!i.exact);case"placeholder":return this.toCallWithExact("GetByPlaceholder",r,!!i.exact);case"label":return this.toCallWithExact("GetByLabel",r,!!i.exact);case"title":return this.toCallWithExact("GetByTitle",r,!!i.exact);default:throw new Error("Unknown selector kind "+n)}}chainLocators(t){return t.join(".")}regexToString(t){const n=t.flags.includes("i")?", RegexOptions.IgnoreCase":"";return`new Regex(${this.quote(Ws(t.source))}${n})`}toCallWithExact(t,n,r){return he(n)?`${t}(${this.regexToString(n)})`:r?`${t}(${this.quote(n)}, new() { Exact = true })`:`${t}(${this.quote(n)})`}toHasText(t){return he(t)?`HasTextRegex = ${this.regexToString(t)}`:`HasText = ${this.quote(t)}`}toTestIdValue(t){return he(t)?this.regexToString(t):this.quote(t)}toHasNotText(t){return he(t)?`HasNotTextRegex = ${this.regexToString(t)}`:`HasNotText = ${this.quote(t)}`}quote(t){return Bs(t,'"')}}class W0{generateLocator(t,n,r,i={}){return JSON.stringify({kind:n,body:r,options:i})}chainLocators(t){const n=t.map(r=>JSON.parse(r));for(let r=0;r<n.length-1;++r)n[r].next=n[r+1];return JSON.stringify(n[0])}}const q0={javascript:F0,python:U0,java:V0,csharp:B0,jsonl:W0};function he(e){return e instanceof RegExp}const sc=new Map;function ai({name:e,items:t=[],id:n,render:r,icon:i,isError:s,isWarning:o,isInfo:l,indent:a,selectedItem:u,onAccepted:c,onSelected:f,onLeftArrow:p,onRightArrow:v,onHighlighted:y,onIconClicked:w,noItemsMessage:x,dataTestId:h,noHighlightOnHover:m}){const g=L.useRef(null),[_,N]=L.useState();return L.useEffect(()=>{y==null||y(_)},[y,_]),L.useEffect(()=>{const k=g.current;if(!k)return;const C=()=>{sc.set(e,k.scrollTop)};return k.addEventListener("scroll",C,{passive:!0}),()=>k.removeEventListener("scroll",C)},[e]),L.useEffect(()=>{g.current&&(g.current.scrollTop=sc.get(e)||0)},[e]),d.jsx("div",{className:"list-view vbox",role:t.length>0?"list":void 0,"data-testid":h||e+"-list",children:d.jsxs("div",{className:"list-view-content",tabIndex:0,onDoubleClick:()=>u&&(c==null?void 0:c(u,t.indexOf(u))),onKeyDown:k=>{var A;if(u&&k.key==="Enter"){c==null||c(u,t.indexOf(u));return}if(k.key!=="ArrowDown"&&k.key!=="ArrowUp"&&k.key!=="ArrowLeft"&&k.key!=="ArrowRight")return;if(k.stopPropagation(),k.preventDefault(),u&&k.key==="ArrowLeft"){p==null||p(u,t.indexOf(u));return}if(u&&k.key==="ArrowRight"){v==null||v(u,t.indexOf(u));return}const C=u?t.indexOf(u):-1;let E=C;k.key==="ArrowDown"&&(C===-1?E=0:E=Math.min(C+1,t.length-1)),k.key==="ArrowUp"&&(C===-1?E=t.length-1:E=Math.max(C-1,0));const S=(A=g.current)==null?void 0:A.children.item(E);X0(S||void 0),y==null||y(void 0),f==null||f(t[E],E)},ref:g,children:[x&&t.length===0&&d.jsx("div",{className:"list-view-empty",children:x}),t.map((k,C)=>{const E=u===k?" selected":"",S=!m&&_===k?" highlighted":"",A=s!=null&&s(k,C)?" error":"",P=o!=null&&o(k,C)?" warning":"",T=l!=null&&l(k,C)?" info":"",I=(a==null?void 0:a(k,C))||0,z=r(k,C);return d.jsxs("div",{role:"listitem",className:"list-view-entry"+E+S+A+P+T,onClick:()=>f==null?void 0:f(k,C),onMouseEnter:()=>N(k),onMouseLeave:()=>N(void 0),children:[I?new Array(I).fill(0).map(()=>d.jsx("div",{className:"list-view-indent"})):void 0,i&&d.jsx("div",{className:"codicon "+(i(k,C)||"codicon-blank"),style:{minWidth:16,marginRight:4},onDoubleClick:ae=>{ae.preventDefault(),ae.stopPropagation()},onClick:ae=>{ae.stopPropagation(),ae.preventDefault(),w==null||w(k,C)}}),typeof z=="string"?d.jsx("div",{style:{textOverflow:"ellipsis",overflow:"hidden"},children:z}):z]},(n==null?void 0:n(k,C))||C)})]})})}function X0(e){e&&(e!=null&&e.scrollIntoViewIfNeeded?e.scrollIntoViewIfNeeded(!1):e==null||e.scrollIntoView())}const Q0=ai;function K0({name:e,rootItem:t,render:n,icon:r,isError:i,isVisible:s,selectedItem:o,onAccepted:l,onSelected:a,onHighlighted:u,treeState:c,setTreeState:f,noItemsMessage:p,dataTestId:v,autoExpandDepth:y}){const w=L.useMemo(()=>G0(t,o,c.expandedItems,y||0),[t,o,c,y]),x=L.useMemo(()=>{if(!s)return[...w.keys()];const h=new Map,m=_=>{const N=h.get(_);if(N!==void 0)return N;let k=_.children.some(E=>m(E));for(const E of _.children){const S=m(E);k=k||S}const C=s(_)||k;return h.set(_,C),C};for(const _ of w.keys())m(_);const g=[];for(const _ of w.keys())s(_)&&g.push(_);return g},[w,s]);return d.jsx(Q0,{name:e,items:x,id:h=>h.id,dataTestId:v||e+"-tree",render:h=>{const m=n(h);return d.jsxs(d.Fragment,{children:[r&&d.jsx("div",{className:"codicon "+(r(h)||"blank"),style:{minWidth:16,marginRight:4}}),typeof m=="string"?d.jsx("div",{style:{textOverflow:"ellipsis",overflow:"hidden"},children:m}):m]})},icon:h=>{const m=w.get(h).expanded;if(typeof m=="boolean")return m?"codicon-chevron-down":"codicon-chevron-right"},isError:h=>(i==null?void 0:i(h))||!1,indent:h=>w.get(h).depth,selectedItem:o,onAccepted:h=>l==null?void 0:l(h),onSelected:h=>a==null?void 0:a(h),onHighlighted:h=>u==null?void 0:u(h),onLeftArrow:h=>{const{expanded:m,parent:g}=w.get(h);m?(c.expandedItems.set(h.id,!1),f({...c})):g&&(a==null||a(g))},onRightArrow:h=>{h.children.length&&(c.expandedItems.set(h.id,!0),f({...c}))},onIconClicked:h=>{const{expanded:m}=w.get(h);if(m){for(let g=o;g;g=g.parent)if(g===h){a==null||a(h);break}c.expandedItems.set(h.id,!1)}else c.expandedItems.set(h.id,!0);f({...c})},noItemsMessage:p})}function G0(e,t,n,r){const i=new Map,s=new Set;for(let l=t==null?void 0:t.parent;l;l=l.parent)s.add(l.id);const o=(l,a)=>{for(const u of l.children){const c=s.has(u.id)||n.get(u.id),f=r>a&&i.size<25&&c!==!1,p=u.children.length?c??f:void 0;i.set(u,{depth:a,expanded:p,parent:e===l?null:l}),p&&o(u,a+1)}};return o(e,0),i}const Y0=K0,J0=({actions:e,selectedAction:t,selectedTime:n,setSelectedTime:r,sdkLanguage:i,onSelected:s,onHighlighted:o,revealConsole:l,isLive:a})=>{const[u,c]=L.useState({expandedItems:new Map}),{rootItem:f,itemMap:p}=L.useMemo(()=>c0(e),[e]),{selectedItem:v}=L.useMemo(()=>({selectedItem:t?p.get(t.callId):void 0}),[p,t]);return d.jsxs("div",{className:"vbox",children:[n&&d.jsxs("div",{className:"action-list-show-all",onClick:()=>r(void 0),children:[d.jsx("span",{className:"codicon codicon-triangle-left"}),"Show all"]}),d.jsx(Y0,{name:"actions",rootItem:f,treeState:u,setTreeState:c,selectedItem:v,onSelected:y=>s(y.action),onHighlighted:y=>o(y==null?void 0:y.action),onAccepted:y=>r({minimum:y.action.startTime,maximum:y.action.endTime}),isError:y=>{var w,x;return!!((x=(w=y.action)==null?void 0:w.error)!=null&&x.message)},isVisible:y=>!n||y.action.startTime<=n.maximum&&y.action.endTime>=n.minimum,render:y=>$a(y.action,{sdkLanguage:i,revealConsole:l,isLive:a,showDuration:!0,showBadges:!0})})]})},$a=(e,t)=>{const{sdkLanguage:n,revealConsole:r,isLive:i,showDuration:s,showBadges:o}=t,{errors:l,warnings:a}=h0(e),u=e.params.selector?Xt(n||"javascript",e.params.selector):void 0;let c="";return e.endTime?c=Qe(e.endTime-e.startTime):e.error?c="Timed out":i||(c="-"),d.jsxs(d.Fragment,{children:[d.jsxs("div",{className:"action-title",title:e.apiName,children:[d.jsx("span",{children:e.apiName}),u&&d.jsx("div",{className:"action-selector",title:u,children:u}),e.method==="goto"&&e.params.url&&d.jsx("div",{className:"action-url",title:e.params.url,children:e.params.url})]}),(s||o)&&d.jsx("div",{className:"spacer"}),s&&d.jsx("div",{className:"action-duration",children:c||d.jsx("span",{className:"codicon codicon-loading"})}),o&&d.jsxs("div",{className:"action-icons",onClick:()=>r==null?void 0:r(),children:[!!l&&d.jsxs("div",{className:"action-icon",children:[d.jsx("span",{className:"codicon codicon-error"}),d.jsx("span",{className:"action-icon-value",children:l})]}),!!a&&d.jsxs("div",{className:"action-icon",children:[d.jsx("span",{className:"codicon codicon-warning"}),d.jsx("span",{className:"action-icon-value",children:a})]})]})]})},Z0=({value:e})=>{const[t,n]=L.useState("codicon-clippy"),r=L.useCallback(()=>{navigator.clipboard.writeText(e).then(()=>{n("codicon-check"),setTimeout(()=>{n("codicon-clippy")},3e3)},()=>{n("codicon-close")})},[e]);return d.jsx("span",{className:`copy-icon codicon ${t}`,onClick:r})},rr=({text:e})=>d.jsx("div",{className:"fill",style:{display:"flex",alignItems:"center",justifyContent:"center",fontSize:24,fontWeight:"bold",opacity:.5},children:e}),e1=({action:e,sdkLanguage:t})=>{if(!e)return d.jsx(rr,{text:"No action selected"});const n={...e.params};delete n.info;const r=Object.keys(n),i=e.wallTime?new Date(e.wallTime).toLocaleString():null,s=e.endTime?Qe(e.endTime-e.startTime):"Timed Out";return d.jsxs("div",{className:"call-tab",children:[d.jsx("div",{className:"call-line",children:e.apiName}),d.jsxs(d.Fragment,{children:[d.jsx("div",{className:"call-section",children:"Time"}),i&&d.jsxs("div",{className:"call-line",children:["wall time:",d.jsx("span",{className:"call-value datetime",title:i,children:i})]}),d.jsxs("div",{className:"call-line",children:["duration:",d.jsx("span",{className:"call-value datetime",title:s,children:s})]})]}),!!r.length&&d.jsx("div",{className:"call-section",children:"Parameters"}),!!r.length&&r.map((o,l)=>oc(lc(e,o,n[o],t),"param-"+l)),!!e.result&&d.jsx("div",{className:"call-section",children:"Return value"}),!!e.result&&Object.keys(e.result).map((o,l)=>oc(lc(e,o,e.result[o],t),"result-"+l))]})};function oc(e,t){let n=e.text.replace(/\n/g,"↵");return e.type==="string"&&(n=`"${n}"`),d.jsxs("div",{className:"call-line",children:[e.name,":",d.jsx("span",{className:`call-value ${e.type}`,title:e.text,children:n}),["string","number","object","locator"].includes(e.type)&&d.jsx(Z0,{value:e.text})]},t)}function lc(e,t,n,r){const i=e.method.includes("eval")||e.method==="waitForFunction";if(t==="files")return{text:"<files>",type:"string",name:t};if((t==="eventInit"||t==="expectedValue"||t==="arg"&&i)&&(n=Ts(n.value,new Array(10).fill({handle:"<handle>"}))),(t==="value"&&i||t==="received"&&e.method==="expect")&&(n=Ts(n,new Array(10).fill({handle:"<handle>"}))),t==="selector")return{text:Xt(r||"javascript",e.params.selector),type:"locator",name:"locator"};const s=typeof n;return s!=="object"||n===null?{text:String(n),type:s,name:t}:n.guid?{text:"<handle>",type:"handle",name:t}:{text:JSON.stringify(n).slice(0,1e3),type:"object",name:t}}function Ts(e,t){if(e.n!==void 0)return e.n;if(e.s!==void 0)return e.s;if(e.b!==void 0)return e.b;if(e.v!==void 0){if(e.v==="undefined")return;if(e.v==="null")return null;if(e.v==="NaN")return NaN;if(e.v==="Infinity")return 1/0;if(e.v==="-Infinity")return-1/0;if(e.v==="-0")return-0}if(e.d!==void 0)return new Date(e.d);if(e.r!==void 0)return new RegExp(e.r.p,e.r.f);if(e.a!==void 0)return e.a.map(n=>Ts(n,t));if(e.o!==void 0){const n={};for(const{k:r,v:i}of e.o)n[r]=Ts(i,t);return n}return e.h!==void 0?t===void 0?"<object>":t[e.h]:"<object>"}const t1=ai,n1=({action:e,isLive:t})=>{const n=L.useMemo(()=>{var o;if(!e||!e.log.length)return[];const r=e.log,i=e.wallTime-e.startTime,s=[];for(let l=0;l<r.length;++l){let a="";if(r[l].time!==-1){const u=(o=r[l])==null?void 0:o.time;l+1<r.length?a=Qe(r[l+1].time-u):e.endTime>0?a=Qe(e.endTime-u):t?a=Qe(Date.now()-i-u):a="-"}s.push({message:r[l].message,time:a})}return s},[e,t]);return n.length?d.jsx(t1,{name:"log",items:n,render:r=>d.jsxs("div",{className:"log-list-item",children:[d.jsx("span",{className:"log-list-duration",children:r.time}),r.message]}),noHighlightOnHover:!0}):d.jsx(rr,{text:"No log entries"})};function ti(e){const t=/(\x1b\[(\d+(;\d+)*)m)|([^\x1b]+)/g,n=[];let r,i={};for(;(r=t.exec(e))!==null;){const[,,s,,o]=r;if(s){const l=+s;switch(l){case 0:i={};break;case 1:i["font-weight"]="bold";break;case 3:i["font-style"]="italic";break;case 4:i["text-decoration"]="underline";break;case 8:i.display="none";break;case 9:i["text-decoration"]="line-through";break;case 22:i={...i,"font-weight":void 0,"font-style":void 0,"text-decoration":void 0};break;case 23:i={...i,"font-weight":void 0,"font-style":void 0};break;case 24:i={...i,"text-decoration":void 0};break;case 30:case 31:case 32:case 33:case 34:case 35:case 36:case 37:i.color=ac[l-30];break;case 39:i={...i,color:void 0};break;case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:i["background-color"]=ac[l-40];break;case 49:i={...i,"background-color":void 0};break;case 53:i["text-decoration"]="overline";break;case 90:case 91:case 92:case 93:case 94:case 95:case 96:case 97:i.color=uc[l-90];break;case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:i["background-color"]=uc[l-100];break}}else o&&n.push(`<span style="${i1(i)}">${r1(o)}</span>`)}return n.join("")}const ac={0:"var(--vscode-terminal-ansiBlack)",1:"var(--vscode-terminal-ansiRed)",2:"var(--vscode-terminal-ansiGreen)",3:"var(--vscode-terminal-ansiYellow)",4:"var(--vscode-terminal-ansiBlue)",5:"var(--vscode-terminal-ansiMagenta)",6:"var(--vscode-terminal-ansiCyan)",7:"var(--vscode-terminal-ansiWhite)"},uc={0:"var(--vscode-terminal-ansiBrightBlack)",1:"var(--vscode-terminal-ansiBrightRed)",2:"var(--vscode-terminal-ansiBrightGreen)",3:"var(--vscode-terminal-ansiBrightYellow)",4:"var(--vscode-terminal-ansiBrightBlue)",5:"var(--vscode-terminal-ansiBrightMagenta)",6:"var(--vscode-terminal-ansiBrightCyan)",7:"var(--vscode-terminal-ansiBrightWhite)"};function r1(e){return e.replace(/[&"<>]/g,t=>({"&":"&amp;",'"':"&quot;","<":"&lt;",">":"&gt;"})[t])}function i1(e){return Object.entries(e).map(([t,n])=>`${t}: ${n}`).join("; ")}const s1=({error:e})=>{const t=L.useMemo(()=>ti(e),[e]);return d.jsx("div",{className:"error-message",dangerouslySetInnerHTML:{__html:t||""}})};function o1(e){return L.useMemo(()=>{if(!e)return{errors:new Map};const t=new Map;for(const n of e.errorDescriptors)t.set(n.message,n);return{errors:t}},[e])}const l1=({errorsModel:e,sdkLanguage:t,revealInSource:n})=>e.errors.size?d.jsx("div",{className:"fill",style:{overflow:"auto"},children:[...e.errors.entries()].map(([r,i])=>{var a;let s,o;const l=(a=i.stack)==null?void 0:a[0];return l&&(s=l.file.replace(/.*[/\\](.*)/,"$1")+":"+l.line,o=l.file+":"+l.line),d.jsxs("div",{children:[d.jsxs("div",{className:"hbox",style:{alignItems:"center",padding:"5px 10px",minHeight:36,fontWeight:"bold",color:"var(--vscode-errorForeground)"},children:[i.action&&$a(i.action,{sdkLanguage:t}),s&&d.jsxs("div",{className:"action-location",children:["@ ",d.jsx("span",{title:o,onClick:()=>n(i),children:s})]})]}),d.jsx(s1,{error:r})]},r)})}):d.jsx(rr,{text:"No errors"}),a1=ai;function u1(e,t){const{entries:n}=L.useMemo(()=>{if(!e)return{entries:[]};const i=[];for(const s of e.events){if(s.type==="console"){const o=s.args&&s.args.length?d1(s.args):Sh(s.text),l=s.location.url,u=`${l?l.substring(l.lastIndexOf("/")+1):"<anonymous>"}:${s.location.lineNumber}`;i.push({browserMessage:{body:o,location:u},isError:s.messageType==="error",isWarning:s.messageType==="warning",timestamp:s.time})}s.type==="event"&&s.method==="pageError"&&i.push({browserError:s.params.error,isError:!0,isWarning:!1,timestamp:s.time})}for(const s of e.stdio){let o="";s.text&&(o=ti(s.text.trim())||""),s.base64&&(o=ti(atob(s.base64).trim())||""),i.push({nodeMessage:{html:o},isError:s.type==="stderr",isWarning:!1,timestamp:s.timestamp})}return i.sort((s,o)=>s.timestamp-o.timestamp),{entries:i}},[e]);return{entries:L.useMemo(()=>t?n.filter(i=>i.timestamp>=t.minimum&&i.timestamp<=t.maximum):n,[n,t])}}const c1=({consoleModel:e,boundaries:t})=>e.entries.length?d.jsx("div",{className:"console-tab",children:d.jsx(a1,{name:"console",items:e.entries,isError:n=>n.isError,isWarning:n=>n.isWarning,render:n=>{const r=Qe(n.timestamp-t.minimum),i=d.jsx("span",{className:"console-time",children:r}),s=n.isError?" status-error":n.isWarning?" status-warning":" status-none",o=n.browserMessage||n.browserError?d.jsx("span",{className:"codicon codicon-browser"+s,title:"Browser message"}):d.jsx("span",{className:"codicon codicon-file"+s,title:"Runner message"});let l,a,u,c;const{browserMessage:f,browserError:p,nodeMessage:v}=n;if(f&&(l=f.location,a=f.body),p){const{error:y,value:w}=p;y?(a=y.message,c=y.stack):a=String(w)}return v&&(u=v.html),d.jsxs("div",{className:"console-line",children:[i,o,l&&d.jsx("span",{className:"console-location",children:l}),a&&d.jsx("span",{className:"console-line-message",children:a}),u&&d.jsx("span",{className:"console-line-message",dangerouslySetInnerHTML:{__html:u}}),c&&d.jsx("div",{className:"console-stack",children:c})]})}})}):d.jsx(rr,{text:"No console entries"});function d1(e){if(e.length===1)return Sh(e[0].preview);const t=typeof e[0].value=="string"&&e[0].value.includes("%"),n=t?e[0].value:"",r=t?e.slice(1):e;let i=0;const s=/%([%sdifoOc])/g;let o;const l=[];let a=[];l.push(d.jsx("span",{children:a}));let u=0;for(;(o=s.exec(n))!==null;){const c=n.substring(u,o.index);a.push(d.jsx("span",{children:c})),u=o.index+2;const f=o[0][1];if(f==="%")a.push(d.jsx("span",{children:"%"}));else if(f==="s"||f==="o"||f==="O"||f==="d"||f==="i"||f==="f"){const p=r[i++],v={};typeof(p==null?void 0:p.value)!="string"&&(v.color="var(--vscode-debugTokenExpression-number)"),a.push(d.jsx("span",{style:v,children:(p==null?void 0:p.preview)||""}))}else if(f==="c"){a=[];const p=r[i++],v=p?f1(p.preview):{};l.push(d.jsx("span",{style:v,children:a}))}}for(u<n.length&&a.push(d.jsx("span",{children:n.substring(u)}));i<r.length;i++){const c=r[i],f={};a.length&&a.push(d.jsx("span",{children:" "})),typeof(c==null?void 0:c.value)!="string"&&(f.color="var(--vscode-debugTokenExpression-number)"),a.push(d.jsx("span",{style:f,children:(c==null?void 0:c.preview)||""}))}return l}function Sh(e){return[d.jsx("span",{dangerouslySetInnerHTML:{__html:ti(e.trim())}})]}function f1(e){try{const t={},n=e.split(";");for(const r of n){const i=r.trim();if(!i)continue;let[s,o]=i.split(":");if(s=s.trim(),o=o.trim(),!h1(s))continue;const l=s.replace(/-([a-z])/g,a=>a[1].toUpperCase());t[l]=o}return t}catch{return{}}}function h1(e){return["background","border","color","font","line","margin","padding","text"].some(n=>e.startsWith(n))}const _h=({noShadow:e,children:t,noMinHeight:n})=>d.jsx("div",{className:"toolbar"+(e?" no-shadow":"")+(n?" no-min-height":""),children:t}),Ml=({tabs:e,selectedTab:t,setSelectedTab:n,leftToolbar:r,rightToolbar:i,dataTestId:s,mode:o})=>(o||(o="default"),d.jsx("div",{className:"tabbed-pane","data-testid":s,children:d.jsxs("div",{className:"vbox",children:[d.jsxs(_h,{children:[r&&d.jsxs("div",{style:{flex:"none",display:"flex",margin:"0 4px",alignItems:"center"},children:[...r]}),o==="default"&&d.jsx("div",{style:{flex:"auto",display:"flex",height:"100%",overflow:"hidden"},children:[...e.map(l=>d.jsx(Eh,{id:l.id,title:l.title,count:l.count,errorCount:l.errorCount,selected:t===l.id,onSelect:n}))]}),o==="select"&&d.jsx("div",{style:{flex:"auto",display:"flex",height:"100%",overflow:"hidden"},children:d.jsx("select",{style:{width:"100%",background:"none",cursor:"pointer"},onChange:l=>{n(e[l.currentTarget.selectedIndex].id)},children:e.map(l=>{let a="";return l.count===1?a=" 🔵":l.count&&(a=` 🔵✖️${l.count}`),l.errorCount===1?a=" 🔴":l.errorCount&&(a=` 🔴✖️${l.errorCount}`),d.jsxs("option",{value:l.id,selected:l.id===t,children:[l.title,a]})})})}),i&&d.jsxs("div",{style:{flex:"none",display:"flex",alignItems:"center"},children:[...i]})]}),e.map(l=>{const a="tab-content tab-"+l.id;if(l.component)return d.jsx("div",{className:a,style:{display:t===l.id?"inherit":"none"},children:l.component},l.id);if(t===l.id)return d.jsx("div",{className:a,children:l.render()},l.id)})]})})),Eh=({id:e,title:t,count:n,errorCount:r,selected:i,onSelect:s})=>d.jsxs("div",{className:"tabbed-pane-tab "+(i?"selected":""),onClick:()=>s(e),title:t,children:[d.jsx("div",{className:"tabbed-pane-tab-label",children:t}),!!n&&d.jsx("div",{className:"tabbed-pane-tab-counter",children:n}),!!r&&d.jsx("div",{className:"tabbed-pane-tab-counter error",children:r})]},e),p1="modulepreload",m1=function(e,t){return new URL(e,t).href},cc={},g1=function(t,n,r){let i=Promise.resolve();if(n&&n.length>0){const s=document.getElementsByTagName("link");i=Promise.all(n.map(o=>{if(o=m1(o,r),o in cc)return;cc[o]=!0;const l=o.endsWith(".css"),a=l?'[rel="stylesheet"]':"";if(!!r)for(let f=s.length-1;f>=0;f--){const p=s[f];if(p.href===o&&(!l||p.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${o}"]${a}`))return;const c=document.createElement("link");if(c.rel=l?"stylesheet":p1,l||(c.as="script",c.crossOrigin=""),c.href=o,document.head.appendChild(c),l)return new Promise((f,p)=>{c.addEventListener("load",f),c.addEventListener("error",()=>p(new Error(`Unable to preload CSS for ${o}`)))})}))}return i.then(()=>t()).catch(s=>{const o=new Event("vite:preloadError",{cancelable:!0});if(o.payload=s,window.dispatchEvent(o),!o.defaultPrevented)throw s})},Xs=({text:e,language:t,readOnly:n,highlight:r,revealLine:i,lineNumbers:s,isFocused:o,focusOnChange:l,wrapLines:a,onChange:u})=>{const[c,f]=pn(),[p]=L.useState(g1(()=>import("./codeMirrorModule-0bpaqixv.js"),__vite__mapDeps([0,1]),import.meta.url).then(x=>x.default)),v=L.useRef(null),[y,w]=L.useState();return L.useEffect(()=>{(async()=>{var _,N;const x=await p,h=f.current;if(!h)return;let m="";if(t==="javascript"&&(m="javascript"),t==="python"&&(m="python"),t==="java"&&(m="text/x-java"),t==="csharp"&&(m="text/x-csharp"),t==="html"&&(m="htmlmixed"),t==="css"&&(m="css"),v.current&&m===v.current.cm.getOption("mode")&&!!n===v.current.cm.getOption("readOnly")&&s===v.current.cm.getOption("lineNumbers")&&a===v.current.cm.getOption("lineWrapping"))return;(N=(_=v.current)==null?void 0:_.cm)==null||N.getWrapperElement().remove();const g=x(h,{value:"",mode:m,readOnly:!!n,lineNumbers:s,lineWrapping:a});return v.current={cm:g},o&&g.focus(),w(g),g})()},[p,y,f,t,s,a,n,o]),L.useEffect(()=>{v.current&&v.current.cm.setSize(c.width,c.height)},[c]),L.useLayoutEffect(()=>{var m;if(!y)return;let x=!1;if(y.getValue()!==e&&(y.setValue(e),x=!0,l&&(y.execCommand("selectAll"),y.focus())),x||JSON.stringify(r)!==JSON.stringify(v.current.highlight)){for(const _ of v.current.highlight||[])y.removeLineClass(_.line-1,"wrap");for(const _ of r||[])y.addLineClass(_.line-1,"wrap",`source-line-${_.type}`);for(const _ of v.current.widgets||[])y.removeLineWidget(_);const g=[];for(const _ of r||[]){if(_.type!=="error")continue;const N=(m=v.current)==null?void 0:m.cm.getLine(_.line-1);if(N){const C=document.createElement("div");C.className="source-line-error-underline",C.innerHTML="&nbsp;".repeat(N.length||1),g.push(y.addLineWidget(_.line,C,{above:!0,coverGutter:!1}))}const k=document.createElement("div");k.innerHTML=ti(_.message||""),k.className="source-line-error-widget",g.push(y.addLineWidget(_.line,k,{above:!0,coverGutter:!1}))}v.current.highlight=r,v.current.widgets=g}typeof i=="number"&&v.current.cm.lineCount()>=i&&y.scrollIntoView({line:Math.max(0,i-1),ch:0},50);let h;return u&&(h=()=>u(y.getValue()),y.on("change",h)),()=>{h&&y.off("change",h)}},[y,e,r,i,l,u]),d.jsx("div",{className:"cm-wrapper",ref:f})},v1=({resource:e,onClose:t})=>{const[n,r]=L.useState("request");return d.jsx(Ml,{dataTestId:"network-request-details",leftToolbar:[d.jsx(Bn,{icon:"close",title:"Close",onClick:t})],tabs:[{id:"request",title:"Request",render:()=>d.jsx(y1,{resource:e})},{id:"response",title:"Response",render:()=>d.jsx(w1,{resource:e})},{id:"body",title:"Body",render:()=>d.jsx(x1,{resource:e})}],selectedTab:n,setSelectedTab:r})},y1=({resource:e})=>{const[t,n]=L.useState(null);return L.useEffect(()=>{(async()=>{if(e.request.postData){const i=e.request.headers.find(l=>l.name==="Content-Type"),s=i?i.value:"",o=kh(s);if(e.request.postData._sha1){const l=await fetch(`sha1/${e.request.postData._sha1}`);n({text:jl(await l.text(),s),language:o})}else n({text:jl(e.request.postData.text,s),language:o})}})()},[e]),d.jsxs("div",{className:"network-request-details-tab",children:[d.jsx("div",{className:"network-request-details-header",children:"URL"}),d.jsx("div",{className:"network-request-details-url",children:e.request.url}),d.jsx("div",{className:"network-request-details-header",children:"Request Headers"}),d.jsx("div",{className:"network-request-details-headers",children:e.request.headers.map(r=>`${r.name}: ${r.value}`).join(`
`)}),t&&d.jsx("div",{className:"network-request-details-header",children:"Request Body"}),t&&d.jsx(Xs,{text:t.text,language:t.language,readOnly:!0,lineNumbers:!0})]})},w1=({resource:e})=>d.jsxs("div",{className:"network-request-details-tab",children:[d.jsx("div",{className:"network-request-details-header",children:"Response Headers"}),d.jsx("div",{className:"network-request-details-headers",children:e.response.headers.map(t=>`${t.name}: ${t.value}`).join(`
`)})]}),x1=({resource:e})=>{const[t,n]=L.useState(null);return L.useEffect(()=>{(async()=>{if(e.response.content._sha1){const i=e.response.content.mimeType.includes("image"),s=await fetch(`sha1/${e.response.content._sha1}`);if(i){const o=await s.blob(),l=new FileReader,a=new Promise(u=>l.onload=u);l.readAsDataURL(o),n({dataUrl:(await a).target.result})}else{const o=jl(await s.text(),e.response.content.mimeType),l=kh(e.response.content.mimeType);n({text:o,language:l})}}})()},[e]),d.jsxs("div",{className:"network-request-details-tab",children:[!e.response.content._sha1&&d.jsx("div",{children:"Response body is not available for this request."}),t&&t.dataUrl&&d.jsx("img",{draggable:"false",src:t.dataUrl}),t&&t.text&&d.jsx(Xs,{text:t.text,language:t.language,readOnly:!0,lineNumbers:!0})]})};function jl(e,t){if(e===null)return"Loading...";const n=e;if(n==="")return"<Empty>";if(t.includes("application/json"))try{return JSON.stringify(JSON.parse(n),null,2)}catch{return n}return t.includes("application/x-www-form-urlencoded")?decodeURIComponent(n):n}function kh(e){if(e.includes("javascript")||e.includes("json"))return"javascript";if(e.includes("html"))return"html";if(e.includes("css"))return"css"}const Th=({cursor:e,onPaneMouseMove:t,onPaneMouseUp:n,onPaneDoubleClick:r})=>(zt.useEffect(()=>{const i=document.createElement("div");return i.style.position="fixed",i.style.top="0",i.style.right="0",i.style.bottom="0",i.style.left="0",i.style.zIndex="9999",i.style.cursor=e,document.body.appendChild(i),t&&i.addEventListener("mousemove",t),n&&i.addEventListener("mouseup",n),r&&document.body.addEventListener("dblclick",r),()=>{t&&i.removeEventListener("mousemove",t),n&&i.removeEventListener("mouseup",n),r&&document.body.removeEventListener("dblclick",r),document.body.removeChild(i)}},[e,t,n,r]),d.jsx(d.Fragment,{})),S1={position:"absolute",top:0,right:0,bottom:0,left:0},Ch=({orientation:e,offsets:t,setOffsets:n,resizerColor:r,resizerWidth:i,minColumnWidth:s})=>{const o=s||0,[l,a]=zt.useState(null),[u,c]=pn(),f={position:"absolute",right:e==="horizontal"?void 0:0,bottom:e==="horizontal"?0:void 0,width:e==="horizontal"?7:void 0,height:e==="horizontal"?void 0:7,borderTopWidth:e==="horizontal"?void 0:(7-i)/2,borderRightWidth:e==="horizontal"?(7-i)/2:void 0,borderBottomWidth:e==="horizontal"?void 0:(7-i)/2,borderLeftWidth:e==="horizontal"?(7-i)/2:void 0,borderColor:"transparent",borderStyle:"solid",cursor:e==="horizontal"?"ew-resize":"ns-resize"};return d.jsxs("div",{style:{position:"absolute",top:0,right:0,bottom:0,left:-(7-i)/2,zIndex:1e3,pointerEvents:"none"},ref:c,children:[!!l&&d.jsx(Th,{cursor:e==="horizontal"?"ew-resize":"ns-resize",onPaneMouseUp:()=>a(null),onPaneMouseMove:p=>{if(!p.buttons)a(null);else if(l){const v=e==="horizontal"?p.clientX-l.clientX:p.clientY-l.clientY,y=l.offset+v,w=l.index>0?t[l.index-1]:0,x=e==="horizontal"?u.width:u.height,h=Math.min(Math.max(w+o,y),x-o)-t[l.index];for(let m=l.index;m<t.length;++m)t[m]=t[m]+h;n([...t])}}}),t.map((p,v)=>d.jsx("div",{style:{...f,top:e==="horizontal"?0:p,left:e==="horizontal"?p:0,pointerEvents:"initial"},onMouseDown:y=>a({clientX:y.clientX,clientY:y.clientY,offset:p,index:v}),children:d.jsx("div",{style:{...S1,background:r}})}))]})};function _1(e){const t=[];for(let s=0;s<e.columns.length-1;++s){const o=e.columns[s];t[s]=(t[s-1]||0)+e.columnWidth(o)}const[n,r]=L.useState(t),i=L.useCallback(s=>{var o,l;(l=e.setSorting)==null||l.call(e,{by:s,negate:((o=e.sorting)==null?void 0:o.by)===s?!e.sorting.negate:!1})},[e]);return d.jsxs("div",{className:`grid-view ${e.name}-grid-view`,children:[d.jsx(Ch,{orientation:"horizontal",offsets:n,setOffsets:r,resizerColor:"var(--vscode-panel-border)",resizerWidth:1,minColumnWidth:25}),d.jsxs("div",{className:"vbox",children:[d.jsx("div",{className:"grid-view-header",children:e.columns.map((s,o)=>d.jsxs("div",{className:"grid-view-header-cell "+E1(s,e.sorting),style:{width:n[o]-(n[o-1]||0)},onClick:()=>e.setSorting&&i(s),children:[d.jsx("span",{className:"grid-view-header-cell-title",children:e.columnTitle(s)}),d.jsx("span",{className:"codicon codicon-triangle-up"}),d.jsx("span",{className:"codicon codicon-triangle-down"})]}))}),d.jsx(ai,{name:e.name,items:e.items,id:e.id,render:(s,o)=>d.jsx(d.Fragment,{children:e.columns.map((l,a)=>{const{body:u,title:c}=e.render(s,l,o);return d.jsx("div",{className:`grid-view-cell grid-view-column-${String(l)}`,title:c,style:{width:n[a]-(n[a-1]||0)},children:u})})}),icon:e.icon,indent:e.indent,isError:e.isError,isWarning:e.isWarning,isInfo:e.isInfo,selectedItem:e.selectedItem,onAccepted:e.onAccepted,onSelected:e.onSelected,onLeftArrow:e.onLeftArrow,onRightArrow:e.onRightArrow,onHighlighted:e.onHighlighted,onIconClicked:e.onIconClicked,noItemsMessage:e.noItemsMessage,dataTestId:e.dataTestId,noHighlightOnHover:e.noHighlightOnHover})]})]})}function E1(e,t){return e===(t==null?void 0:t.by)?" filter-"+(t.negate?"negative":"positive"):""}const k1=_1;function T1(e,t){return{resources:L.useMemo(()=>((e==null?void 0:e.resources)||[]).filter(s=>t?!!s._monotonicTime&&s._monotonicTime>=t.minimum&&s._monotonicTime<=t.maximum:!0),[e,t])}}const C1=({boundaries:e,networkModel:t,onEntryHovered:n})=>{const[r,i]=L.useState(void 0),[s,o]=L.useState(void 0),{renderedEntries:l}=L.useMemo(()=>{const u=t.resources.map(c=>A1(c,e));return r&&j1(u,r),{renderedEntries:u}},[t.resources,r,e]);if(!t.resources.length)return d.jsx(rr,{text:"No network calls"});const a=d.jsx(k1,{name:"network",items:l,selectedItem:s,onSelected:u=>o(u),onHighlighted:u=>n(u==null?void 0:u.resource),columns:s?["name"]:["name","method","status","contentType","duration","size","start","route"],columnTitle:N1,columnWidth:b1,isError:u=>u.status.code>=400,isInfo:u=>!!u.route,render:(u,c)=>L1(u,c),sorting:r,setSorting:i});return d.jsxs(d.Fragment,{children:[!s&&a,s&&d.jsxs(Es,{sidebarSize:200,sidebarIsFirst:!0,orientation:"horizontal",children:[d.jsx(v1,{resource:s.resource,onClose:()=>o(void 0)}),a]})]})},N1=e=>e==="name"?"Name":e==="method"?"Method":e==="status"?"Status":e==="contentType"?"Content Type":e==="duration"?"Duration":e==="size"?"Size":e==="start"?"Start":e==="route"?"Route":"",b1=e=>e==="name"?200:e==="method"||e==="status"?60:e==="contentType"?200:100,L1=(e,t)=>t==="name"?{body:e.name.name,title:e.name.url}:t==="method"?{body:e.method}:t==="status"?{body:e.status.code>0?e.status.code:"",title:e.status.text}:t==="contentType"?{body:e.contentType}:t==="duration"?{body:Qe(e.duration)}:t==="size"?{body:nm(e.size)}:t==="start"?{body:Qe(e.start)}:t==="route"?{body:e.route}:{body:""},A1=(e,t)=>{const n=M1(e);let r;try{const o=new URL(e.request.url);r=o.pathname.substring(o.pathname.lastIndexOf("/")+1),r||(r=o.host)}catch{r=e.request.url}let i=e.response.content.mimeType;const s=i.match(/^(.*);\s*charset=.*$/);return s&&(i=s[1]),{name:{name:r,url:e.request.url},method:e.request.method,status:{code:e.response.status,text:e.response.statusText},contentType:i,duration:e.time,size:e.response._transferSize>0?e.response._transferSize:e.response.bodySize,start:e._monotonicTime-t.minimum,route:n,resource:e}};function M1(e){return e._wasAborted?"aborted":e._wasContinued?"continued":e._wasFulfilled?"fulfilled":e._apiRequest?"api":""}function j1(e,t){const n=I1(t==null?void 0:t.by);n&&e.sort(n),t.negate&&e.reverse()}function I1(e){if(e==="start")return(t,n)=>t.start-n.start;if(e==="duration")return(t,n)=>t.duration-n.duration;if(e==="status")return(t,n)=>t.status.code-n.status.code;if(e==="method")return(t,n)=>{const r=t.method,i=n.method;return r.localeCompare(i)};if(e==="size")return(t,n)=>t.size-n.size;if(e==="contentType")return(t,n)=>t.contentType.localeCompare(n.contentType);if(e==="name")return(t,n)=>t.name.name.localeCompare(n.name.name);if(e==="route")return(t,n)=>t.route.localeCompare(n.route)}const dc={queryAll(e,t){t.startsWith("/")&&e.nodeType!==Node.DOCUMENT_NODE&&(t="."+t);const n=[],r=e.ownerDocument||e;if(!r)return n;const i=r.evaluate(t,e,null,XPathResult.ORDERED_NODE_ITERATOR_TYPE);for(let s=i.iterateNext();s;s=i.iterateNext())s.nodeType===Node.ELEMENT_NODE&&n.push(s);return n}};let Nh="";function P1(e){Nh=e}function Qs(e,t){for(;t;){if(e.contains(t))return!0;t=Lh(t)}return!1}function Ee(e){if(e.parentElement)return e.parentElement;if(e.parentNode&&e.parentNode.nodeType===11&&e.parentNode.host)return e.parentNode.host}function bh(e){let t=e;for(;t.parentNode;)t=t.parentNode;if(t.nodeType===11||t.nodeType===9)return t}function Lh(e){for(;e.parentElement;)e=e.parentElement;return Ee(e)}function Cr(e,t,n){for(;e;){const r=e.closest(t);if(n&&r!==n&&(r!=null&&r.contains(n)))return;if(r)return r;e=Lh(e)}}function ir(e,t){return e.ownerDocument&&e.ownerDocument.defaultView?e.ownerDocument.defaultView.getComputedStyle(e,t):void 0}function Ah(e,t){if(t=t??ir(e),!t)return!0;if(Element.prototype.checkVisibility&&Nh!=="webkit"){if(!e.checkVisibility({checkOpacity:!1,checkVisibilityCSS:!1}))return!1}else{const n=e.closest("details,summary");if(n!==e&&(n==null?void 0:n.nodeName)==="DETAILS"&&!n.open)return!1}return t.visibility==="visible"}function Cs(e){const t=ir(e);if(!t)return!0;if(t.display==="contents"){for(let r=e.firstChild;r;r=r.nextSibling)if(r.nodeType===1&&Cs(r)||r.nodeType===3&&Mh(r))return!0;return!1}if(!Ah(e,t))return!1;const n=e.getBoundingClientRect();return n.width>0&&n.height>0}function Mh(e){const t=e.ownerDocument.createRange();t.selectNode(e);const n=t.getBoundingClientRect();return n.width>0&&n.height>0}function fc(e){return e.hasAttribute("aria-label")||e.hasAttribute("aria-labelledby")}const hc="article:not([role]), aside:not([role]), main:not([role]), nav:not([role]), section:not([role]), [role=article], [role=complementary], [role=main], [role=navigation], [role=region]",R1=["aria-atomic","aria-busy","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-dropeffect","aria-errormessage","aria-flowto","aria-grabbed","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-live","aria-owns","aria-relevant","aria-roledescription"];function jh(e){return R1.some(t=>e.hasAttribute(t))}const Co={A:e=>e.hasAttribute("href")?"link":null,AREA:e=>e.hasAttribute("href")?"link":null,ARTICLE:()=>"article",ASIDE:()=>"complementary",BLOCKQUOTE:()=>"blockquote",BUTTON:()=>"button",CAPTION:()=>"caption",CODE:()=>"code",DATALIST:()=>"listbox",DD:()=>"definition",DEL:()=>"deletion",DETAILS:()=>"group",DFN:()=>"term",DIALOG:()=>"dialog",DT:()=>"term",EM:()=>"emphasis",FIELDSET:()=>"group",FIGURE:()=>"figure",FOOTER:e=>Cr(e,hc)?null:"contentinfo",FORM:e=>fc(e)?"form":null,H1:()=>"heading",H2:()=>"heading",H3:()=>"heading",H4:()=>"heading",H5:()=>"heading",H6:()=>"heading",HEADER:e=>Cr(e,hc)?null:"banner",HR:()=>"separator",HTML:()=>"document",IMG:e=>e.getAttribute("alt")===""&&!jh(e)&&Number.isNaN(Number(String(e.getAttribute("tabindex"))))?"presentation":"img",INPUT:e=>{const t=e.type.toLowerCase();if(t==="search")return e.hasAttribute("list")?"combobox":"searchbox";if(["email","tel","text","url",""].includes(t)){const n=Ks(e,e.getAttribute("list"))[0];return n&&n.tagName==="DATALIST"?"combobox":"textbox"}return t==="hidden"?"":{button:"button",checkbox:"checkbox",image:"button",number:"spinbutton",radio:"radio",range:"slider",reset:"button",submit:"button"}[t]||"textbox"},INS:()=>"insertion",LI:()=>"listitem",MAIN:()=>"main",MARK:()=>"mark",MATH:()=>"math",MENU:()=>"list",METER:()=>"meter",NAV:()=>"navigation",OL:()=>"list",OPTGROUP:()=>"group",OPTION:()=>"option",OUTPUT:()=>"status",P:()=>"paragraph",PROGRESS:()=>"progressbar",SECTION:e=>fc(e)?"region":null,SELECT:e=>e.hasAttribute("multiple")||e.size>1?"listbox":"combobox",STRONG:()=>"strong",SUB:()=>"subscript",SUP:()=>"superscript",SVG:()=>"img",TABLE:()=>"table",TBODY:()=>"rowgroup",TD:e=>{const t=Cr(e,"table"),n=t?Ns(t):"";return n==="grid"||n==="treegrid"?"gridcell":"cell"},TEXTAREA:()=>"textbox",TFOOT:()=>"rowgroup",TH:e=>{if(e.getAttribute("scope")==="col")return"columnheader";if(e.getAttribute("scope")==="row")return"rowheader";const t=Cr(e,"table"),n=t?Ns(t):"";return n==="grid"||n==="treegrid"?"gridcell":"cell"},THEAD:()=>"rowgroup",TIME:()=>"time",TR:()=>"row",UL:()=>"list"},$1={DD:["DL","DIV"],DIV:["DL"],DT:["DL","DIV"],LI:["OL","UL"],TBODY:["TABLE"],TD:["TR"],TFOOT:["TABLE"],TH:["TR"],THEAD:["TABLE"],TR:["THEAD","TBODY","TFOOT","TABLE"]};function pc(e){var r;const t=((r=Co[e.tagName.toUpperCase()])==null?void 0:r.call(Co,e))||"";if(!t)return null;let n=e;for(;n;){const i=Ee(n),s=$1[n.tagName];if(!s||!i||!s.includes(i.tagName))break;const o=Ns(i);if((o==="none"||o==="presentation")&&!Ih(i))return o;n=i}return t}const O1=["alert","alertdialog","application","article","banner","blockquote","button","caption","cell","checkbox","code","columnheader","combobox","command","complementary","composite","contentinfo","definition","deletion","dialog","directory","document","emphasis","feed","figure","form","generic","grid","gridcell","group","heading","img","input","insertion","landmark","link","list","listbox","listitem","log","main","marquee","math","meter","menu","menubar","menuitem","menuitemcheckbox","menuitemradio","navigation","none","note","option","paragraph","presentation","progressbar","radio","radiogroup","range","region","roletype","row","rowgroup","rowheader","scrollbar","search","searchbox","section","sectionhead","select","separator","slider","spinbutton","status","strong","structure","subscript","superscript","switch","tab","table","tablist","tabpanel","term","textbox","time","timer","toolbar","tooltip","tree","treegrid","treeitem","widget","window"],z1=["command","composite","input","landmark","range","roletype","section","sectionhead","select","structure","widget","window"],D1=O1.filter(e=>!z1.includes(e));function Ns(e){return(e.getAttribute("role")||"").split(" ").map(n=>n.trim()).find(n=>D1.includes(n))||null}function Ih(e){return!jh(e)}function $e(e){const t=Ns(e);return!t||(t==="none"||t==="presentation")&&Ih(e)?pc(e):t}function Ph(e){return e===null?void 0:e.toLowerCase()==="true"}function Oa(e){if(["STYLE","SCRIPT","NOSCRIPT","TEMPLATE"].includes(e.tagName))return!0;const t=ir(e),n=e.nodeName==="SLOT";if((t==null?void 0:t.display)==="contents"&&!n){for(let i=e.firstChild;i;i=i.nextSibling)if(i.nodeType===1&&!Oa(i)||i.nodeType===3&&Mh(i))return!1;return!0}return!(e.nodeName==="OPTION"&&!!e.closest("select"))&&!n&&!Ah(e,t)?!0:Rh(e)}function Rh(e){let t=jt==null?void 0:jt.get(e);if(t===void 0){if(t=!1,e.parentElement&&e.parentElement.shadowRoot&&!e.assignedSlot&&(t=!0),!t){const n=ir(e);t=!n||n.display==="none"||Ph(e.getAttribute("aria-hidden"))===!0}if(!t){const n=Ee(e);n&&(t=Rh(n))}jt==null||jt.set(e,t)}return t}function Ks(e,t){if(!t)return[];const n=bh(e);if(!n)return[];try{const r=t.split(" ").filter(s=>!!s),i=new Set;for(const s of r){const o=n.querySelector("#"+CSS.escape(s));o&&i.add(o)}return[...i]}catch{return[]}}function H1(e){return e.replace(/\r\n/g,`
`).replace(/\u00A0/g," ").replace(/\s\s+/g," ").trim()}function mc(e,t){const n=[...e.querySelectorAll(t)];for(const r of Ks(e,e.getAttribute("aria-owns")))r.matches(t)&&n.push(r),n.push(...r.querySelectorAll(t));return n}function gc(e,t){const n=t==="::before"?Fa:Ua;if(n!=null&&n.has(e))return(n==null?void 0:n.get(e))||"";const r=ir(e,t),i=F1(r);return n&&n.set(e,i),i}function F1(e){if(!e)return"";const t=e.content;if(t[0]==="'"&&t[t.length-1]==="'"||t[0]==='"'&&t[t.length-1]==='"'){const n=t.substring(1,t.length-1);return(e.display||"inline")!=="inline"?" "+n+" ":n}return""}function $h(e){const t=e.getAttribute("aria-labelledby");return t===null?null:Ks(e,t)}function U1(e,t){const n=["button","cell","checkbox","columnheader","gridcell","heading","link","menuitem","menuitemcheckbox","menuitemradio","option","radio","row","rowheader","switch","tab","tooltip","treeitem"].includes(e),r=t&&["","caption","code","contentinfo","definition","deletion","emphasis","insertion","list","listitem","mark","none","paragraph","presentation","region","row","rowgroup","section","strong","subscript","superscript","table","term","time"].includes(e);return n||r}function za(e,t){const n=t?Ha:Da;let r=n==null?void 0:n.get(e);return r===void 0&&(r="",["caption","code","definition","deletion","emphasis","generic","insertion","mark","paragraph","presentation","strong","subscript","suggestion","superscript","term","time"].includes($e(e)||"")||(r=H1(wt(e,{includeHidden:t,visitedElements:new Set,embeddedInLabelledBy:"none",embeddedInLabel:"none",embeddedInTextAlternativeElement:!1,embeddedInTargetElement:"self"}))),n==null||n.set(e,r)),r}function wt(e,t){if(t.visitedElements.has(e))return"";const n={...t,embeddedInLabel:t.embeddedInLabel==="self"?"descendant":t.embeddedInLabel,embeddedInLabelledBy:t.embeddedInLabelledBy==="self"?"descendant":t.embeddedInLabelledBy,embeddedInTargetElement:t.embeddedInTargetElement==="self"?"descendant":t.embeddedInTargetElement};if(!t.includeHidden&&t.embeddedInLabelledBy!=="self"&&Oa(e))return t.visitedElements.add(e),"";const r=$h(e);if(t.embeddedInLabelledBy==="none"){const o=(r||[]).map(l=>wt(l,{...t,embeddedInLabelledBy:"self",embeddedInTargetElement:"none",embeddedInLabel:"none",embeddedInTextAlternativeElement:!1})).join(" ");if(o)return o}const i=$e(e)||"";if(t.embeddedInLabel!=="none"||t.embeddedInLabelledBy!=="none"){const o=[...e.labels||[]].includes(e),l=(r||[]).includes(e);if(!o&&!l){if(i==="textbox")return t.visitedElements.add(e),e.tagName==="INPUT"||e.tagName==="TEXTAREA"?e.value:e.textContent||"";if(["combobox","listbox"].includes(i)){t.visitedElements.add(e);let a;if(e.tagName==="SELECT")a=[...e.selectedOptions],!a.length&&e.options.length&&a.push(e.options[0]);else{const u=i==="combobox"?mc(e,"*").find(c=>$e(c)==="listbox"):e;a=u?mc(u,'[aria-selected="true"]').filter(c=>$e(c)==="option"):[]}return a.map(u=>wt(u,n)).join(" ")}if(["progressbar","scrollbar","slider","spinbutton","meter"].includes(i))return t.visitedElements.add(e),e.hasAttribute("aria-valuetext")?e.getAttribute("aria-valuetext")||"":e.hasAttribute("aria-valuenow")?e.getAttribute("aria-valuenow")||"":e.getAttribute("value")||"";if(["menu"].includes(i))return t.visitedElements.add(e),""}}const s=e.getAttribute("aria-label")||"";if(s.trim())return t.visitedElements.add(e),s;if(!["presentation","none"].includes(i)){if(e.tagName==="INPUT"&&["button","submit","reset"].includes(e.type)){t.visitedElements.add(e);const o=e.value||"";return o.trim()?o:e.type==="submit"?"Submit":e.type==="reset"?"Reset":e.getAttribute("title")||""}if(e.tagName==="INPUT"&&e.type==="image"){t.visitedElements.add(e);const o=e.labels||[];if(o.length&&t.embeddedInLabelledBy==="none")return bi(o,t);const l=e.getAttribute("alt")||"";if(l.trim())return l;const a=e.getAttribute("title")||"";return a.trim()?a:"Submit"}if(!r&&e.tagName==="BUTTON"){t.visitedElements.add(e);const o=e.labels||[];if(o.length)return bi(o,t)}if(!r&&e.tagName==="OUTPUT"){t.visitedElements.add(e);const o=e.labels||[];return o.length?bi(o,t):e.getAttribute("title")||""}if(!r&&(e.tagName==="TEXTAREA"||e.tagName==="SELECT"||e.tagName==="INPUT")){t.visitedElements.add(e);const o=e.labels||[];if(o.length)return bi(o,t);const l=e.tagName==="INPUT"&&["text","password","search","tel","email","url"].includes(e.type)||e.tagName==="TEXTAREA",a=e.getAttribute("placeholder")||"",u=e.getAttribute("title")||"";return!l||u?u:a}if(!r&&e.tagName==="FIELDSET"){t.visitedElements.add(e);for(let l=e.firstElementChild;l;l=l.nextElementSibling)if(l.tagName==="LEGEND")return wt(l,{...n,embeddedInTextAlternativeElement:!0});return e.getAttribute("title")||""}if(!r&&e.tagName==="FIGURE"){t.visitedElements.add(e);for(let l=e.firstElementChild;l;l=l.nextElementSibling)if(l.tagName==="FIGCAPTION")return wt(l,{...n,embeddedInTextAlternativeElement:!0});return e.getAttribute("title")||""}if(e.tagName==="IMG"){t.visitedElements.add(e);const o=e.getAttribute("alt")||"";return o.trim()?o:e.getAttribute("title")||""}if(e.tagName==="TABLE"){t.visitedElements.add(e);for(let l=e.firstElementChild;l;l=l.nextElementSibling)if(l.tagName==="CAPTION")return wt(l,{...n,embeddedInTextAlternativeElement:!0});const o=e.getAttribute("summary")||"";if(o)return o}if(e.tagName==="AREA"){t.visitedElements.add(e);const o=e.getAttribute("alt")||"";return o.trim()?o:e.getAttribute("title")||""}if(e.tagName.toUpperCase()==="SVG"||e.ownerSVGElement){t.visitedElements.add(e);for(let o=e.firstElementChild;o;o=o.nextElementSibling)if(o.tagName.toUpperCase()==="TITLE"&&o.ownerSVGElement)return wt(o,{...n,embeddedInLabelledBy:"self"})}if(e.ownerSVGElement&&e.tagName.toUpperCase()==="A"){const o=e.getAttribute("xlink:title")||"";if(o.trim())return t.visitedElements.add(e),o}}if(U1(i,t.embeddedInTargetElement==="descendant")||t.embeddedInLabelledBy!=="none"||t.embeddedInLabel!=="none"||t.embeddedInTextAlternativeElement){t.visitedElements.add(e);const o=[],l=(c,f)=>{var p;if(!(f&&c.assignedSlot))if(c.nodeType===1){const v=((p=ir(c))==null?void 0:p.display)||"inline";let y=wt(c,n);(v!=="inline"||c.nodeName==="BR")&&(y=" "+y+" "),o.push(y)}else c.nodeType===3&&o.push(c.textContent||"")};o.push(gc(e,"::before"));const a=e.nodeName==="SLOT"?e.assignedNodes():[];if(a.length)for(const c of a)l(c,!1);else{for(let c=e.firstChild;c;c=c.nextSibling)l(c,!0);if(e.shadowRoot)for(let c=e.shadowRoot.firstChild;c;c=c.nextSibling)l(c,!0);for(const c of Ks(e,e.getAttribute("aria-owns")))l(c,!0)}o.push(gc(e,"::after"));const u=o.join("");if(u.trim())return u}if(!["presentation","none"].includes(i)||e.tagName==="IFRAME"){t.visitedElements.add(e);const o=e.getAttribute("title")||"";if(o.trim())return o}return t.visitedElements.add(e),""}const Oh=["gridcell","option","row","tab","rowheader","columnheader","treeitem"];function V1(e){return e.tagName==="OPTION"?e.selected:Oh.includes($e(e)||"")?Ph(e.getAttribute("aria-selected"))===!0:!1}const zh=["checkbox","menuitemcheckbox","option","radio","switch","menuitemradio","treeitem"];function B1(e){const t=Dh(e,!0);return t==="error"?!1:t}function Dh(e,t){if(t&&e.tagName==="INPUT"&&e.indeterminate)return"mixed";if(e.tagName==="INPUT"&&["checkbox","radio"].includes(e.type))return e.checked;if(zh.includes($e(e)||"")){const n=e.getAttribute("aria-checked");return n==="true"?!0:t&&n==="mixed"?"mixed":!1}return"error"}const Hh=["button"];function W1(e){if(Hh.includes($e(e)||"")){const t=e.getAttribute("aria-pressed");if(t==="true")return!0;if(t==="mixed")return"mixed"}return!1}const Fh=["application","button","checkbox","combobox","gridcell","link","listbox","menuitem","row","rowheader","tab","treeitem","columnheader","menuitemcheckbox","menuitemradio","rowheader","switch"];function q1(e){if(e.tagName==="DETAILS")return e.open;if(Fh.includes($e(e)||"")){const t=e.getAttribute("aria-expanded");return t===null?"none":t==="true"}return"none"}const Uh=["heading","listitem","row","treeitem"];function X1(e){const t={H1:1,H2:2,H3:3,H4:4,H5:5,H6:6}[e.tagName];if(t)return t;if(Uh.includes($e(e)||"")){const n=e.getAttribute("aria-level"),r=n===null?Number.NaN:Number(n);if(Number.isInteger(r)&&r>=1)return r}return 0}const Q1=["application","button","composite","gridcell","group","input","link","menuitem","scrollbar","separator","tab","checkbox","columnheader","combobox","grid","listbox","menu","menubar","menuitemcheckbox","menuitemradio","option","radio","radiogroup","row","rowheader","searchbox","select","slider","spinbutton","switch","tablist","textbox","toolbar","tree","treegrid","treeitem"];function Vh(e){return["BUTTON","INPUT","SELECT","TEXTAREA","OPTION","OPTGROUP"].includes(e.tagName)&&(e.hasAttribute("disabled")||Bh(e))?!0:Wh(e)}function Bh(e){return e?e.tagName==="FIELDSET"&&e.hasAttribute("disabled")?!0:Bh(e.parentElement):!1}function Wh(e){if(!e)return!1;if(Q1.includes($e(e)||"")){const t=(e.getAttribute("aria-disabled")||"").toLowerCase();if(t==="true")return!0;if(t==="false")return!1}return Wh(Ee(e))}function bi(e,t){return[...e].map(n=>wt(n,{...t,embeddedInLabel:"self",embeddedInTextAlternativeElement:!1,embeddedInLabelledBy:"none",embeddedInTargetElement:"none"})).filter(n=>!!n).join(" ")}let Da,Ha,jt,Fa,Ua,qh=0;function Xh(){++qh,Da??(Da=new Map),Ha??(Ha=new Map),jt??(jt=new Map),Fa??(Fa=new Map),Ua??(Ua=new Map)}function Qh(){--qh||(Da=void 0,Ha=void 0,jt=void 0,Fa=void 0,Ua=void 0)}function Kh(e,t){for(const n of t.jsonPath)e!=null&&(e=e[n]);return Gh(e,t)}function Gh(e,t){const n=typeof e=="string"&&!t.caseSensitive?e.toUpperCase():e,r=typeof t.value=="string"&&!t.caseSensitive?t.value.toUpperCase():t.value;return t.op==="<truthy>"?!!n:t.op==="="?r instanceof RegExp?typeof n=="string"&&!!n.match(r):n===r:typeof n!="string"||typeof r!="string"?!1:t.op==="*="?n.includes(r):t.op==="^="?n.startsWith(r):t.op==="$="?n.endsWith(r):t.op==="|="?n===r||n.startsWith(r+"-"):t.op==="~="?n.split(" ").includes(r):!1}function Va(e){const t=e.ownerDocument;return e.nodeName==="SCRIPT"||e.nodeName==="NOSCRIPT"||e.nodeName==="STYLE"||t.head&&t.head.contains(e)}function Me(e,t){let n=e.get(t);if(n===void 0){if(n={full:"",normalized:"",immediate:[]},!Va(t)){let r="";if(t instanceof HTMLInputElement&&(t.type==="submit"||t.type==="button"))n={full:t.value,normalized:Fe(t.value),immediate:[t.value]};else{for(let i=t.firstChild;i;i=i.nextSibling)i.nodeType===Node.TEXT_NODE?(n.full+=i.nodeValue||"",r+=i.nodeValue||""):(r&&n.immediate.push(r),r="",i.nodeType===Node.ELEMENT_NODE&&(n.full+=Me(e,i).full));r&&n.immediate.push(r),t.shadowRoot&&(n.full+=Me(e,t.shadowRoot).full),n.full&&(n.normalized=Fe(n.full))}}e.set(t,n)}return n}function Gs(e,t,n){if(Va(t)||!n(Me(e,t)))return"none";for(let r=t.firstChild;r;r=r.nextSibling)if(r.nodeType===Node.ELEMENT_NODE&&n(Me(e,r)))return"selfAndChildren";return t.shadowRoot&&n(Me(e,t.shadowRoot))?"selfAndChildren":"self"}function Yh(e,t){const n=$h(t);if(n)return n.map(s=>Me(e,s));const r=t.getAttribute("aria-label");if(r!==null&&r.trim())return[{full:r,normalized:Fe(r),immediate:[r]}];const i=t.nodeName==="INPUT"&&t.type!=="hidden";if(["BUTTON","METER","OUTPUT","PROGRESS","SELECT","TEXTAREA"].includes(t.nodeName)||i){const s=t.labels;if(s)return[...s].map(o=>Me(e,o))}return[]}function vc(e){return e.displayName||e.name||"Anonymous"}function K1(e){if(e.type)switch(typeof e.type){case"function":return vc(e.type);case"string":return e.type;case"object":return e.type.displayName||(e.type.render?vc(e.type.render):"")}if(e._currentElement){const t=e._currentElement.type;if(typeof t=="string")return t;if(typeof t=="function")return t.displayName||t.name||"Anonymous"}return""}function G1(e){var t;return e.key??((t=e._currentElement)==null?void 0:t.key)}function Y1(e){if(e.child){const n=[];for(let r=e.child;r;r=r.sibling)n.push(r);return n}if(!e._currentElement)return[];const t=n=>{var i;const r=(i=n._currentElement)==null?void 0:i.type;return typeof r=="function"||typeof r=="string"};if(e._renderedComponent){const n=e._renderedComponent;return t(n)?[n]:[]}return e._renderedChildren?[...Object.values(e._renderedChildren)].filter(t):[]}function J1(e){var r;const t=e.memoizedProps||((r=e._currentElement)==null?void 0:r.props);if(!t||typeof t=="string")return t;const n={...t};return delete n.children,n}function Jh(e){var r;const t={key:G1(e),name:K1(e),children:Y1(e).map(Jh),rootElements:[],props:J1(e)},n=e.stateNode||e._hostNode||((r=e._renderedComponent)==null?void 0:r._hostNode);if(n instanceof Element)t.rootElements.push(n);else for(const i of t.children)t.rootElements.push(...i.rootElements);return t}function Zh(e,t,n=[]){t(e)&&n.push(e);for(const r of e.children)Zh(r,t,n);return n}function ep(e,t=[]){const r=(e.ownerDocument||e).createTreeWalker(e,NodeFilter.SHOW_ELEMENT);do{const i=r.currentNode,s=i,o=Object.keys(s).find(a=>a.startsWith("__reactContainer")&&s[a]!==null);if(o)t.push(s[o].stateNode.current);else{const a="_reactRootContainer";s.hasOwnProperty(a)&&s[a]!==null&&t.push(s[a]._internalRoot.current)}if(i instanceof Element&&i.hasAttribute("data-reactroot"))for(const a of Object.keys(i))(a.startsWith("__reactInternalInstance")||a.startsWith("__reactFiber"))&&t.push(i[a]);const l=i instanceof Element?i.shadowRoot:null;l&&ep(l,t)}while(r.nextNode());return t}const Z1={queryAll(e,t){const{name:n,attributes:r}=ln(t,!1),o=ep(e.ownerDocument||e).map(a=>Jh(a)).map(a=>Zh(a,u=>{const c=u.props??{};if(u.key!==void 0&&(c.key=u.key),n&&u.name!==n||u.rootElements.some(f=>!Qs(e,f)))return!1;for(const f of r)if(!Kh(c,f))return!1;return!0})).flat(),l=new Set;for(const a of o)for(const u of a.rootElements)l.add(u);return[...l]}};function tp(e,t){const n=e.replace(/^[a-zA-Z]:/,"").replace(/\\/g,"/");let r=n.substring(n.lastIndexOf("/")+1);return t&&r.endsWith(t)&&(r=r.substring(0,r.length-t.length)),r}function ev(e,t){return t?t.toUpperCase():""}const tv=/(?:^|[-_/])(\w)/g,np=e=>e&&e.replace(tv,ev);function nv(e){function t(c){const f=c.name||c._componentTag||c.__playwright_guessedName;if(f)return f;const p=c.__file;if(p)return np(tp(p,".vue"))}function n(c,f){return c.type.__playwright_guessedName=f,f}function r(c){var p,v,y,w;const f=t(c.type||{});if(f)return f;if(c.root===c)return"Root";for(const x in(v=(p=c.parent)==null?void 0:p.type)==null?void 0:v.components)if(((y=c.parent)==null?void 0:y.type.components[x])===c.type)return n(c,x);for(const x in(w=c.appContext)==null?void 0:w.components)if(c.appContext.components[x]===c.type)return n(c,x);return"Anonymous Component"}function i(c){return c._isBeingDestroyed||c.isUnmounted}function s(c){return c.subTree.type.toString()==="Symbol(Fragment)"}function o(c){const f=[];return c.component&&f.push(c.component),c.suspense&&f.push(...o(c.suspense.activeBranch)),Array.isArray(c.children)&&c.children.forEach(p=>{p.component?f.push(p.component):f.push(...o(p))}),f.filter(p=>{var v;return!i(p)&&!((v=p.type.devtools)!=null&&v.hide)})}function l(c){return s(c)?a(c.subTree):[c.subTree.el]}function a(c){if(!c.children)return[];const f=[];for(let p=0,v=c.children.length;p<v;p++){const y=c.children[p];y.component?f.push(...l(y.component)):y.el&&f.push(y.el)}return f}function u(c){return{name:r(c),children:o(c.subTree).map(u),rootElements:l(c),props:c.props}}return u(e)}function rv(e){function t(s){const o=s.displayName||s.name||s._componentTag;if(o)return o;const l=s.__file;if(l)return np(tp(l,".vue"))}function n(s){const o=t(s.$options||s.fnOptions||{});return o||(s.$root===s?"Root":"Anonymous Component")}function r(s){return s.$children?s.$children:Array.isArray(s.subTree.children)?s.subTree.children.filter(o=>!!o.component).map(o=>o.component):[]}function i(s){return{name:n(s),children:r(s).map(i),rootElements:[s.$el],props:s._props}}return i(e)}function rp(e,t,n=[]){t(e)&&n.push(e);for(const r of e.children)rp(r,t,n);return n}function ip(e,t=[]){const r=(e.ownerDocument||e).createTreeWalker(e,NodeFilter.SHOW_ELEMENT),i=new Set;do{const s=r.currentNode;s.__vue__&&i.add(s.__vue__.$root),s.__vue_app__&&s._vnode&&s._vnode.component&&t.push({root:s._vnode.component,version:3});const o=s instanceof Element?s.shadowRoot:null;o&&ip(o,t)}while(r.nextNode());for(const s of i)t.push({version:2,root:s});return t}const iv={queryAll(e,t){const n=e.ownerDocument||e,{name:r,attributes:i}=ln(t,!1),l=ip(n).map(u=>u.version===3?nv(u.root):rv(u.root)).map(u=>rp(u,c=>{if(r&&c.name!==r||c.rootElements.some(f=>!Qs(e,f)))return!1;for(const f of i)if(!Kh(c.props,f))return!1;return!0})).flat(),a=new Set;for(const u of l)for(const c of u.rootElements)a.add(c);return[...a]}},sp=["selected","checked","pressed","expanded","level","disabled","name","include-hidden"];sp.sort();function yr(e,t,n){if(!t.includes(n))throw new Error(`"${e}" attribute is only supported for roles: ${t.slice().sort().map(r=>`"${r}"`).join(", ")}`)}function wn(e,t){if(e.op!=="<truthy>"&&!t.includes(e.value))throw new Error(`"${e.name}" must be one of ${t.map(n=>JSON.stringify(n)).join(", ")}`)}function xn(e,t){if(!t.includes(e.op))throw new Error(`"${e.name}" does not support "${e.op}" matcher`)}function sv(e,t){const n={role:t};for(const r of e)switch(r.name){case"checked":{yr(r.name,zh,t),wn(r,[!0,!1,"mixed"]),xn(r,["<truthy>","="]),n.checked=r.op==="<truthy>"?!0:r.value;break}case"pressed":{yr(r.name,Hh,t),wn(r,[!0,!1,"mixed"]),xn(r,["<truthy>","="]),n.pressed=r.op==="<truthy>"?!0:r.value;break}case"selected":{yr(r.name,Oh,t),wn(r,[!0,!1]),xn(r,["<truthy>","="]),n.selected=r.op==="<truthy>"?!0:r.value;break}case"expanded":{yr(r.name,Fh,t),wn(r,[!0,!1]),xn(r,["<truthy>","="]),n.expanded=r.op==="<truthy>"?!0:r.value;break}case"level":{if(yr(r.name,Uh,t),typeof r.value=="string"&&(r.value=+r.value),r.op!=="="||typeof r.value!="number"||Number.isNaN(r.value))throw new Error('"level" attribute must be compared to a number');n.level=r.value;break}case"disabled":{wn(r,[!0,!1]),xn(r,["<truthy>","="]),n.disabled=r.op==="<truthy>"?!0:r.value;break}case"name":{if(r.op==="<truthy>")throw new Error('"name" attribute must have a value');if(typeof r.value!="string"&&!(r.value instanceof RegExp))throw new Error('"name" attribute must be a string or a regular expression');n.name=r.value,n.nameOp=r.op,n.exact=r.caseSensitive;break}case"include-hidden":{wn(r,[!0,!1]),xn(r,["<truthy>","="]),n.includeHidden=r.op==="<truthy>"?!0:r.value;break}default:throw new Error(`Unknown attribute "${r.name}", must be one of ${sp.map(i=>`"${i}"`).join(", ")}.`)}return n}function ov(e,t,n){const r=[],i=o=>{if($e(o)===t.role&&!(t.selected!==void 0&&V1(o)!==t.selected)&&!(t.checked!==void 0&&B1(o)!==t.checked)&&!(t.pressed!==void 0&&W1(o)!==t.pressed)&&!(t.expanded!==void 0&&q1(o)!==t.expanded)&&!(t.level!==void 0&&X1(o)!==t.level)&&!(t.disabled!==void 0&&Vh(o)!==t.disabled)&&!(!t.includeHidden&&Oa(o))){if(t.name!==void 0){const l=Fe(za(o,!!t.includeHidden));if(typeof t.name=="string"&&(t.name=Fe(t.name)),n&&!t.exact&&t.nameOp==="="&&(t.nameOp="*="),!Gh(l,{name:"",jsonPath:[],op:t.nameOp||"=",value:t.name,caseSensitive:!!t.exact}))return}r.push(o)}},s=o=>{const l=[];o.shadowRoot&&l.push(o.shadowRoot);for(const a of o.querySelectorAll("*"))i(a),a.shadowRoot&&l.push(a.shadowRoot);l.forEach(s)};return s(e),r}function yc(e){return{queryAll:(t,n)=>{const r=ln(n,!0),i=r.name.toLowerCase();if(!i)throw new Error("Role must not be empty");const s=sv(r.attributes,i);Xh();try{return ov(t,s,e)}finally{Qh()}}}}function lv(e,t,n){const r=e.left-t.right;if(!(r<0||n!==void 0&&r>n))return r+Math.max(t.bottom-e.bottom,0)+Math.max(e.top-t.top,0)}function av(e,t,n){const r=t.left-e.right;if(!(r<0||n!==void 0&&r>n))return r+Math.max(t.bottom-e.bottom,0)+Math.max(e.top-t.top,0)}function uv(e,t,n){const r=t.top-e.bottom;if(!(r<0||n!==void 0&&r>n))return r+Math.max(e.left-t.left,0)+Math.max(t.right-e.right,0)}function cv(e,t,n){const r=e.top-t.bottom;if(!(r<0||n!==void 0&&r>n))return r+Math.max(e.left-t.left,0)+Math.max(t.right-e.right,0)}function dv(e,t,n){const r=n===void 0?50:n;let i=0;return e.left-t.right>=0&&(i+=e.left-t.right),t.left-e.right>=0&&(i+=t.left-e.right),t.top-e.bottom>=0&&(i+=t.top-e.bottom),e.top-t.bottom>=0&&(i+=e.top-t.bottom),i>r?void 0:i}const fv=["left-of","right-of","above","below","near"];function op(e,t,n,r){const i=t.getBoundingClientRect(),s={"left-of":av,"right-of":lv,above:uv,below:cv,near:dv}[e];let o;for(const l of n){if(l===t)continue;const a=s(i,l.getBoundingClientRect(),r);a!==void 0&&(o===void 0||a<o)&&(o=a)}return o}class hv{constructor(t){this._engines=new Map,this._cacheQueryCSS=new Map,this._cacheMatches=new Map,this._cacheQuery=new Map,this._cacheMatchesSimple=new Map,this._cacheMatchesParents=new Map,this._cacheCallMatches=new Map,this._cacheCallQuery=new Map,this._cacheQuerySimple=new Map,this._cacheText=new Map,this._retainCacheCounter=0;for(const[i,s]of t)this._engines.set(i,s);this._engines.set("not",gv),this._engines.set("is",Nr),this._engines.set("where",Nr),this._engines.set("has",pv),this._engines.set("scope",mv),this._engines.set("light",vv),this._engines.set("visible",yv),this._engines.set("text",wv),this._engines.set("text-is",xv),this._engines.set("text-matches",Sv),this._engines.set("has-text",_v),this._engines.set("right-of",wr("right-of")),this._engines.set("left-of",wr("left-of")),this._engines.set("above",wr("above")),this._engines.set("below",wr("below")),this._engines.set("near",wr("near")),this._engines.set("nth-match",Ev);const n=[...this._engines.keys()];n.sort();const r=[...wh];if(r.sort(),n.join("|")!==r.join("|"))throw new Error(`Please keep customCSSNames in sync with evaluator engines: ${n.join("|")} vs ${r.join("|")}`)}begin(){++this._retainCacheCounter}end(){--this._retainCacheCounter,this._retainCacheCounter||(this._cacheQueryCSS.clear(),this._cacheMatches.clear(),this._cacheQuery.clear(),this._cacheMatchesSimple.clear(),this._cacheMatchesParents.clear(),this._cacheCallMatches.clear(),this._cacheCallQuery.clear(),this._cacheQuerySimple.clear(),this._cacheText.clear())}_cached(t,n,r,i){t.has(n)||t.set(n,[]);const s=t.get(n),o=s.find(a=>r.every((u,c)=>a.rest[c]===u));if(o)return o.result;const l=i();return s.push({rest:r,result:l}),l}_checkSelector(t){if(!(typeof t=="object"&&t&&(Array.isArray(t)||"simples"in t&&t.simples.length)))throw new Error(`Malformed selector "${t}"`);return t}matches(t,n,r){const i=this._checkSelector(n);this.begin();try{return this._cached(this._cacheMatches,t,[i,r.scope,r.pierceShadow,r.originalScope],()=>Array.isArray(i)?this._matchesEngine(Nr,t,i,r):(this._hasScopeClause(i)&&(r=this._expandContextForScopeMatching(r)),this._matchesSimple(t,i.simples[i.simples.length-1].selector,r)?this._matchesParents(t,i,i.simples.length-2,r):!1))}finally{this.end()}}query(t,n){const r=this._checkSelector(n);this.begin();try{return this._cached(this._cacheQuery,r,[t.scope,t.pierceShadow,t.originalScope],()=>{if(Array.isArray(r))return this._queryEngine(Nr,t,r);this._hasScopeClause(r)&&(t=this._expandContextForScopeMatching(t));const i=this._scoreMap;this._scoreMap=new Map;let s=this._querySimple(t,r.simples[r.simples.length-1].selector);return s=s.filter(o=>this._matchesParents(o,r,r.simples.length-2,t)),this._scoreMap.size&&s.sort((o,l)=>{const a=this._scoreMap.get(o),u=this._scoreMap.get(l);return a===u?0:a===void 0?1:u===void 0?-1:a-u}),this._scoreMap=i,s})}finally{this.end()}}_markScore(t,n){this._scoreMap&&this._scoreMap.set(t,n)}_hasScopeClause(t){return t.simples.some(n=>n.selector.functions.some(r=>r.name==="scope"))}_expandContextForScopeMatching(t){if(t.scope.nodeType!==1)return t;const n=Ee(t.scope);return n?{...t,scope:n,originalScope:t.originalScope||t.scope}:t}_matchesSimple(t,n,r){return this._cached(this._cacheMatchesSimple,t,[n,r.scope,r.pierceShadow,r.originalScope],()=>{if(t===r.scope||n.css&&!this._matchesCSS(t,n.css))return!1;for(const i of n.functions)if(!this._matchesEngine(this._getEngine(i.name),t,i.args,r))return!1;return!0})}_querySimple(t,n){return n.functions.length?this._cached(this._cacheQuerySimple,n,[t.scope,t.pierceShadow,t.originalScope],()=>{let r=n.css;const i=n.functions;r==="*"&&i.length&&(r=void 0);let s,o=-1;r!==void 0?s=this._queryCSS(t,r):(o=i.findIndex(l=>this._getEngine(l.name).query!==void 0),o===-1&&(o=0),s=this._queryEngine(this._getEngine(i[o].name),t,i[o].args));for(let l=0;l<i.length;l++){if(l===o)continue;const a=this._getEngine(i[l].name);a.matches!==void 0&&(s=s.filter(u=>this._matchesEngine(a,u,i[l].args,t)))}for(let l=0;l<i.length;l++){if(l===o)continue;const a=this._getEngine(i[l].name);a.matches===void 0&&(s=s.filter(u=>this._matchesEngine(a,u,i[l].args,t)))}return s}):this._queryCSS(t,n.css||"*")}_matchesParents(t,n,r,i){return r<0?!0:this._cached(this._cacheMatchesParents,t,[n,r,i.scope,i.pierceShadow,i.originalScope],()=>{const{selector:s,combinator:o}=n.simples[r];if(o===">"){const l=Li(t,i);return!l||!this._matchesSimple(l,s,i)?!1:this._matchesParents(l,n,r-1,i)}if(o==="+"){const l=No(t,i);return!l||!this._matchesSimple(l,s,i)?!1:this._matchesParents(l,n,r-1,i)}if(o===""){let l=Li(t,i);for(;l;){if(this._matchesSimple(l,s,i)){if(this._matchesParents(l,n,r-1,i))return!0;if(n.simples[r-1].combinator==="")break}l=Li(l,i)}return!1}if(o==="~"){let l=No(t,i);for(;l;){if(this._matchesSimple(l,s,i)){if(this._matchesParents(l,n,r-1,i))return!0;if(n.simples[r-1].combinator==="~")break}l=No(l,i)}return!1}if(o===">="){let l=t;for(;l;){if(this._matchesSimple(l,s,i)){if(this._matchesParents(l,n,r-1,i))return!0;if(n.simples[r-1].combinator==="")break}l=Li(l,i)}return!1}throw new Error(`Unsupported combinator "${o}"`)})}_matchesEngine(t,n,r,i){if(t.matches)return this._callMatches(t,n,r,i);if(t.query)return this._callQuery(t,r,i).includes(n);throw new Error('Selector engine should implement "matches" or "query"')}_queryEngine(t,n,r){if(t.query)return this._callQuery(t,r,n);if(t.matches)return this._queryCSS(n,"*").filter(i=>this._callMatches(t,i,r,n));throw new Error('Selector engine should implement "matches" or "query"')}_callMatches(t,n,r,i){return this._cached(this._cacheCallMatches,n,[t,i.scope,i.pierceShadow,i.originalScope,...r],()=>t.matches(n,r,i,this))}_callQuery(t,n,r){return this._cached(this._cacheCallQuery,t,[r.scope,r.pierceShadow,r.originalScope,...n],()=>t.query(r,n,this))}_matchesCSS(t,n){return t.matches(n)}_queryCSS(t,n){return this._cached(this._cacheQueryCSS,n,[t.scope,t.pierceShadow,t.originalScope],()=>{let r=[];function i(s){if(r=r.concat([...s.querySelectorAll(n)]),!!t.pierceShadow){s.shadowRoot&&i(s.shadowRoot);for(const o of s.querySelectorAll("*"))o.shadowRoot&&i(o.shadowRoot)}}return i(t.scope),r})}_getEngine(t){const n=this._engines.get(t);if(!n)throw new Error(`Unknown selector engine "${t}"`);return n}}const Nr={matches(e,t,n,r){if(t.length===0)throw new Error('"is" engine expects non-empty selector list');return t.some(i=>r.matches(e,i,n))},query(e,t,n){if(t.length===0)throw new Error('"is" engine expects non-empty selector list');let r=[];for(const i of t)r=r.concat(n.query(e,i));return t.length===1?r:lp(r)}},pv={matches(e,t,n,r){if(t.length===0)throw new Error('"has" engine expects non-empty selector list');return r.query({...n,scope:e},t).length>0}},mv={matches(e,t,n,r){if(t.length!==0)throw new Error('"scope" engine expects no arguments');const i=n.originalScope||n.scope;return i.nodeType===9?e===i.documentElement:e===i},query(e,t,n){if(t.length!==0)throw new Error('"scope" engine expects no arguments');const r=e.originalScope||e.scope;if(r.nodeType===9){const i=r.documentElement;return i?[i]:[]}return r.nodeType===1?[r]:[]}},gv={matches(e,t,n,r){if(t.length===0)throw new Error('"not" engine expects non-empty selector list');return!r.matches(e,t,n)}},vv={query(e,t,n){return n.query({...e,pierceShadow:!1},t)},matches(e,t,n,r){return r.matches(e,t,{...n,pierceShadow:!1})}},yv={matches(e,t,n,r){if(t.length)throw new Error('"visible" engine expects no arguments');return Cs(e)}},wv={matches(e,t,n,r){if(t.length!==1||typeof t[0]!="string")throw new Error('"text" engine expects a single string');const i=Fe(t[0]).toLowerCase(),s=o=>o.normalized.toLowerCase().includes(i);return Gs(r._cacheText,e,s)==="self"}},xv={matches(e,t,n,r){if(t.length!==1||typeof t[0]!="string")throw new Error('"text-is" engine expects a single string');const i=Fe(t[0]),s=o=>!i&&!o.immediate.length?!0:o.immediate.some(l=>Fe(l)===i);return Gs(r._cacheText,e,s)!=="none"}},Sv={matches(e,t,n,r){if(t.length===0||typeof t[0]!="string"||t.length>2||t.length===2&&typeof t[1]!="string")throw new Error('"text-matches" engine expects a regexp body and optional regexp flags');const i=new RegExp(t[0],t.length===2?t[1]:void 0),s=o=>i.test(o.full);return Gs(r._cacheText,e,s)==="self"}},_v={matches(e,t,n,r){if(t.length!==1||typeof t[0]!="string")throw new Error('"has-text" engine expects a single string');if(Va(e))return!1;const i=Fe(t[0]).toLowerCase();return(o=>o.normalized.toLowerCase().includes(i))(Me(r._cacheText,e))}};function wr(e){return{matches(t,n,r,i){const s=n.length&&typeof n[n.length-1]=="number"?n[n.length-1]:void 0,o=s===void 0?n:n.slice(0,n.length-1);if(n.length<1+(s===void 0?0:1))throw new Error(`"${e}" engine expects a selector list and optional maximum distance in pixels`);const l=i.query(r,o),a=op(e,t,l,s);return a===void 0?!1:(i._markScore(t,a),!0)}}}const Ev={query(e,t,n){let r=t[t.length-1];if(t.length<2)throw new Error('"nth-match" engine expects non-empty selector list and an index argument');if(typeof r!="number"||r<1)throw new Error('"nth-match" engine expects a one-based index as the last argument');const i=Nr.query(e,t.slice(0,t.length-1),n);return r--,r<i.length?[i[r]]:[]}};function Li(e,t){if(e!==t.scope)return t.pierceShadow?Ee(e):e.parentElement||void 0}function No(e,t){if(e!==t.scope)return e.previousElementSibling||void 0}function lp(e){const t=new Map,n=[],r=[];function i(o){let l=t.get(o);if(l)return l;const a=Ee(o);return a?i(a).children.push(o):n.push(o),l={children:[],taken:!1},t.set(o,l),l}for(const o of e)i(o).taken=!0;function s(o){const l=t.get(o);if(l.taken&&r.push(o),l.children.length>1){const a=new Set(l.children);l.children=[];let u=o.firstElementChild;for(;u&&l.children.length<a.size;)a.has(u)&&l.children.push(u),u=u.nextElementSibling;for(u=o.shadowRoot?o.shadowRoot.firstElementChild:null;u&&l.children.length<a.size;)a.has(u)&&l.children.push(u),u=u.nextElementSibling}l.children.forEach(s)}return n.forEach(s),r}const Il=new Map,Pl=new Map,ap=10,sr=ap/2,wc=1,kv=2,Tv=10,Cv=50,up=100,cp=120,dp=140,fp=160,Rl=180,hp=200,Nv=250,bv=up+sr,Lv=cp+sr,Av=dp+sr,Mv=fp+sr,jv=Rl+sr,Iv=hp+sr,Pv=300,Rv=500,$v=510,bo=520,pp=530,mp=1e4,Ov=1e7,zv=1e3;function xc(e,t,n){e._evaluator.begin(),Xh();try{let r=[];if(n.forTextExpect){let o=Ai(e,t.ownerDocument.documentElement,n);for(let l=t;l;l=Ee(l)){const a=Sn(e,l,{...n,noText:!0});if(!a)continue;if(It(a)<=zv){o=a;break}}r=[Yi(o)]}else if(t=Cr(t,"button,select,input,[role=button],[role=checkbox],[role=radio],a,[role=link]",n.root)||t,n.multiple){const o=Sn(e,t,n),l=Sn(e,t,{...n,noText:!0});let a=[o,l];if(Il.clear(),Pl.clear(),o&&Lo(o)&&a.push(Sn(e,t,{...n,noCSSId:!0})),l&&Lo(l)&&a.push(Sn(e,t,{...n,noText:!0,noCSSId:!0})),a=a.filter(Boolean),!a.length){const u=Ai(e,t,n);a.push(u),Lo(u)&&a.push(Ai(e,t,{...n,noCSSId:!0}))}r=[...new Set(a.map(u=>Yi(u)))]}else{const o=Sn(e,t,n)||Ai(e,t,n);r=[Yi(o)]}const i=r[0],s=e.parseSelector(i);return{selector:i,selectors:r,elements:e.querySelectorAll(s,n.root??t.ownerDocument)}}finally{Il.clear(),Pl.clear(),Qh(),e._evaluator.end()}}function Sc(e){return e.filter(t=>t[0].selector[0]!=="/")}function Sn(e,t,n){if(n.root&&!Qs(n.root,t))throw new Error("Target element must belong to the root's subtree");if(t===n.root)return[{engine:"css",selector:":scope",score:1}];if(t.ownerDocument.documentElement===t)return[{engine:"css",selector:"html",score:1}];const r=(s,o)=>{const l=s===t;let a=o?Hv(e,s,s===t):[];s!==t&&(a=Sc(a));const u=Dv(e,s,n).filter(p=>!n.omitInternalEngines||!p.engine.startsWith("internal:")).map(p=>[p]);let c=_c(e,n.root??t.ownerDocument,s,[...a,...u],l);a=Sc(a);const f=p=>{const v=o&&!p.length,y=[...p,...u].filter(x=>c?It(x)<It(c):!0);let w=y[0];if(w)for(let x=Ee(s);x&&x!==n.root;x=Ee(x)){const h=i(x,v);if(!h||c&&It([...h,...w])>=It(c))continue;if(w=_c(e,x,s,y,l),!w)return;const m=[...h,...w];(!c||It(m)<It(c))&&(c=m)}};return f(a),s===t&&a.length&&f([]),c},i=(s,o)=>{const l=o?Il:Pl;let a=l.get(s);return a===void 0&&(a=r(s,o),l.set(s,a)),a};return r(t,!n.noText)}function Dv(e,t,n){const r=[];{for(const o of["data-testid","data-test-id","data-test"])o!==n.testIdAttributeName&&t.getAttribute(o)&&r.push({engine:"css",selector:`[${o}=${gr(t.getAttribute(o))}]`,score:kv});if(!n.noCSSId){const o=t.getAttribute("id");o&&!Fv(o)&&r.push({engine:"css",selector:gp(o),score:Rv})}r.push({engine:"css",selector:qe(t.nodeName.toLowerCase()),score:pp})}if(t.nodeName==="IFRAME"){for(const o of["name","title"])t.getAttribute(o)&&r.push({engine:"css",selector:`${qe(t.nodeName.toLowerCase())}[${o}=${gr(t.getAttribute(o))}]`,score:Tv});return t.getAttribute(n.testIdAttributeName)&&r.push({engine:"css",selector:`[${n.testIdAttributeName}=${gr(t.getAttribute(n.testIdAttributeName))}]`,score:wc}),$l([r]),r}if(t.getAttribute(n.testIdAttributeName)&&r.push({engine:"internal:testid",selector:`[${n.testIdAttributeName}=${_e(t.getAttribute(n.testIdAttributeName),!0)}]`,score:wc}),t.nodeName==="INPUT"||t.nodeName==="TEXTAREA"){const o=t;if(o.placeholder){r.push({engine:"internal:attr",selector:`[placeholder=${_e(o.placeholder,!0)}]`,score:bv});for(const l of $n(o.placeholder))r.push({engine:"internal:attr",selector:`[placeholder=${_e(l.text,!1)}]`,score:up-l.scoreBouns})}}const i=Yh(e._evaluator._cacheText,t);for(const o of i){const l=o.normalized;r.push({engine:"internal:label",selector:st(l,!0),score:Lv});for(const a of $n(l))r.push({engine:"internal:label",selector:st(a.text,!1),score:cp-a.scoreBouns})}const s=$e(t);return s&&!["none","presentation"].includes(s)&&r.push({engine:"internal:role",selector:s,score:$v}),t.getAttribute("name")&&["BUTTON","FORM","FIELDSET","FRAME","IFRAME","INPUT","KEYGEN","OBJECT","OUTPUT","SELECT","TEXTAREA","MAP","META","PARAM"].includes(t.nodeName)&&r.push({engine:"css",selector:`${qe(t.nodeName.toLowerCase())}[name=${gr(t.getAttribute("name"))}]`,score:bo}),["INPUT","TEXTAREA"].includes(t.nodeName)&&t.getAttribute("type")!=="hidden"&&t.getAttribute("type")&&r.push({engine:"css",selector:`${qe(t.nodeName.toLowerCase())}[type=${gr(t.getAttribute("type"))}]`,score:bo}),["INPUT","TEXTAREA","SELECT"].includes(t.nodeName)&&t.getAttribute("type")!=="hidden"&&r.push({engine:"css",selector:qe(t.nodeName.toLowerCase()),score:bo+1}),$l([r]),r}function Hv(e,t,n){if(t.nodeName==="SELECT")return[];const r=[],i=t.getAttribute("title");if(i){r.push([{engine:"internal:attr",selector:`[title=${_e(i,!0)}]`,score:Iv}]);for(const a of $n(i))r.push([{engine:"internal:attr",selector:`[title=${_e(a.text,!1)}]`,score:hp-a.scoreBouns}])}const s=t.getAttribute("alt");if(s&&["APPLET","AREA","IMG","INPUT"].includes(t.nodeName)){r.push([{engine:"internal:attr",selector:`[alt=${_e(s,!0)}]`,score:Mv}]);for(const a of $n(s))r.push([{engine:"internal:attr",selector:`[alt=${_e(a.text,!1)}]`,score:fp-a.scoreBouns}])}const o=Me(e._evaluator._cacheText,t).normalized;if(o){const a=$n(o);if(n){o.length<=80&&r.push([{engine:"internal:text",selector:st(o,!0),score:jv}]);for(const c of a)r.push([{engine:"internal:text",selector:st(c.text,!1),score:Rl-c.scoreBouns}])}const u={engine:"css",selector:qe(t.nodeName.toLowerCase()),score:pp};for(const c of a)r.push([u,{engine:"internal:has-text",selector:st(c.text,!1),score:Rl-c.scoreBouns}]);o.length<=80&&r.push([u,{engine:"internal:has-text",selector:"/^"+w0(o)+"$/",score:Nv}])}const l=$e(t);if(l&&!["none","presentation"].includes(l)){const a=za(t,!1);if(a){r.push([{engine:"internal:role",selector:`${l}[name=${_e(a,!0)}]`,score:Av}]);for(const u of $n(a))r.push([{engine:"internal:role",selector:`${l}[name=${_e(u.text,!1)}]`,score:dp-u.scoreBouns}])}}return $l(r),r}function gp(e){return/^[a-zA-Z][a-zA-Z0-9\-\_]+$/.test(e)?"#"+e:`[id="${qe(e)}"]`}function Lo(e){return e.some(t=>t.engine==="css"&&(t.selector.startsWith("#")||t.selector.startsWith('[id="')))}function Ai(e,t,n){const r=n.root??t.ownerDocument,i=[];function s(l){const a=i.slice();l&&a.unshift(l);const u=a.join(" > "),c=e.parseSelector(u);return e.querySelector(c,r,!1)===t?u:void 0}function o(l){const a={engine:"css",selector:l,score:Ov},u=e.parseSelector(l),c=e.querySelectorAll(u,r);if(c.length===1)return[a];const f={engine:"nth",selector:String(c.indexOf(t)),score:mp};return[a,f]}for(let l=t;l&&l!==r;l=Ee(l)){const a=l.nodeName.toLowerCase();let u="";if(l.id&&!n.noCSSId){const p=gp(l.id),v=s(p);if(v)return o(v);u=p}const c=l.parentNode,f=[...l.classList];for(let p=0;p<f.length;++p){const v="."+qe(f.slice(0,p+1).join(".")),y=s(v);if(y)return o(y);!u&&c&&c.querySelectorAll(v).length===1&&(u=v)}if(c){const p=[...c.children],y=p.filter(x=>x.nodeName.toLowerCase()===a).indexOf(l)===0?qe(a):`${qe(a)}:nth-child(${1+p.indexOf(l)})`,w=s(y);if(w)return o(w);u||(u=y)}else u||(u=qe(a));i.unshift(u)}return o(s())}function $l(e){for(const t of e)for(const n of t)n.score>Cv&&n.score<Pv&&(n.score+=Math.min(ap,n.selector.length/10|0))}function Yi(e){const t=[];let n="";for(const{engine:r,selector:i}of e)t.length&&(n!=="css"||r!=="css"||i.startsWith(":nth-match("))&&t.push(">>"),n=r,r==="css"?t.push(i):t.push(`${r}=${i}`);return t.join(" ")}function It(e){let t=0;for(let n=0;n<e.length;n++)t+=e[n].score*(e.length-n);return t}function _c(e,t,n,r,i){const s=r.map(l=>({tokens:l,score:It(l)}));s.sort((l,a)=>l.score-a.score);let o=null;for(const{tokens:l}of s){const a=e.parseSelector(Yi(l)),u=e.querySelectorAll(a,t);if(u[0]===n&&u.length===1)return l;const c=u.indexOf(n);if(!i||o||c===-1||u.length>5)continue;const f={engine:"nth",selector:String(c),score:mp};o=[...l,f]}return o}function Fv(e){let t,n=0;for(let r=0;r<e.length;++r){const i=e[r];let s;if(!(i==="-"||i==="_")){if(i>="a"&&i<="z"?s="lower":i>="A"&&i<="Z"?s="upper":i>="0"&&i<="9"?s="digit":s="other",s==="lower"&&t==="upper"){t=s;continue}t&&t!==s&&++n,t=s}}return n>=e.length/4}function Ec(e,t){if(e.length<=t)return e;e=e.substring(0,t);const n=e.match(/^(.*)\b(.+?)$/);return n?n[1].trimEnd():""}function $n(e){let t=[];{const n=e.match(/^([\d.,]+)[^.,\w]/),r=n?n[1].length:0;if(r){const i=e.substring(r).trimStart();t.push({text:i,scoreBouns:i.length<=30?2:1})}}{const n=e.match(/[^.,\w]([\d.,]+)$/),r=n?n[1].length:0;if(r){const i=e.substring(0,e.length-r).trimEnd();t.push({text:i,scoreBouns:i.length<=30?2:1})}}return e.length<=30?t.push({text:e,scoreBouns:0}):(t.push({text:Ec(e,80),scoreBouns:0}),t.push({text:Ec(e,30),scoreBouns:1})),t=t.filter(n=>n.text),t.length||t.push({text:e.substring(0,80),scoreBouns:0}),t}const Uv=`:host{font-size:13px;font-family:system-ui,Ubuntu,Droid Sans,sans-serif;color:#333}x-pw-tooltip{-webkit-backdrop-filter:blur(5px);backdrop-filter:blur(5px);background-color:#fff;border-radius:6px;box-shadow:0 .5rem 1.2rem #0000004d;display:none;font-size:12.8px;font-weight:400;left:0;line-height:1.5;max-width:600px;position:absolute;top:0;padding:0;flex-direction:column;overflow:hidden}x-pw-tooltip-line{display:flex;max-width:600px;padding:6px;-webkit-user-select:none;user-select:none;cursor:pointer}x-pw-tooltip-line.selectable:hover{background-color:#f2f2f2;overflow:hidden}x-pw-tooltip-footer{display:flex;max-width:600px;padding:6px;-webkit-user-select:none;user-select:none;color:#777}x-pw-dialog{background-color:#fff;pointer-events:auto;border-radius:6px;box-shadow:0 .5rem 1.2rem #0000004d;display:flex;flex-direction:column;position:absolute;width:400px;height:150px;z-index:10;font-size:13px}x-pw-dialog-body{display:flex;flex-direction:column;flex:auto}x-pw-dialog-body label{margin:5px 8px;display:flex;flex-direction:row;align-items:center}x-pw-highlight{position:absolute;top:0;left:0;width:0;height:0}x-pw-action-point{position:absolute;width:20px;height:20px;background:red;border-radius:10px;margin:-10px 0 0 -10px;z-index:2}x-pw-separator{height:1px;margin:6px 9px;background:#949494e5}x-pw-tool-gripper{height:28px;width:24px;margin:2px 0;cursor:grab}x-pw-tool-gripper:active{cursor:grabbing}x-pw-tool-gripper>x-div{width:100%;height:100%;-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;-webkit-mask-size:20px;mask-repeat:no-repeat;mask-position:center;mask-size:16px;-webkit-mask-image:url("data:image/svg+xml;utf8,<svg width='16' height='16' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg' fill='currentColor'><path d='M11 18c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2zm-2-8c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm6 4c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z' /></svg>");mask-image:url("data:image/svg+xml;utf8,<svg width='16' height='16' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg' fill='currentColor'><path d='M11 18c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2zm-2-8c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm6 4c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z' /></svg>");background-color:#555}x-pw-tools-list>label{display:flex;align-items:center;margin:0 10px;-webkit-user-select:none;user-select:none}x-pw-tools-list{display:flex;width:100%;border-bottom:1px solid #dddddd}x-pw-tool-item{pointer-events:auto;cursor:pointer;height:28px;width:28px;border-radius:3px}x-pw-tool-item:not(.disabled):hover{background-color:#dbdbdb}x-pw-tool-item.active{background-color:#8acae480}x-pw-tool-item.active:not(.disabled):hover{background-color:#8acae4c4}x-pw-tool-item>x-div{width:100%;height:100%;-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;-webkit-mask-size:20px;mask-repeat:no-repeat;mask-position:center;mask-size:16px;background-color:#3a3a3a}x-pw-tool-item.disabled>x-div{background-color:#61616180;cursor:default}x-pw-tool-item.record.active{background-color:transparent}x-pw-tool-item.record.active:hover{background-color:#dbdbdb}x-pw-tool-item.record.active>x-div{background-color:#a1260d}x-pw-tool-item.accept>x-div{background-color:#388a34}x-pw-tool-item.record>x-div{-webkit-mask-image:url("data:image/svg+xml;utf8,<svg width='16' height='16' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg' fill='currentColor'><path d='M8 1a6.8 6.8 0 0 1 1.86.253 6.899 6.899 0 0 1 3.083 1.805 6.903 6.903 0 0 1 1.804 3.083C14.916 6.738 15 7.357 15 8s-.084 1.262-.253 1.86a6.9 6.9 0 0 1-.704 1.674 7.157 7.157 0 0 1-2.516 2.509 6.966 6.966 0 0 1-1.668.71A6.984 6.984 0 0 1 8 15a6.984 6.984 0 0 1-1.86-.246 7.098 7.098 0 0 1-1.674-.711 7.3 7.3 0 0 1-1.415-1.094 7.295 7.295 0 0 1-1.094-1.415 7.098 7.098 0 0 1-.71-1.675A6.985 6.985 0 0 1 1 8c0-.643.082-1.262.246-1.86a6.968 6.968 0 0 1 .711-1.667 7.156 7.156 0 0 1 2.509-2.516 6.895 6.895 0 0 1 1.675-.704A6.808 6.808 0 0 1 8 1z'/></svg>");mask-image:url("data:image/svg+xml;utf8,<svg width='16' height='16' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg' fill='currentColor'><path d='M8 1a6.8 6.8 0 0 1 1.86.253 6.899 6.899 0 0 1 3.083 1.805 6.903 6.903 0 0 1 1.804 3.083C14.916 6.738 15 7.357 15 8s-.084 1.262-.253 1.86a6.9 6.9 0 0 1-.704 1.674 7.157 7.157 0 0 1-2.516 2.509 6.966 6.966 0 0 1-1.668.71A6.984 6.984 0 0 1 8 15a6.984 6.984 0 0 1-1.86-.246 7.098 7.098 0 0 1-1.674-.711 7.3 7.3 0 0 1-1.415-1.094 7.295 7.295 0 0 1-1.094-1.415 7.098 7.098 0 0 1-.71-1.675A6.985 6.985 0 0 1 1 8c0-.643.082-1.262.246-1.86a6.968 6.968 0 0 1 .711-1.667 7.156 7.156 0 0 1 2.509-2.516 6.895 6.895 0 0 1 1.675-.704A6.808 6.808 0 0 1 8 1z'/></svg>")}x-pw-tool-item.pick-locator>x-div{-webkit-mask-image:url("data:image/svg+xml;utf8,<svg width='16' height='16' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg' fill='currentColor'><path fill-rule='evenodd' clip-rule='evenodd' d='M1 3l1-1h12l1 1v6h-1V3H2v8h5v1H2l-1-1V3zm14.707 9.707L9 6v9.414l2.707-2.707h4zM10 13V8.414l3.293 3.293h-2L10 13z'/></svg>");mask-image:url("data:image/svg+xml;utf8,<svg width='16' height='16' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg' fill='currentColor'><path fill-rule='evenodd' clip-rule='evenodd' d='M1 3l1-1h12l1 1v6h-1V3H2v8h5v1H2l-1-1V3zm14.707 9.707L9 6v9.414l2.707-2.707h4zM10 13V8.414l3.293 3.293h-2L10 13z'/></svg>")}x-pw-tool-item.text>x-div{-webkit-mask-image:url("data:image/svg+xml;utf8,<svg width='16' height='16' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg' fill='currentColor'><path fill-rule='evenodd' clip-rule='evenodd' d='M0 11H1V13H15V11H16V14H15H1H0V11Z'/><path d='M6.84048 11H5.95963V10.1406H5.93814C5.555 10.7995 4.99104 11.1289 4.24625 11.1289C3.69839 11.1289 3.26871 10.9839 2.95718 10.6938C2.64924 10.4038 2.49527 10.0189 2.49527 9.53906C2.49527 8.51139 3.10041 7.91341 4.3107 7.74512L5.95963 7.51416C5.95963 6.57959 5.58186 6.1123 4.82632 6.1123C4.16389 6.1123 3.56591 6.33789 3.03238 6.78906V5.88672C3.57307 5.54297 4.19612 5.37109 4.90152 5.37109C6.19416 5.37109 6.84048 6.05501 6.84048 7.42285V11ZM5.95963 8.21777L4.63297 8.40039C4.22476 8.45768 3.91682 8.55973 3.70914 8.70654C3.50145 8.84977 3.39761 9.10579 3.39761 9.47461C3.39761 9.74316 3.4925 9.96338 3.68228 10.1353C3.87564 10.3035 4.13166 10.3877 4.45035 10.3877C4.8872 10.3877 5.24706 10.2355 5.52994 9.93115C5.8164 9.62321 5.95963 9.2347 5.95963 8.76562V8.21777Z'/><path d='M9.3475 10.2051H9.32601V11H8.44515V2.85742H9.32601V6.4668H9.3475C9.78076 5.73633 10.4146 5.37109 11.2489 5.37109C11.9543 5.37109 12.5057 5.61816 12.9032 6.1123C13.3042 6.60286 13.5047 7.26172 13.5047 8.08887C13.5047 9.00911 13.2809 9.74674 12.8333 10.3018C12.3857 10.8532 11.7734 11.1289 10.9964 11.1289C10.2695 11.1289 9.71989 10.821 9.3475 10.2051ZM9.32601 7.98682V8.75488C9.32601 9.20964 9.47282 9.59635 9.76644 9.91504C10.0636 10.2301 10.4396 10.3877 10.8944 10.3877C11.4279 10.3877 11.8451 10.1836 12.1458 9.77539C12.4502 9.36719 12.6024 8.79964 12.6024 8.07275C12.6024 7.46045 12.4609 6.98063 12.1781 6.6333C11.8952 6.28597 11.512 6.1123 11.0286 6.1123C10.5166 6.1123 10.1048 6.29134 9.7933 6.64941C9.48177 7.00391 9.32601 7.44971 9.32601 7.98682Z'/></svg>");mask-image:url("data:image/svg+xml;utf8,<svg width='16' height='16' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg' fill='currentColor'><path fill-rule='evenodd' clip-rule='evenodd' d='M0 11H1V13H15V11H16V14H15H1H0V11Z'/><path d='M6.84048 11H5.95963V10.1406H5.93814C5.555 10.7995 4.99104 11.1289 4.24625 11.1289C3.69839 11.1289 3.26871 10.9839 2.95718 10.6938C2.64924 10.4038 2.49527 10.0189 2.49527 9.53906C2.49527 8.51139 3.10041 7.91341 4.3107 7.74512L5.95963 7.51416C5.95963 6.57959 5.58186 6.1123 4.82632 6.1123C4.16389 6.1123 3.56591 6.33789 3.03238 6.78906V5.88672C3.57307 5.54297 4.19612 5.37109 4.90152 5.37109C6.19416 5.37109 6.84048 6.05501 6.84048 7.42285V11ZM5.95963 8.21777L4.63297 8.40039C4.22476 8.45768 3.91682 8.55973 3.70914 8.70654C3.50145 8.84977 3.39761 9.10579 3.39761 9.47461C3.39761 9.74316 3.4925 9.96338 3.68228 10.1353C3.87564 10.3035 4.13166 10.3877 4.45035 10.3877C4.8872 10.3877 5.24706 10.2355 5.52994 9.93115C5.8164 9.62321 5.95963 9.2347 5.95963 8.76562V8.21777Z'/><path d='M9.3475 10.2051H9.32601V11H8.44515V2.85742H9.32601V6.4668H9.3475C9.78076 5.73633 10.4146 5.37109 11.2489 5.37109C11.9543 5.37109 12.5057 5.61816 12.9032 6.1123C13.3042 6.60286 13.5047 7.26172 13.5047 8.08887C13.5047 9.00911 13.2809 9.74674 12.8333 10.3018C12.3857 10.8532 11.7734 11.1289 10.9964 11.1289C10.2695 11.1289 9.71989 10.821 9.3475 10.2051ZM9.32601 7.98682V8.75488C9.32601 9.20964 9.47282 9.59635 9.76644 9.91504C10.0636 10.2301 10.4396 10.3877 10.8944 10.3877C11.4279 10.3877 11.8451 10.1836 12.1458 9.77539C12.4502 9.36719 12.6024 8.79964 12.6024 8.07275C12.6024 7.46045 12.4609 6.98063 12.1781 6.6333C11.8952 6.28597 11.512 6.1123 11.0286 6.1123C10.5166 6.1123 10.1048 6.29134 9.7933 6.64941C9.48177 7.00391 9.32601 7.44971 9.32601 7.98682Z'/></svg>")}x-pw-tool-item.visibility>x-div{-webkit-mask-image:url("data:image/svg+xml;utf8,<svg width='16' height='16' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg' fill='currentColor'><path d='M7.99993 6.00316C9.47266 6.00316 10.6666 7.19708 10.6666 8.66981C10.6666 10.1426 9.47266 11.3365 7.99993 11.3365C6.52715 11.3365 5.33324 10.1426 5.33324 8.66981C5.33324 7.19708 6.52715 6.00316 7.99993 6.00316ZM7.99993 7.00315C7.07946 7.00315 6.33324 7.74935 6.33324 8.66981C6.33324 9.59028 7.07946 10.3365 7.99993 10.3365C8.9204 10.3365 9.6666 9.59028 9.6666 8.66981C9.6666 7.74935 8.9204 7.00315 7.99993 7.00315ZM7.99993 3.66675C11.0756 3.66675 13.7307 5.76675 14.4673 8.70968C14.5344 8.97755 14.3716 9.24908 14.1037 9.31615C13.8358 9.38315 13.5643 9.22041 13.4973 8.95248C12.8713 6.45205 10.6141 4.66675 7.99993 4.66675C5.38454 4.66675 3.12664 6.45359 2.50182 8.95555C2.43491 9.22341 2.16348 9.38635 1.89557 9.31948C1.62766 9.25255 1.46471 8.98115 1.53162 8.71321C2.26701 5.76856 4.9229 3.66675 7.99993 3.66675Z'/></svg>");mask-image:url("data:image/svg+xml;utf8,<svg width='16' height='16' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg' fill='currentColor'><path d='M7.99993 6.00316C9.47266 6.00316 10.6666 7.19708 10.6666 8.66981C10.6666 10.1426 9.47266 11.3365 7.99993 11.3365C6.52715 11.3365 5.33324 10.1426 5.33324 8.66981C5.33324 7.19708 6.52715 6.00316 7.99993 6.00316ZM7.99993 7.00315C7.07946 7.00315 6.33324 7.74935 6.33324 8.66981C6.33324 9.59028 7.07946 10.3365 7.99993 10.3365C8.9204 10.3365 9.6666 9.59028 9.6666 8.66981C9.6666 7.74935 8.9204 7.00315 7.99993 7.00315ZM7.99993 3.66675C11.0756 3.66675 13.7307 5.76675 14.4673 8.70968C14.5344 8.97755 14.3716 9.24908 14.1037 9.31615C13.8358 9.38315 13.5643 9.22041 13.4973 8.95248C12.8713 6.45205 10.6141 4.66675 7.99993 4.66675C5.38454 4.66675 3.12664 6.45359 2.50182 8.95555C2.43491 9.22341 2.16348 9.38635 1.89557 9.31948C1.62766 9.25255 1.46471 8.98115 1.53162 8.71321C2.26701 5.76856 4.9229 3.66675 7.99993 3.66675Z'/></svg>")}x-pw-tool-item.value>x-div{-webkit-mask-image:url("data:image/svg+xml;utf8,<svg width='16' height='16' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg' fill='currentColor'><path fill-rule='evenodd' clip-rule='evenodd' d='M4 6h8v1H4V6zm8 3H4v1h8V9z'/><path fill-rule='evenodd' clip-rule='evenodd' d='M1 4l1-1h12l1 1v8l-1 1H2l-1-1V4zm1 0v8h12V4H2z'/></svg>");mask-image:url("data:image/svg+xml;utf8,<svg width='16' height='16' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg' fill='currentColor'><path fill-rule='evenodd' clip-rule='evenodd' d='M4 6h8v1H4V6zm8 3H4v1h8V9z'/><path fill-rule='evenodd' clip-rule='evenodd' d='M1 4l1-1h12l1 1v8l-1 1H2l-1-1V4zm1 0v8h12V4H2z'/></svg>")}x-pw-tool-item.accept>x-div{-webkit-mask-image:url("data:image/svg+xml;utf8,<svg width='16' height='16' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg' fill='currentColor'><path d='M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z'/></svg>");mask-image:url("data:image/svg+xml;utf8,<svg width='16' height='16' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg' fill='currentColor'><path d='M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z'/></svg>")}x-pw-tool-item.cancel>x-div{-webkit-mask-image:url("data:image/svg+xml;utf8,<svg width='16' height='16' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg' fill='currentColor'><path d='M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/></svg>");mask-image:url("data:image/svg+xml;utf8,<svg width='16' height='16' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg' fill='currentColor'><path d='M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/></svg>")}x-pw-tool-item.succeeded>x-div{-webkit-mask-image:url("data:image/svg+xml;utf8,<svg width='16' height='16' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg' fill='currentColor'><path d='M6.27 10.87h.71l4.56-4.56-.71-.71-4.2 4.21-1.92-1.92L4 8.6l2.27 2.27z'/><path fill-rule='evenodd' clip-rule='evenodd' d='M8.6 1c1.6.1 3.1.9 4.2 2 1.3 1.4 2 3.1 2 5.1 0 1.6-.6 3.1-1.6 4.4-1 1.2-2.4 2.1-4 2.4-1.6.3-3.2.1-4.6-.7-1.4-.8-2.5-2-3.1-3.5C.9 9.2.8 7.5 1.3 6c.5-1.6 1.4-2.9 2.8-3.8C5.4 1.3 7 .9 8.6 1zm.5 12.9c1.3-.3 2.5-1 3.4-2.1.8-1.1 1.3-2.4 1.2-3.8 0-1.6-.6-3.2-1.7-4.3-1-1-2.2-1.6-3.6-1.7-1.3-.1-2.7.2-3.8 1-1.1.8-1.9 1.9-2.3 3.3-.4 1.3-.4 2.7.2 4 .6 1.3 1.5 2.3 2.7 3 1.2.7 2.6.9 3.9.6z'/></svg>")!important;mask-image:url("data:image/svg+xml;utf8,<svg width='16' height='16' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg' fill='currentColor'><path d='M6.27 10.87h.71l4.56-4.56-.71-.71-4.2 4.21-1.92-1.92L4 8.6l2.27 2.27z'/><path fill-rule='evenodd' clip-rule='evenodd' d='M8.6 1c1.6.1 3.1.9 4.2 2 1.3 1.4 2 3.1 2 5.1 0 1.6-.6 3.1-1.6 4.4-1 1.2-2.4 2.1-4 2.4-1.6.3-3.2.1-4.6-.7-1.4-.8-2.5-2-3.1-3.5C.9 9.2.8 7.5 1.3 6c.5-1.6 1.4-2.9 2.8-3.8C5.4 1.3 7 .9 8.6 1zm.5 12.9c1.3-.3 2.5-1 3.4-2.1.8-1.1 1.3-2.4 1.2-3.8 0-1.6-.6-3.2-1.7-4.3-1-1-2.2-1.6-3.6-1.7-1.3-.1-2.7.2-3.8 1-1.1.8-1.9 1.9-2.3 3.3-.4 1.3-.4 2.7.2 4 .6 1.3 1.5 2.3 2.7 3 1.2.7 2.6.9 3.9.6z'/></svg>")!important;background-color:#388a34!important;-webkit-mask-size:18px!important;mask-size:18px!important}x-pw-overlay{position:absolute;top:0;max-width:min-content;z-index:2147483647;background:transparent;pointer-events:auto}x-pw-overlay x-pw-tools-list{background-color:#fffd;box-shadow:#0000001a 0 5px 5px;border-radius:3px;border-bottom:none}x-pw-overlay x-pw-tool-item{margin:2px}textarea.text-editor{font-family:system-ui,Ubuntu,Droid Sans,sans-serif;flex:auto;border:none;margin:6px 10px;color:#333;outline:1px solid transparent!important;resize:none;padding:0;font-size:13px}textarea.text-editor.does-not-match{outline:1px solid red!important}x-div{display:block}x-spacer{flex:auto}*{box-sizing:border-box}*[hidden]{display:none!important}x-locator-editor{flex:none;width:100%;height:60px;padding:4px;border-bottom:1px solid #dddddd;outline:1px solid transparent}x-locator-editor.does-not-match{outline:1px solid red}.CodeMirror{width:100%!important;height:100%!important}`;class Ao{constructor(t){this._highlightEntries=[],this._highlightOptions={},this._language="javascript",this._injectedScript=t;const n=t.document;this._isUnderTest=t.isUnderTest,this._glassPaneElement=n.createElement("x-pw-glass"),this._glassPaneElement.style.position="fixed",this._glassPaneElement.style.top="0",this._glassPaneElement.style.right="0",this._glassPaneElement.style.bottom="0",this._glassPaneElement.style.left="0",this._glassPaneElement.style.zIndex="2147483646",this._glassPaneElement.style.pointerEvents="none",this._glassPaneElement.style.display="flex",this._glassPaneElement.style.backgroundColor="transparent";for(const i of["click","auxclick","dragstart","input","keydown","keyup","pointerdown","pointerup","mousedown","mouseup","mouseleave","focus","scroll"])this._glassPaneElement.addEventListener(i,s=>{s.stopPropagation(),s.stopImmediatePropagation(),s.type==="click"&&s.button===0&&this._highlightOptions.tooltipListItemSelected&&this._highlightOptions.tooltipListItemSelected(void 0)});this._actionPointElement=n.createElement("x-pw-action-point"),this._actionPointElement.setAttribute("hidden","true"),this._glassPaneShadow=this._glassPaneElement.attachShadow({mode:this._isUnderTest?"open":"closed"}),this._glassPaneShadow.appendChild(this._actionPointElement);const r=n.createElement("style");r.textContent=Uv,this._glassPaneShadow.appendChild(r)}install(){this._injectedScript.document.documentElement.appendChild(this._glassPaneElement)}setLanguage(t){this._language=t}runHighlightOnRaf(t){this._rafRequest&&cancelAnimationFrame(this._rafRequest),this.updateHighlight(this._injectedScript.querySelectorAll(t,this._injectedScript.document.documentElement),{tooltipText:Xt(this._language,hn(t))}),this._rafRequest=requestAnimationFrame(()=>this.runHighlightOnRaf(t))}uninstall(){this._rafRequest&&cancelAnimationFrame(this._rafRequest),this._glassPaneElement.remove()}showActionPoint(t,n){this._actionPointElement.style.top=n+"px",this._actionPointElement.style.left=t+"px",this._actionPointElement.hidden=!1}hideActionPoint(){this._actionPointElement.hidden=!0}clearHighlight(){var t,n;for(const r of this._highlightEntries)(t=r.highlightElement)==null||t.remove(),(n=r.tooltipElement)==null||n.remove();this._highlightEntries=[],this._highlightOptions={},this._glassPaneElement.style.pointerEvents="none"}updateHighlight(t,n){this._innerUpdateHighlight(t,n)}maskElements(t,n){this._innerUpdateHighlight(t,{color:n})}_innerUpdateHighlight(t,n){let r=n.color;if(r||(r=t.length>1?"#f6b26b7f":"#6fa8dc7f"),!this._highlightIsUpToDate(t,n)){this.clearHighlight(),this._highlightOptions=n,this._glassPaneElement.style.pointerEvents=n.tooltipListItemSelected?"initial":"none";for(let i=0;i<t.length;++i){const s=this._createHighlightElement();this._glassPaneShadow.appendChild(s);let o;if(n.tooltipList||n.tooltipText||n.tooltipFooter){o=this._injectedScript.document.createElement("x-pw-tooltip"),this._glassPaneShadow.appendChild(o),o.style.top="0",o.style.left="0",o.style.display="flex";let l=[];if(n.tooltipList)l=n.tooltipList;else if(n.tooltipText){const a=t.length>1?` [${i+1} of ${t.length}]`:"";l=[n.tooltipText+a]}for(let a=0;a<l.length;a++){const u=this._injectedScript.document.createElement("x-pw-tooltip-line");u.textContent=l[a],o.appendChild(u),n.tooltipListItemSelected&&(u.classList.add("selectable"),u.addEventListener("click",()=>{var c;return(c=n.tooltipListItemSelected)==null?void 0:c.call(n,a)}))}if(n.tooltipFooter){const a=this._injectedScript.document.createElement("x-pw-tooltip-footer");a.textContent=n.tooltipFooter,o.appendChild(a)}}this._highlightEntries.push({targetElement:t[i],tooltipElement:o,highlightElement:s})}for(const i of this._highlightEntries){if(i.box=i.targetElement.getBoundingClientRect(),!i.tooltipElement)continue;const{anchorLeft:s,anchorTop:o}=this.tooltipPosition(i.box,i.tooltipElement);i.tooltipTop=o,i.tooltipLeft=s}for(const i of this._highlightEntries){i.tooltipElement&&(i.tooltipElement.style.top=i.tooltipTop+"px",i.tooltipElement.style.left=i.tooltipLeft+"px");const s=i.box;i.highlightElement.style.backgroundColor=r,i.highlightElement.style.left=s.x+"px",i.highlightElement.style.top=s.y+"px",i.highlightElement.style.width=s.width+"px",i.highlightElement.style.height=s.height+"px",i.highlightElement.style.display="block",this._isUnderTest&&console.error("Highlight box for test: "+JSON.stringify({x:s.x,y:s.y,width:s.width,height:s.height}))}}}firstBox(){var t;return(t=this._highlightEntries[0])==null?void 0:t.box}tooltipPosition(t,n){const r=n.offsetWidth,i=n.offsetHeight,s=this._glassPaneElement.offsetWidth,o=this._glassPaneElement.offsetHeight;let l=t.left;l+r>s-5&&(l=s-r-5);let a=t.bottom+5;return a+i>o-5&&(t.top>i+5?a=t.top-i-5:a=o-5-i),{anchorLeft:l,anchorTop:a}}_highlightIsUpToDate(t,n){var r,i;if(n.tooltipText!==this._highlightOptions.tooltipText||n.tooltipListItemSelected!==this._highlightOptions.tooltipListItemSelected||n.tooltipFooter!==this._highlightOptions.tooltipFooter||((r=n.tooltipList)==null?void 0:r.length)!==((i=this._highlightOptions.tooltipList)==null?void 0:i.length))return!1;if(n.tooltipList&&this._highlightOptions.tooltipList){for(let s=0;s<n.tooltipList.length;s++)if(n.tooltipList[s]!==this._highlightOptions.tooltipList[s])return!1}if(t.length!==this._highlightEntries.length)return!1;for(let s=0;s<this._highlightEntries.length;++s){if(t[s]!==this._highlightEntries[s].targetElement)return!1;const o=this._highlightEntries[s].box;if(!o)return!1;const l=t[s].getBoundingClientRect();if(l.top!==o.top||l.right!==o.right||l.bottom!==o.bottom||l.left!==o.left)return!1}return!0}_createHighlightElement(){return this._injectedScript.document.createElement("x-pw-highlight")}appendChild(t){this._glassPaneShadow.appendChild(t)}}class vp{constructor(t,n,r,i,s,o,l){this.onGlobalListenersRemoved=new Set,this._testIdAttributeNameForStrictErrorAndConsoleCodegen="data-testid",this.utils={isInsideScope:Qs,elementText:Me,asLocator:Xt,normalizeWhiteSpace:Fe},this.window=t,this.document=t.document,this.isUnderTest=n,this._sdkLanguage=r,this._testIdAttributeNameForStrictErrorAndConsoleCodegen=i,this._evaluator=new hv(new Map),this._engines=new Map,this._engines.set("xpath",dc),this._engines.set("xpath:light",dc),this._engines.set("_react",Z1),this._engines.set("_vue",iv),this._engines.set("role",yc(!1)),this._engines.set("text",this._createTextEngine(!0,!1)),this._engines.set("text:light",this._createTextEngine(!1,!1)),this._engines.set("id",this._createAttributeEngine("id",!0)),this._engines.set("id:light",this._createAttributeEngine("id",!1)),this._engines.set("data-testid",this._createAttributeEngine("data-testid",!0)),this._engines.set("data-testid:light",this._createAttributeEngine("data-testid",!1)),this._engines.set("data-test-id",this._createAttributeEngine("data-test-id",!0)),this._engines.set("data-test-id:light",this._createAttributeEngine("data-test-id",!1)),this._engines.set("data-test",this._createAttributeEngine("data-test",!0)),this._engines.set("data-test:light",this._createAttributeEngine("data-test",!1)),this._engines.set("css",this._createCSSEngine()),this._engines.set("nth",{queryAll:()=>[]}),this._engines.set("visible",this._createVisibleEngine()),this._engines.set("internal:control",this._createControlEngine()),this._engines.set("internal:has",this._createHasEngine()),this._engines.set("internal:has-not",this._createHasNotEngine()),this._engines.set("internal:and",{queryAll:()=>[]}),this._engines.set("internal:or",{queryAll:()=>[]}),this._engines.set("internal:chain",this._createInternalChainEngine()),this._engines.set("internal:label",this._createInternalLabelEngine()),this._engines.set("internal:text",this._createTextEngine(!0,!0)),this._engines.set("internal:has-text",this._createInternalHasTextEngine()),this._engines.set("internal:has-not-text",this._createInternalHasNotTextEngine()),this._engines.set("internal:attr",this._createNamedAttributeEngine()),this._engines.set("internal:testid",this._createNamedAttributeEngine()),this._engines.set("internal:role",yc(!0));for(const{name:a,engine:u}of l)this._engines.set(a,u);this._stableRafCount=s,this._browserName=o,P1(o),this._setupGlobalListenersRemovalDetection(),this._setupHitTargetInterceptors(),n&&(this.window.__injectedScript=this)}eval(t){return this.window.eval(t)}testIdAttributeNameForStrictErrorAndConsoleCodegen(){return this._testIdAttributeNameForStrictErrorAndConsoleCodegen}parseSelector(t){const n=qs(t);return z0(n,r=>{if(!this._engines.has(r.name))throw this.createStacklessError(`Unknown engine "${r.name}" while parsing selector ${t}`)}),n}generateSelector(t,n){return xc(this,t,n)}generateSelectorSimple(t,n){return xc(this,t,{...n,testIdAttributeName:this._testIdAttributeNameForStrictErrorAndConsoleCodegen}).selector}querySelector(t,n,r){const i=this.querySelectorAll(t,n);if(r&&i.length>1)throw this.strictModeViolationError(t,i);return i[0]}_queryNth(t,n){const r=[...t];let i=+n.body;return i===-1&&(i=r.length-1),new Set(r.slice(i,i+1))}_queryLayoutSelector(t,n,r){const i=n.name,s=n.body,o=[],l=this.querySelectorAll(s.parsed,r);for(const a of t){const u=op(i,a,l,s.distance);u!==void 0&&o.push({element:a,score:u})}return o.sort((a,u)=>a.score-u.score),new Set(o.map(a=>a.element))}querySelectorAll(t,n){if(t.capture!==void 0){if(t.parts.some(i=>i.name==="nth"))throw this.createStacklessError("Can't query n-th element in a request with the capture.");const r={parts:t.parts.slice(0,t.capture+1)};if(t.capture<t.parts.length-1){const i={parts:t.parts.slice(t.capture+1)},s={name:"internal:has",body:{parsed:i},source:hn(i)};r.parts.push(s)}return this.querySelectorAll(r,n)}if(!n.querySelectorAll)throw this.createStacklessError("Node is not queryable.");if(t.capture!==void 0)throw this.createStacklessError("Internal error: there should not be a capture in the selector.");if(n.nodeType===11&&t.parts.length===1&&t.parts[0].name==="css"&&t.parts[0].source===":scope")return[n];this._evaluator.begin();try{let r=new Set([n]);for(const i of t.parts)if(i.name==="nth")r=this._queryNth(r,i);else if(i.name==="internal:and"){const s=this.querySelectorAll(i.body.parsed,n);r=new Set(s.filter(o=>r.has(o)))}else if(i.name==="internal:or"){const s=this.querySelectorAll(i.body.parsed,n);r=new Set(lp(new Set([...r,...s])))}else if(fv.includes(i.name))r=this._queryLayoutSelector(r,i,n);else{const s=new Set;for(const o of r){const l=this._queryEngineAll(i,o);for(const a of l)s.add(a)}r=s}return[...r]}finally{this._evaluator.end()}}_queryEngineAll(t,n){const r=this._engines.get(t.name).queryAll(n,t.body);for(const i of r)if(!("nodeName"in i))throw this.createStacklessError(`Expected a Node but got ${Object.prototype.toString.call(i)}`);return r}_createAttributeEngine(t,n){const r=i=>[{simples:[{selector:{css:`[${t}=${JSON.stringify(i)}]`,functions:[]},combinator:""}]}];return{queryAll:(i,s)=>this._evaluator.query({scope:i,pierceShadow:n},r(s))}}_createCSSEngine(){return{queryAll:(t,n)=>this._evaluator.query({scope:t,pierceShadow:!0},n)}}_createTextEngine(t,n){return{queryAll:(i,s)=>{const{matcher:o,kind:l}=ji(s,n),a=[];let u=null;const c=p=>{if(l==="lax"&&u&&u.contains(p))return!1;const v=Gs(this._evaluator._cacheText,p,o);v==="none"&&(u=p),(v==="self"||v==="selfAndChildren"&&l==="strict"&&!n)&&a.push(p)};i.nodeType===Node.ELEMENT_NODE&&c(i);const f=this._evaluator._queryCSS({scope:i,pierceShadow:t},"*");for(const p of f)c(p);return a}}}_createInternalHasTextEngine(){return{queryAll:(t,n)=>{if(t.nodeType!==1)return[];const r=t,i=Me(this._evaluator._cacheText,r),{matcher:s}=ji(n,!0);return s(i)?[r]:[]}}}_createInternalHasNotTextEngine(){return{queryAll:(t,n)=>{if(t.nodeType!==1)return[];const r=t,i=Me(this._evaluator._cacheText,r),{matcher:s}=ji(n,!0);return s(i)?[]:[r]}}}_createInternalLabelEngine(){return{queryAll:(t,n)=>{const{matcher:r}=ji(n,!0);return this._evaluator._queryCSS({scope:t,pierceShadow:!0},"*").filter(s=>Yh(this._evaluator._cacheText,s).some(o=>r(o)))}}}_createNamedAttributeEngine(){return{queryAll:(n,r)=>{const i=ln(r,!0);if(i.name||i.attributes.length!==1)throw new Error("Malformed attribute selector: "+r);const{name:s,value:o,caseSensitive:l}=i.attributes[0],a=l?null:o.toLowerCase();let u;return o instanceof RegExp?u=f=>!!f.match(o):l?u=f=>f===o:u=f=>f.toLowerCase().includes(a),this._evaluator._queryCSS({scope:n,pierceShadow:!0},`[${s}]`).filter(f=>u(f.getAttribute(s)))}}}_createControlEngine(){return{queryAll(t,n){if(n==="enter-frame")return[];if(n==="return-empty")return[];if(n==="component")return t.nodeType!==1?[]:[t.childElementCount===1?t.firstElementChild:t];throw new Error(`Internal error, unknown internal:control selector ${n}`)}}}_createHasEngine(){return{queryAll:(n,r)=>n.nodeType!==1?[]:!!this.querySelector(r.parsed,n,!1)?[n]:[]}}_createHasNotEngine(){return{queryAll:(n,r)=>n.nodeType!==1?[]:!!this.querySelector(r.parsed,n,!1)?[]:[n]}}_createVisibleEngine(){return{queryAll:(n,r)=>n.nodeType!==1?[]:Cs(n)===!!r?[n]:[]}}_createInternalChainEngine(){return{queryAll:(n,r)=>this.querySelectorAll(r.parsed,n)}}extend(t,n){const r=this.window.eval(`
    (() => {
      const module = {};
      ${t}
      return module.exports.default();
    })()`);return new r(this,n)}isVisible(t){return Cs(t)}async viewportRatio(t){return await new Promise(n=>{const r=new IntersectionObserver(i=>{n(i[0].intersectionRatio),r.disconnect()});r.observe(t),requestAnimationFrame(()=>{})})}getElementBorderWidth(t){if(t.nodeType!==Node.ELEMENT_NODE||!t.ownerDocument||!t.ownerDocument.defaultView)return{left:0,top:0};const n=t.ownerDocument.defaultView.getComputedStyle(t);return{left:parseInt(n.borderLeftWidth||"",10),top:parseInt(n.borderTopWidth||"",10)}}describeIFrameStyle(t){if(!t.ownerDocument||!t.ownerDocument.defaultView)return"error:notconnected";const n=t.ownerDocument.defaultView;for(let i=t;i;i=Ee(i))if(n.getComputedStyle(i).transform!=="none")return"transformed";const r=n.getComputedStyle(t);return{left:parseInt(r.borderLeftWidth||"",10)+parseInt(r.paddingLeft||"",10),top:parseInt(r.borderTopWidth||"",10)+parseInt(r.paddingTop||"",10)}}retarget(t,n){let r=t.nodeType===Node.ELEMENT_NODE?t:t.parentElement;return r?(n==="none"||(r.matches("input, textarea, select")||(n==="button-link"?r=r.closest("button, [role=button], a, [role=link]")||r:r=r.closest("button, [role=button], [role=checkbox], [role=radio]")||r),n==="follow-label"&&(!r.matches("input, textarea, button, select, [role=button], [role=checkbox], [role=radio]")&&!r.isContentEditable&&(r=r.closest("label")||r),r.nodeName==="LABEL"&&(r=r.control||r))),r):null}async checkElementStates(t,n){if(n.includes("stable")){const r=await this._checkElementIsStable(t);if(r===!1)return{missingState:"stable"};if(r==="error:notconnected")return r}for(const r of n)if(r!=="stable"){const i=this.elementState(t,r);if(i===!1)return{missingState:r};if(i==="error:notconnected")return i}}async _checkElementIsStable(t){const n=Symbol("continuePolling");let r,i=0,s=0;const o=()=>{const f=this.retarget(t,"no-follow-label");if(!f)return"error:notconnected";const p=performance.now();if(this._stableRafCount>1&&p-s<15)return n;s=p;const v=f.getBoundingClientRect(),y={x:v.top,y:v.left,width:v.width,height:v.height};if(r){if(!(y.x===r.x&&y.y===r.y&&y.width===r.width&&y.height===r.height))return!1;if(++i>=this._stableRafCount)return!0}return r=y,n};let l,a;const u=new Promise((f,p)=>{l=f,a=p}),c=()=>{try{const f=o();f!==n?l(f):requestAnimationFrame(c)}catch(f){a(f)}};return requestAnimationFrame(c),u}elementState(t,n){const r=this.retarget(t,["stable","visible","hidden"].includes(n)?"none":"follow-label");if(!r||!r.isConnected)return n==="hidden"?!0:"error:notconnected";if(n==="visible")return this.isVisible(r);if(n==="hidden")return!this.isVisible(r);const i=Vh(r);if(n==="disabled")return i;if(n==="enabled")return!i;const s=!(["INPUT","TEXTAREA","SELECT"].includes(r.nodeName)&&r.hasAttribute("readonly"));if(n==="editable")return!i&&s;if(n==="checked"||n==="unchecked"){const o=n==="checked",l=Dh(r,!1);if(l==="error")throw this.createStacklessError("Not a checkbox or radio button");return o===l}throw this.createStacklessError(`Unexpected element state "${n}"`)}selectOptions(t,n){const r=this.retarget(t,"follow-label");if(!r)return"error:notconnected";if(r.nodeName.toLowerCase()!=="select")throw this.createStacklessError("Element is not a <select> element");const i=r,s=[...i.options],o=[];let l=n.slice();for(let a=0;a<s.length;a++){const u=s[a],c=f=>{if(f instanceof Node)return u===f;let p=!0;return f.valueOrLabel!==void 0&&(p=p&&(f.valueOrLabel===u.value||f.valueOrLabel===u.label)),f.value!==void 0&&(p=p&&f.value===u.value),f.label!==void 0&&(p=p&&f.label===u.label),f.index!==void 0&&(p=p&&f.index===a),p};if(l.some(c))if(o.push(u),i.multiple)l=l.filter(f=>!c(f));else{l=[];break}}return l.length?"error:optionsnotfound":(i.value=void 0,o.forEach(a=>a.selected=!0),i.dispatchEvent(new Event("input",{bubbles:!0,composed:!0})),i.dispatchEvent(new Event("change",{bubbles:!0})),o.map(a=>a.value))}fill(t,n){const r=this.retarget(t,"follow-label");if(!r)return"error:notconnected";if(r.nodeName.toLowerCase()==="input"){const i=r,s=i.type.toLowerCase(),o=new Set(["color","date","time","datetime-local","month","range","week"]);if(!new Set(["","email","number","password","search","tel","text","url"]).has(s)&&!o.has(s))throw this.createStacklessError(`Input of type "${s}" cannot be filled`);if(s==="number"&&(n=n.trim(),isNaN(Number(n))))throw this.createStacklessError("Cannot type text into input[type=number]");if(o.has(s)){if(n=n.trim(),i.focus(),i.value=n,i.value!==n)throw this.createStacklessError("Malformed value");return r.dispatchEvent(new Event("input",{bubbles:!0,composed:!0})),r.dispatchEvent(new Event("change",{bubbles:!0})),"done"}}else if(r.nodeName.toLowerCase()!=="textarea"){if(!r.isContentEditable)throw this.createStacklessError("Element is not an <input>, <textarea> or [contenteditable] element")}return this.selectText(r),"needsinput"}selectText(t){const n=this.retarget(t,"follow-label");if(!n)return"error:notconnected";if(n.nodeName.toLowerCase()==="input"){const s=n;return s.select(),s.focus(),"done"}if(n.nodeName.toLowerCase()==="textarea"){const s=n;return s.selectionStart=0,s.selectionEnd=s.value.length,s.focus(),"done"}const r=n.ownerDocument.createRange();r.selectNodeContents(n);const i=n.ownerDocument.defaultView.getSelection();return i&&(i.removeAllRanges(),i.addRange(r)),n.focus(),"done"}_activelyFocused(t){const n=t.getRootNode().activeElement,r=n===t&&!!t.ownerDocument&&t.ownerDocument.hasFocus();return{activeElement:n,isFocused:r}}focusNode(t,n){if(!t.isConnected)return"error:notconnected";if(t.nodeType!==Node.ELEMENT_NODE)throw this.createStacklessError("Node is not an element");const{activeElement:r,isFocused:i}=this._activelyFocused(t);if(t.isContentEditable&&!i&&r&&r.blur&&r.blur(),t.focus(),t.focus(),n&&!i&&t.nodeName.toLowerCase()==="input")try{t.setSelectionRange(0,0)}catch{}return"done"}blurNode(t){if(!t.isConnected)return"error:notconnected";if(t.nodeType!==Node.ELEMENT_NODE)throw this.createStacklessError("Node is not an element");return t.blur(),"done"}setInputFiles(t,n){if(t.nodeType!==Node.ELEMENT_NODE)return"Node is not of type HTMLElement";const r=t;if(r.nodeName!=="INPUT")return"Not an <input> element";const i=r;if((i.getAttribute("type")||"").toLowerCase()!=="file")return"Not an input[type=file] element";const o=n.map(a=>{const u=Uint8Array.from(atob(a.buffer),c=>c.charCodeAt(0));return new File([u],a.name,{type:a.mimeType,lastModified:a.lastModifiedMs})}),l=new DataTransfer;for(const a of o)l.items.add(a);i.files=l.files,i.dispatchEvent(new Event("input",{bubbles:!0,composed:!0})),i.dispatchEvent(new Event("change",{bubbles:!0}))}expectHitTarget(t,n){const r=[];let i=n;for(;i;){const c=bh(i);if(!c||(r.push(c),c.nodeType===9))break;i=c.host}let s;for(let c=r.length-1;c>=0;c--){const f=r[c],p=f.elementsFromPoint(t.x,t.y),v=f.elementFromPoint(t.x,t.y);if(v&&p[0]&&Ee(v)===p[0]){const w=this.window.getComputedStyle(v);(w==null?void 0:w.display)==="contents"&&p.unshift(v)}p[0]&&p[0].shadowRoot===f&&p[1]===v&&p.shift();const y=p[0];if(!y||(s=y,c&&y!==r[c-1].host))break}const o=[];for(;s&&s!==n;)o.push(s),s=Ee(s);if(s===n)return"done";const l=this.previewNode(o[0]||this.document.documentElement);let a,u=n;for(;u;){const c=o.indexOf(u);if(c!==-1){c>1&&(a=this.previewNode(o[c-1]));break}u=Ee(u)}return a?{hitTargetDescription:`${l} from ${a} subtree`}:{hitTargetDescription:l}}setupHitTargetInterceptor(t,n,r,i){const s=this.retarget(t,"button-link");if(!s||!s.isConnected)return"error:notconnected";if(r){const c=this.expectHitTarget(r,s);if(c!=="done")return c.hitTargetDescription}if(n==="drag")return{stop:()=>"done"};const o={hover:yp,tap:wp,mouse:xp}[n];let l;const a=c=>{if(!o.has(c.type)||!c.isTrusted)return;const f=this.window.TouchEvent&&c instanceof this.window.TouchEvent?c.touches[0]:c;l===void 0&&f&&(l=this.expectHitTarget({x:f.clientX,y:f.clientY},s)),(i||l!=="done"&&l!==void 0)&&(c.preventDefault(),c.stopPropagation(),c.stopImmediatePropagation())},u=()=>(this._hitTargetInterceptor===a&&(this._hitTargetInterceptor=void 0),l||"done");return this._hitTargetInterceptor=a,{stop:u}}dispatchEvent(t,n,r){let i;switch(r={bubbles:!0,cancelable:!0,composed:!0,...r},Wv.get(n)){case"mouse":i=new MouseEvent(n,r);break;case"keyboard":i=new KeyboardEvent(n,r);break;case"touch":i=new TouchEvent(n,r);break;case"pointer":i=new PointerEvent(n,r);break;case"focus":i=new FocusEvent(n,r);break;case"drag":i=new DragEvent(n,r);break;case"wheel":i=new WheelEvent(n,r);break;case"deviceorientation":try{i=new DeviceOrientationEvent(n,r)}catch{const{bubbles:s,cancelable:o,alpha:l,beta:a,gamma:u,absolute:c}=r;i=this.document.createEvent("DeviceOrientationEvent"),i.initDeviceOrientationEvent(n,s,o,l,a,u,c)}break;case"devicemotion":try{i=new DeviceMotionEvent(n,r)}catch{const{bubbles:s,cancelable:o,acceleration:l,accelerationIncludingGravity:a,rotationRate:u,interval:c}=r;i=this.document.createEvent("DeviceMotionEvent"),i.initDeviceMotionEvent(n,s,o,l,a,u,c)}break;default:i=new Event(n,r);break}t.dispatchEvent(i)}previewNode(t){if(t.nodeType===Node.TEXT_NODE)return Mi(`#text=${t.nodeValue||""}`);if(t.nodeType!==Node.ELEMENT_NODE)return Mi(`<${t.nodeName.toLowerCase()} />`);const n=t,r=[];for(let a=0;a<n.attributes.length;a++){const{name:u,value:c}=n.attributes[a];u!=="style"&&(!c&&Bv.has(u)?r.push(` ${u}`):r.push(` ${u}="${c}"`))}r.sort((a,u)=>a.length-u.length);const i=nc(r.join(""),50);if(Vv.has(n.nodeName))return Mi(`<${n.nodeName.toLowerCase()}${i}/>`);const s=n.childNodes;let o=!1;if(s.length<=5){o=!0;for(let a=0;a<s.length;a++)o=o&&s[a].nodeType===Node.TEXT_NODE}const l=o?n.textContent||"":s.length?"…":"";return Mi(`<${n.nodeName.toLowerCase()}${i}>${nc(l,50)}</${n.nodeName.toLowerCase()}>`)}strictModeViolationError(t,n){const r=n.slice(0,10).map(s=>({preview:this.previewNode(s),selector:this.generateSelectorSimple(s)})),i=r.map((s,o)=>`
    ${o+1}) ${s.preview} aka ${Xt(this._sdkLanguage,s.selector)}`);return r.length<n.length&&i.push(`
    ...`),this.createStacklessError(`strict mode violation: ${Xt(this._sdkLanguage,hn(t))} resolved to ${n.length} elements:${i.join("")}
`)}createStacklessError(t){if(this._browserName==="firefox"){const r=new Error("Error: "+t);return r.stack="",r}const n=new Error(t);return delete n.stack,n}createHighlight(){return new Ao(this)}maskSelectors(t,n){this._highlight&&this.hideHighlight(),this._highlight=new Ao(this),this._highlight.install();const r=[];for(const i of t)r.push(this.querySelectorAll(i,this.document.documentElement));this._highlight.maskElements(r.flat(),n)}highlight(t){this._highlight||(this._highlight=new Ao(this),this._highlight.install()),this._highlight.runHighlightOnRaf(t)}hideHighlight(){this._highlight&&(this._highlight.uninstall(),delete this._highlight)}markTargetElements(t,n){const r=new CustomEvent("__playwright_target__",{bubbles:!0,cancelable:!0,detail:n,composed:!0});for(const i of t)i.dispatchEvent(r)}_setupGlobalListenersRemovalDetection(){const t="__playwright_global_listeners_check__";let n=!1;const r=()=>n=!0;this.window.addEventListener(t,r),new MutationObserver(i=>{if(i.some(o=>Array.from(o.addedNodes).includes(this.document.documentElement))&&(n=!1,this.window.dispatchEvent(new CustomEvent(t)),!n)){this.window.addEventListener(t,r);for(const o of this.onGlobalListenersRemoved)o()}}).observe(this.document,{childList:!0})}_setupHitTargetInterceptors(){const t=r=>{var i;return(i=this._hitTargetInterceptor)==null?void 0:i.call(this,r)},n=()=>{for(const r of qv)this.window.addEventListener(r,t,{capture:!0,passive:!1})};n(),this.onGlobalListenersRemoved.add(n)}async expect(t,n,r){return n.expression==="to.have.count"||n.expression.endsWith(".array")?this.expectArray(r,n):t?await this.expectSingleElement(t,n):!n.isNot&&n.expression==="to.be.hidden"?{matches:!0}:n.isNot&&n.expression==="to.be.visible"?{matches:!1}:!n.isNot&&n.expression==="to.be.detached"?{matches:!0}:n.isNot&&n.expression==="to.be.attached"?{matches:!1}:n.isNot&&n.expression==="to.be.in.viewport"?{matches:!1}:{matches:n.isNot,missingRecevied:!0}}async expectSingleElement(t,n){var i;const r=n.expression;{let s;if(r==="to.have.attribute"?s=t.hasAttribute(n.expressionArg):r==="to.be.checked"?s=this.elementState(t,"checked"):r==="to.be.unchecked"?s=this.elementState(t,"unchecked"):r==="to.be.disabled"?s=this.elementState(t,"disabled"):r==="to.be.editable"?s=this.elementState(t,"editable"):r==="to.be.readonly"?s=!this.elementState(t,"editable"):r==="to.be.empty"?t.nodeName==="INPUT"||t.nodeName==="TEXTAREA"?s=!t.value:s=!((i=t.textContent)!=null&&i.trim()):r==="to.be.enabled"?s=this.elementState(t,"enabled"):r==="to.be.focused"?s=this._activelyFocused(t).isFocused:r==="to.be.hidden"?s=this.elementState(t,"hidden"):r==="to.be.visible"?s=this.elementState(t,"visible"):r==="to.be.attached"?s=!0:r==="to.be.detached"&&(s=!1),s!==void 0){if(s==="error:notcheckbox")throw this.createStacklessError("Element is not a checkbox");if(s==="error:notconnected")throw this.createStacklessError("Element is not connected");return{received:s,matches:s}}}if(r==="to.have.property"){let s=t;const o=n.expressionArg.split(".");for(let u=0;u<o.length-1;u++){if(typeof s!="object"||!(o[u]in s))return{received:void 0,matches:!1};s=s[o[u]]}const l=s[o[o.length-1]],a=Ol(l,n.expectedValue);return{received:l,matches:a}}if(r==="to.be.in.viewport"){const s=await this.viewportRatio(t);return{received:`viewport ratio ${s}`,matches:s>0&&s>(n.expectedNumber??0)-1e-9}}if(r==="to.have.values"){if(t=this.retarget(t,"follow-label"),t.nodeName!=="SELECT"||!t.multiple)throw this.createStacklessError("Not a select element with a multiple attribute");const s=[...t.selectedOptions].map(o=>o.value);return s.length!==n.expectedText.length?{received:s,matches:!1}:{received:s,matches:s.map((o,l)=>new Mo(n.expectedText[l]).matches(o)).every(Boolean)}}{let s;if(r==="to.have.attribute.value"){const o=t.getAttribute(n.expressionArg);if(o===null)return{received:null,matches:!1};s=o}else if(r==="to.have.class")s=t.classList.toString();else if(r==="to.have.css")s=this.window.getComputedStyle(t).getPropertyValue(n.expressionArg);else if(r==="to.have.id")s=t.id;else if(r==="to.have.text")s=n.useInnerText?t.innerText:Me(new Map,t).full;else if(r==="to.have.title")s=this.document.title;else if(r==="to.have.url")s=this.document.location.href;else if(r==="to.have.value"){if(t=this.retarget(t,"follow-label"),t.nodeName!=="INPUT"&&t.nodeName!=="TEXTAREA"&&t.nodeName!=="SELECT")throw this.createStacklessError("Not an input element");s=t.value}if(s!==void 0&&n.expectedText){const o=new Mo(n.expectedText[0]);return{received:s,matches:o.matches(s)}}}throw this.createStacklessError("Unknown expect matcher: "+r)}expectArray(t,n){const r=n.expression;if(r==="to.have.count"){const s=t.length,o=s===n.expectedNumber;return{received:s,matches:o}}let i;if(r==="to.have.text.array"||r==="to.contain.text.array"?i=t.map(s=>n.useInnerText?s.innerText:Me(new Map,s).full):r==="to.have.class.array"&&(i=t.map(s=>s.classList.toString())),i&&n.expectedText){const s=r!=="to.contain.text.array";if(!(i.length===n.expectedText.length||!s))return{received:i,matches:!1};const l=n.expectedText.map(c=>new Mo(c));let a=0,u=0;for(;a<l.length&&u<i.length;)l[a].matches(i[u])&&++a,++u;return{received:i,matches:a===l.length}}throw this.createStacklessError("Unknown expect matcher: "+r)}getElementAccessibleName(t,n){return za(t,!!n)}getAriaRole(t){return $e(t)}}const Vv=new Set(["AREA","BASE","BR","COL","COMMAND","EMBED","HR","IMG","INPUT","KEYGEN","LINK","MENUITEM","META","PARAM","SOURCE","TRACK","WBR"]),Bv=new Set(["checked","selected","disabled","readonly","multiple"]);function Mi(e){return e.replace(/\n/g,"↵").replace(/\t/g,"⇆")}const Wv=new Map([["auxclick","mouse"],["click","mouse"],["dblclick","mouse"],["mousedown","mouse"],["mouseeenter","mouse"],["mouseleave","mouse"],["mousemove","mouse"],["mouseout","mouse"],["mouseover","mouse"],["mouseup","mouse"],["mouseleave","mouse"],["mousewheel","mouse"],["keydown","keyboard"],["keyup","keyboard"],["keypress","keyboard"],["textInput","keyboard"],["touchstart","touch"],["touchmove","touch"],["touchend","touch"],["touchcancel","touch"],["pointerover","pointer"],["pointerout","pointer"],["pointerenter","pointer"],["pointerleave","pointer"],["pointerdown","pointer"],["pointerup","pointer"],["pointermove","pointer"],["pointercancel","pointer"],["gotpointercapture","pointer"],["lostpointercapture","pointer"],["focus","focus"],["blur","focus"],["drag","drag"],["dragstart","drag"],["dragend","drag"],["dragover","drag"],["dragenter","drag"],["dragleave","drag"],["dragexit","drag"],["drop","drag"],["wheel","wheel"],["deviceorientation","deviceorientation"],["deviceorientationabsolute","deviceorientation"],["devicemotion","devicemotion"]]),yp=new Set(["mousemove"]),wp=new Set(["pointerdown","pointerup","touchstart","touchend","touchcancel"]),xp=new Set(["mousedown","mouseup","pointerdown","pointerup","click","auxclick","dblclick","contextmenu"]),qv=new Set([...yp,...wp,...xp]);function Xv(e){if(e=e.substring(1,e.length-1),!e.includes("\\"))return e;const t=[];let n=0;for(;n<e.length;)e[n]==="\\"&&n+1<e.length&&n++,t.push(e[n++]);return t.join("")}function ji(e,t){if(e[0]==="/"&&e.lastIndexOf("/")>0){const i=e.lastIndexOf("/"),s=new RegExp(e.substring(1,i),e.substring(i+1));return{matcher:o=>s.test(o.full),kind:"regex"}}const n=t?JSON.parse.bind(JSON):Xv;let r=!1;return e.length>1&&e[0]==='"'&&e[e.length-1]==='"'?(e=n(e),r=!0):t&&e.length>1&&e[0]==='"'&&e[e.length-2]==='"'&&e[e.length-1]==="i"?(e=n(e.substring(0,e.length-1)),r=!1):t&&e.length>1&&e[0]==='"'&&e[e.length-2]==='"'&&e[e.length-1]==="s"?(e=n(e.substring(0,e.length-1)),r=!0):e.length>1&&e[0]==="'"&&e[e.length-1]==="'"&&(e=n(e),r=!0),e=Fe(e),r?t?{kind:"strict",matcher:s=>s.normalized===e}:{matcher:s=>!e&&!s.immediate.length?!0:s.immediate.some(o=>Fe(o)===e),kind:"strict"}:(e=e.toLowerCase(),{kind:"lax",matcher:i=>i.normalized.toLowerCase().includes(e)})}class Mo{constructor(t){if(this._normalizeWhiteSpace=t.normalizeWhiteSpace,this._ignoreCase=t.ignoreCase,this._string=t.matchSubstring?void 0:this.normalize(t.string),this._substring=t.matchSubstring?this.normalize(t.string):void 0,t.regexSource){const n=new Set((t.regexFlags||"").split(""));t.ignoreCase===!1&&n.delete("i"),t.ignoreCase===!0&&n.add("i"),this._regex=new RegExp(t.regexSource,[...n].join(""))}}matches(t){return this._regex||(t=this.normalize(t)),this._string!==void 0?t===this._string:this._substring!==void 0?t.includes(this._substring):this._regex?!!this._regex.test(t):!1}normalize(t){return t&&(this._normalizeWhiteSpace&&(t=Fe(t)),this._ignoreCase&&(t=t.toLocaleLowerCase()),t)}}function Ol(e,t){if(e===t)return!0;if(e&&t&&typeof e=="object"&&typeof t=="object"){if(e.constructor!==t.constructor)return!1;if(Array.isArray(e)){if(e.length!==t.length)return!1;for(let r=0;r<e.length;++r)if(!Ol(e[r],t[r]))return!1;return!0}if(e instanceof RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r=0;r<n.length;++r)if(!t.hasOwnProperty(n[r]))return!1;for(const r of n)if(!Ol(e[r],t[r]))return!1;return!0}return typeof e=="number"&&typeof t=="number"?isNaN(e)&&isNaN(t):!1}class kc{cursor(){return"default"}}class jo{constructor(t,n){this._hoveredModel=null,this._hoveredElement=null,this._hoveredSelectors=null,this._recorder=t,this._assertVisibility=n}cursor(){return"pointer"}cleanup(){this._hoveredModel=null,this._hoveredElement=null,this._hoveredSelectors=null}onClick(t){var n;q(t),t.button===0&&(n=this._hoveredModel)!=null&&n.selector&&this._commit(this._hoveredModel.selector)}onContextMenu(t){if(this._hoveredModel&&!this._hoveredModel.tooltipListItemSelected&&this._hoveredSelectors&&this._hoveredSelectors.length>1){q(t);const n=this._hoveredSelectors;this._hoveredModel.tooltipFooter=void 0,this._hoveredModel.tooltipList=n.map(r=>this._recorder.injectedScript.utils.asLocator(this._recorder.state.language,r)),this._hoveredModel.tooltipListItemSelected=r=>{r===void 0?this._reset(!0):this._commit(n[r])},this._recorder.updateHighlight(this._hoveredModel,!0)}}onPointerDown(t){q(t)}onPointerUp(t){q(t)}onMouseDown(t){q(t)}onMouseUp(t){q(t)}onMouseMove(t){var s;q(t);let n=this._recorder.deepEventTarget(t);if(n.isConnected||(n=null),this._hoveredElement===n)return;this._hoveredElement=n;let r=null,i=[];if(this._hoveredElement){const o=this._recorder.injectedScript.generateSelector(this._hoveredElement,{testIdAttributeName:this._recorder.state.testIdAttributeName,multiple:!1});i=o.selectors,r={selector:o.selector,elements:o.elements,tooltipText:this._recorder.injectedScript.utils.asLocator(this._recorder.state.language,o.selector),tooltipFooter:i.length>1?"Click to select, right-click for more options":void 0,color:this._assertVisibility?"#8acae480":void 0}}((s=this._hoveredModel)==null?void 0:s.selector)!==(r==null?void 0:r.selector)&&(this._hoveredModel=r,this._hoveredSelectors=i,this._recorder.updateHighlight(r,!0))}onMouseEnter(t){q(t)}onMouseLeave(t){q(t);const n=this._recorder.injectedScript.window;n.top!==n&&this._recorder.deepEventTarget(t).nodeType===Node.DOCUMENT_NODE&&this._reset(!0)}onKeyDown(t){var n,r,i;q(t),t.key==="Escape"&&((n=this._hoveredModel)!=null&&n.tooltipListItemSelected?this._reset(!0):this._assertVisibility&&((i=(r=this._recorder.delegate).setMode)==null||i.call(r,"recording")))}onKeyUp(t){q(t)}onScroll(t){this._reset(!1)}_commit(t){var n,r,i,s,o,l,a;this._assertVisibility?((r=(n=this._recorder.delegate).recordAction)==null||r.call(n,{name:"assertVisible",selector:t,signals:[]}),(s=(i=this._recorder.delegate).setMode)==null||s.call(i,"recording"),(o=this._recorder.overlay)==null||o.flashToolSucceeded("assertingVisibility")):(a=(l=this._recorder.delegate).setSelector)==null||a.call(l,t)}_reset(t){this._hoveredElement=null,this._hoveredModel=null,this._hoveredSelectors=null,this._recorder.updateHighlight(null,t)}}class Qv{constructor(t){this._performingAction=!1,this._hoveredModel=null,this._hoveredElement=null,this._activeModel=null,this._expectProgrammaticKeyUp=!1,this._recorder=t}cursor(){return"pointer"}cleanup(){this._hoveredModel=null,this._hoveredElement=null,this._activeModel=null,this._expectProgrammaticKeyUp=!1}onClick(t){if(Nc(this._hoveredElement)||this._shouldIgnoreMouseEvent(t)||this._actionInProgress(t)||this._consumedDueToNoModel(t,this._hoveredModel))return;const n=Io(this._recorder.deepEventTarget(t));if(n){this._performAction({name:n.checked?"check":"uncheck",selector:this._hoveredModel.selector,signals:[]});return}this._performAction({name:"click",selector:this._hoveredModel.selector,position:Zv(t),signals:[],button:Jv(t),modifiers:Cc(t),clickCount:t.detail})}onPointerDown(t){this._shouldIgnoreMouseEvent(t)||this._performingAction||q(t)}onPointerUp(t){this._shouldIgnoreMouseEvent(t)||this._performingAction||q(t)}onMouseDown(t){this._shouldIgnoreMouseEvent(t)||(this._performingAction||q(t),this._activeModel=this._hoveredModel)}onMouseUp(t){this._shouldIgnoreMouseEvent(t)||this._performingAction||q(t)}onMouseMove(t){const n=this._recorder.deepEventTarget(t);this._hoveredElement!==n&&(this._hoveredElement=n,this._updateModelForHoveredElement())}onMouseLeave(t){const n=this._recorder.injectedScript.window;n.top!==n&&this._recorder.deepEventTarget(t).nodeType===Node.DOCUMENT_NODE&&(this._hoveredElement=null,this._updateModelForHoveredElement())}onFocus(t){this._onFocus(!0)}onInput(t){var r,i,s,o,l,a;const n=this._recorder.deepEventTarget(t);if(n.nodeName==="INPUT"&&n.type.toLowerCase()==="file"){(i=(r=this._recorder.delegate).recordAction)==null||i.call(r,{name:"setInputFiles",selector:this._activeModel.selector,signals:[],files:[...n.files||[]].map(u=>u.name)});return}if(Nc(n)){(o=(s=this._recorder.delegate).recordAction)==null||o.call(s,{name:"fill",selector:this._hoveredModel.selector,signals:[],text:n.value});return}if(["INPUT","TEXTAREA"].includes(n.nodeName)||n.isContentEditable){if(n.nodeName==="INPUT"&&["checkbox","radio"].includes(n.type.toLowerCase())||this._consumedDueWrongTarget(t))return;(a=(l=this._recorder.delegate).recordAction)==null||a.call(l,{name:"fill",selector:this._activeModel.selector,signals:[],text:n.isContentEditable?n.innerText:n.value})}if(n.nodeName==="SELECT"){const u=n;if(this._actionInProgress(t))return;this._performAction({name:"select",selector:this._hoveredModel.selector,options:[...u.selectedOptions].map(c=>c.value),signals:[]})}}onKeyDown(t){if(this._shouldGenerateKeyPressFor(t)){if(this._actionInProgress(t)){this._expectProgrammaticKeyUp=!0;return}if(!this._consumedDueWrongTarget(t)){if(t.key===" "){const n=Io(this._recorder.deepEventTarget(t));if(n){this._performAction({name:n.checked?"uncheck":"check",selector:this._activeModel.selector,signals:[]});return}}this._performAction({name:"press",selector:this._activeModel.selector,signals:[],key:t.key,modifiers:Cc(t)})}}}onKeyUp(t){if(this._shouldGenerateKeyPressFor(t)){if(!this._expectProgrammaticKeyUp){q(t);return}this._expectProgrammaticKeyUp=!1}}onScroll(t){this._hoveredModel=null,this._hoveredElement=null,this._recorder.updateHighlight(null,!1)}_onFocus(t){const n=Yv(this._recorder.document);if(t&&n===this._recorder.document.body)return;const r=n?this._recorder.injectedScript.generateSelector(n,{testIdAttributeName:this._recorder.state.testIdAttributeName}):null;this._activeModel=r&&r.selector?r:null,t&&(this._hoveredElement=n),this._updateModelForHoveredElement()}_shouldIgnoreMouseEvent(t){const n=this._recorder.deepEventTarget(t),r=n.nodeName;return!!(r==="SELECT"||r==="OPTION"||r==="INPUT"&&["date","range"].includes(n.type))}_actionInProgress(t){return this._performingAction?!0:(q(t),!1)}_consumedDueToNoModel(t,n){return n?!1:(q(t),!0)}_consumedDueWrongTarget(t){return this._activeModel&&this._activeModel.elements[0]===this._recorder.deepEventTarget(t)?!1:(q(t),!0)}async _performAction(t){var n,r;this._hoveredElement=null,this._hoveredModel=null,this._activeModel=null,this._recorder.updateHighlight(null,!1),this._performingAction=!0,await((r=(n=this._recorder.delegate).performAction)==null?void 0:r.call(n,t).catch(()=>{})),this._performingAction=!1,this._onFocus(!1),this._recorder.injectedScript.isUnderTest&&console.error("Action performed for test: "+JSON.stringify({hovered:this._hoveredModel?this._hoveredModel.selector:null,active:this._activeModel?this._activeModel.selector:null}))}_shouldGenerateKeyPressFor(t){if(t.key==="Enter"&&(this._recorder.deepEventTarget(t).nodeName==="TEXTAREA"||this._recorder.deepEventTarget(t).isContentEditable)||["Backspace","Delete","AltGraph"].includes(t.key)||t.key==="@"&&t.code==="KeyL")return!1;if(navigator.platform.includes("Mac")){if(t.key==="v"&&t.metaKey)return!1}else if(t.key==="v"&&t.ctrlKey||t.key==="Insert"&&t.shiftKey)return!1;if(["Shift","Control","Meta","Alt","Process"].includes(t.key))return!1;const n=t.ctrlKey||t.altKey||t.metaKey;return t.key.length===1&&!n?!!Io(this._recorder.deepEventTarget(t)):!0}_updateModelForHoveredElement(){if(!this._hoveredElement||!this._hoveredElement.isConnected){this._hoveredModel=null,this._hoveredElement=null,this._recorder.updateHighlight(null,!0);return}const{selector:t,elements:n}=this._recorder.injectedScript.generateSelector(this._hoveredElement,{testIdAttributeName:this._recorder.state.testIdAttributeName});this._hoveredModel&&this._hoveredModel.selector===t||(this._hoveredModel=t?{selector:t,elements:n,color:"#dc6f6f7f"}:null,this._recorder.updateHighlight(this._hoveredModel,!0))}}class Tc{constructor(t,n){this._hoverHighlight=null,this._action=null,this._dialogElement=null,this._textCache=new Map,this._recorder=t,this._kind=n,this._acceptButton=this._recorder.document.createElement("x-pw-tool-item"),this._acceptButton.title="Accept",this._acceptButton.classList.add("accept"),this._acceptButton.appendChild(this._recorder.document.createElement("x-div")),this._acceptButton.addEventListener("click",()=>this._commit()),this._cancelButton=this._recorder.document.createElement("x-pw-tool-item"),this._cancelButton.title="Close",this._cancelButton.classList.add("cancel"),this._cancelButton.appendChild(this._recorder.document.createElement("x-div")),this._cancelButton.addEventListener("click",()=>this._closeDialog())}cursor(){return"pointer"}cleanup(){this._closeDialog(),this._hoverHighlight=null}onClick(t){q(t),this._kind==="value"?this._commitAssertValue():this._dialogElement||this._showDialog()}onMouseDown(t){const n=this._recorder.deepEventTarget(t);this._elementHasValue(n)&&t.preventDefault()}onPointerUp(t){var r;const n=(r=this._hoverHighlight)==null?void 0:r.elements[0];this._kind==="value"&&n&&n.nodeName==="INPUT"&&n.disabled&&this._commitAssertValue()}onMouseMove(t){var r;if(this._dialogElement)return;const n=this._recorder.deepEventTarget(t);((r=this._hoverHighlight)==null?void 0:r.elements[0])!==n&&(this._kind==="text"?this._hoverHighlight=this._recorder.injectedScript.utils.elementText(this._textCache,n).full?{elements:[n],selector:""}:null:this._hoverHighlight=this._elementHasValue(n)?this._recorder.injectedScript.generateSelector(n,{testIdAttributeName:this._recorder.state.testIdAttributeName}):null,this._hoverHighlight&&(this._hoverHighlight.color="#8acae480"),this._recorder.updateHighlight(this._hoverHighlight,!0))}onKeyDown(t){var n,r;t.key==="Escape"&&((r=(n=this._recorder.delegate).setMode)==null||r.call(n,"recording")),q(t)}onScroll(t){this._recorder.updateHighlight(this._hoverHighlight,!1)}_elementHasValue(t){return t.nodeName==="TEXTAREA"||t.nodeName==="SELECT"||t.nodeName==="INPUT"&&!["button","image","reset","submit"].includes(t.type)}_generateAction(){var n;this._textCache.clear();const t=(n=this._hoverHighlight)==null?void 0:n.elements[0];if(!t)return null;if(this._kind==="value"){if(!this._elementHasValue(t))return null;const{selector:r}=this._recorder.injectedScript.generateSelector(t,{testIdAttributeName:this._recorder.state.testIdAttributeName});return t.nodeName==="INPUT"&&["checkbox","radio"].includes(t.type.toLowerCase())?{name:"assertChecked",selector:r,signals:[],checked:!t.checked}:{name:"assertValue",selector:r,signals:[],value:t.value}}else return this._hoverHighlight=this._recorder.injectedScript.generateSelector(t,{testIdAttributeName:this._recorder.state.testIdAttributeName,forTextExpect:!0}),this._hoverHighlight.color="#8acae480",this._recorder.updateHighlight(this._hoverHighlight,!0),{name:"assertText",selector:this._hoverHighlight.selector,signals:[],text:this._recorder.injectedScript.utils.elementText(this._textCache,t).normalized,substring:!0}}_renderValue(t){return(t==null?void 0:t.name)==="assertText"?this._recorder.injectedScript.utils.normalizeWhiteSpace(t.text):(t==null?void 0:t.name)==="assertChecked"?String(t.checked):(t==null?void 0:t.name)==="assertValue"?t.value:""}_commit(){var t,n,r,i;!this._action||!this._dialogElement||(this._closeDialog(),(n=(t=this._recorder.delegate).recordAction)==null||n.call(t,this._action),(i=(r=this._recorder.delegate).setMode)==null||i.call(r,"recording"))}_showDialog(){var a;if(!((a=this._hoverHighlight)!=null&&a.elements[0])||(this._action=this._generateAction(),!this._action||this._action.name!=="assertText"))return;this._dialogElement=this._recorder.document.createElement("x-pw-dialog"),this._keyboardListener=u=>{if(u.key==="Escape"){this._closeDialog();return}if(u.key==="Enter"&&(u.ctrlKey||u.metaKey)){this._dialogElement&&this._commit();return}},this._recorder.document.addEventListener("keydown",this._keyboardListener,!0);const t=this._recorder.document.createElement("x-pw-tools-list"),n=this._recorder.document.createElement("label");n.textContent="Assert that element contains text",t.appendChild(n),t.appendChild(this._recorder.document.createElement("x-spacer")),t.appendChild(this._acceptButton),t.appendChild(this._cancelButton),this._dialogElement.appendChild(t);const r=this._recorder.document.createElement("x-pw-dialog-body"),i=this._action,s=this._recorder.document.createElement("textarea");s.setAttribute("spellcheck","false"),s.value=this._renderValue(this._action),s.classList.add("text-editor");const o=()=>{var v;const u=this._recorder.injectedScript.utils.normalizeWhiteSpace(s.value),c=(v=this._hoverHighlight)==null?void 0:v.elements[0];if(!c)return;i.text=u;const f=this._recorder.injectedScript.utils.elementText(this._textCache,c).normalized,p=u&&f.includes(u);s.classList.toggle("does-not-match",!p)};s.addEventListener("input",o),r.appendChild(s),this._dialogElement.appendChild(r),this._recorder.highlight.appendChild(this._dialogElement);const l=this._recorder.highlight.tooltipPosition(this._recorder.highlight.firstBox(),this._dialogElement);this._dialogElement.style.top=l.anchorTop+"px",this._dialogElement.style.left=l.anchorLeft+"px",s.focus()}_closeDialog(){this._dialogElement&&(this._dialogElement.remove(),this._recorder.document.removeEventListener("keydown",this._keyboardListener),this._dialogElement=null)}_commitAssertValue(){var n,r,i,s,o;if(this._kind!=="value")return;const t=this._generateAction();t&&((r=(n=this._recorder.delegate).recordAction)==null||r.call(n,t),(s=(i=this._recorder.delegate).setMode)==null||s.call(i,"recording"),(o=this._recorder.overlay)==null||o.flashToolSucceeded("assertingValue"))}}class Kv{constructor(t){this._listeners=[],this._offsetX=0,this._measure={width:0,height:0},this._recorder=t;const n=this._recorder.injectedScript.document;this._overlayElement=n.createElement("x-pw-overlay");const r=n.createElement("x-pw-tools-list");this._overlayElement.appendChild(r),this._dragHandle=n.createElement("x-pw-tool-gripper"),this._dragHandle.appendChild(n.createElement("x-div")),r.appendChild(this._dragHandle),this._recordToggle=this._recorder.injectedScript.document.createElement("x-pw-tool-item"),this._recordToggle.title="Record",this._recordToggle.classList.add("record"),this._recordToggle.appendChild(this._recorder.injectedScript.document.createElement("x-div")),r.appendChild(this._recordToggle),this._pickLocatorToggle=this._recorder.injectedScript.document.createElement("x-pw-tool-item"),this._pickLocatorToggle.title="Pick locator",this._pickLocatorToggle.classList.add("pick-locator"),this._pickLocatorToggle.appendChild(this._recorder.injectedScript.document.createElement("x-div")),r.appendChild(this._pickLocatorToggle),this._assertVisibilityToggle=this._recorder.injectedScript.document.createElement("x-pw-tool-item"),this._assertVisibilityToggle.title="Assert visibility",this._assertVisibilityToggle.classList.add("visibility"),this._assertVisibilityToggle.appendChild(this._recorder.injectedScript.document.createElement("x-div")),r.appendChild(this._assertVisibilityToggle),this._assertTextToggle=this._recorder.injectedScript.document.createElement("x-pw-tool-item"),this._assertTextToggle.title="Assert text",this._assertTextToggle.classList.add("text"),this._assertTextToggle.appendChild(this._recorder.injectedScript.document.createElement("x-div")),r.appendChild(this._assertTextToggle),this._assertValuesToggle=this._recorder.injectedScript.document.createElement("x-pw-tool-item"),this._assertValuesToggle.title="Assert value",this._assertValuesToggle.classList.add("value"),this._assertValuesToggle.appendChild(this._recorder.injectedScript.document.createElement("x-div")),r.appendChild(this._assertValuesToggle),this._updateVisualPosition(),this._refreshListeners()}_refreshListeners(){Sp(this._listeners),this._listeners=[Y(this._dragHandle,"mousedown",t=>{this._dragState={offsetX:this._offsetX,dragStart:{x:t.clientX,y:0}}}),Y(this._recordToggle,"click",()=>{var t,n;(n=(t=this._recorder.delegate).setMode)==null||n.call(t,this._recorder.state.mode==="none"||this._recorder.state.mode==="standby"||this._recorder.state.mode==="inspecting"?"recording":"standby")}),Y(this._pickLocatorToggle,"click",()=>{var n,r;const t={inspecting:"standby",none:"inspecting",standby:"inspecting",recording:"recording-inspecting","recording-inspecting":"recording",assertingText:"recording-inspecting",assertingVisibility:"recording-inspecting",assertingValue:"recording-inspecting"};(r=(n=this._recorder.delegate).setMode)==null||r.call(n,t[this._recorder.state.mode])}),Y(this._assertVisibilityToggle,"click",()=>{var t,n;this._assertVisibilityToggle.classList.contains("disabled")||(n=(t=this._recorder.delegate).setMode)==null||n.call(t,this._recorder.state.mode==="assertingVisibility"?"recording":"assertingVisibility")}),Y(this._assertTextToggle,"click",()=>{var t,n;this._assertTextToggle.classList.contains("disabled")||(n=(t=this._recorder.delegate).setMode)==null||n.call(t,this._recorder.state.mode==="assertingText"?"recording":"assertingText")}),Y(this._assertValuesToggle,"click",()=>{var t,n;this._assertValuesToggle.classList.contains("disabled")||(n=(t=this._recorder.delegate).setMode)==null||n.call(t,this._recorder.state.mode==="assertingValue"?"recording":"assertingValue")})]}install(){this._recorder.highlight.appendChild(this._overlayElement),this._refreshListeners(),this._updateVisualPosition()}contains(t){return this._recorder.injectedScript.utils.isInsideScope(this._overlayElement,t)}setUIState(t){this._recordToggle.classList.toggle("active",t.mode==="recording"||t.mode==="assertingText"||t.mode==="assertingVisibility"||t.mode==="assertingValue"||t.mode==="recording-inspecting"),this._pickLocatorToggle.classList.toggle("active",t.mode==="inspecting"||t.mode==="recording-inspecting"),this._assertVisibilityToggle.classList.toggle("active",t.mode==="assertingVisibility"),this._assertVisibilityToggle.classList.toggle("disabled",t.mode==="none"||t.mode==="standby"||t.mode==="inspecting"),this._assertTextToggle.classList.toggle("active",t.mode==="assertingText"),this._assertTextToggle.classList.toggle("disabled",t.mode==="none"||t.mode==="standby"||t.mode==="inspecting"),this._assertValuesToggle.classList.toggle("active",t.mode==="assertingValue"),this._assertValuesToggle.classList.toggle("disabled",t.mode==="none"||t.mode==="standby"||t.mode==="inspecting"),this._offsetX!==t.overlay.offsetX&&(this._offsetX=t.overlay.offsetX,this._updateVisualPosition()),t.mode==="none"?this._hideOverlay():this._showOverlay()}flashToolSucceeded(t){const n=t==="assertingVisibility"?this._assertVisibilityToggle:this._assertValuesToggle;n.classList.add("succeeded"),setTimeout(()=>n.classList.remove("succeeded"),2e3)}_hideOverlay(){this._overlayElement.setAttribute("hidden","true")}_showOverlay(){this._overlayElement.hasAttribute("hidden")&&(this._overlayElement.removeAttribute("hidden"),this._updateVisualPosition())}_updateVisualPosition(){this._measure=this._overlayElement.getBoundingClientRect(),this._overlayElement.style.left=(this._recorder.injectedScript.window.innerWidth-this._measure.width)/2+this._offsetX+"px"}onMouseMove(t){var n,r;if(!t.buttons)return this._dragState=void 0,!1;if(this._dragState){this._offsetX=this._dragState.offsetX+t.clientX-this._dragState.dragStart.x;const i=(this._recorder.injectedScript.window.innerWidth-this._measure.width)/2-10;return this._offsetX=Math.max(-i,Math.min(i,this._offsetX)),this._updateVisualPosition(),(r=(n=this._recorder.delegate).setOverlayState)==null||r.call(n,{offsetX:this._offsetX}),q(t),!0}return!1}onMouseUp(t){return this._dragState?(q(t),!0):!1}onClick(t){return this._dragState?(this._dragState=void 0,q(t),!0):!1}}class Gv{constructor(t){this._listeners=[],this._actionSelectorModel=null,this.state={mode:"none",testIdAttributeName:"data-testid",language:"javascript",overlay:{offsetX:0}},this.delegate={},this.document=t.document,this.injectedScript=t,this.highlight=t.createHighlight(),this._tools={none:new kc,standby:new kc,inspecting:new jo(this,!1),recording:new Qv(this),"recording-inspecting":new jo(this,!1),assertingText:new Tc(this,"text"),assertingVisibility:new jo(this,!0),assertingValue:new Tc(this,"value")},this._currentTool=this._tools.none,t.window.top===t.window&&(this.overlay=new Kv(this),this.overlay.setUIState(this.state)),this._styleElement=this.document.createElement("style"),this._styleElement.textContent=`
      body[data-pw-cursor=pointer] *, body[data-pw-cursor=pointer] *::after { cursor: pointer !important; }
      body[data-pw-cursor=text] *, body[data-pw-cursor=text] *::after { cursor: text !important; }
    `,this.installListeners(),t.isUnderTest&&console.error("Recorder script ready for test")}installListeners(){var t;Sp(this._listeners),this._listeners=[Y(this.document,"click",n=>this._onClick(n),!0),Y(this.document,"auxclick",n=>this._onClick(n),!0),Y(this.document,"contextmenu",n=>this._onContextMenu(n),!0),Y(this.document,"dragstart",n=>this._onDragStart(n),!0),Y(this.document,"input",n=>this._onInput(n),!0),Y(this.document,"keydown",n=>this._onKeyDown(n),!0),Y(this.document,"keyup",n=>this._onKeyUp(n),!0),Y(this.document,"pointerdown",n=>this._onPointerDown(n),!0),Y(this.document,"pointerup",n=>this._onPointerUp(n),!0),Y(this.document,"mousedown",n=>this._onMouseDown(n),!0),Y(this.document,"mouseup",n=>this._onMouseUp(n),!0),Y(this.document,"mousemove",n=>this._onMouseMove(n),!0),Y(this.document,"mouseleave",n=>this._onMouseLeave(n),!0),Y(this.document,"mouseenter",n=>this._onMouseEnter(n),!0),Y(this.document,"focus",n=>this._onFocus(n),!0),Y(this.document,"scroll",n=>this._onScroll(n),!0)],this.highlight.install(),(t=this.overlay)==null||t.install(),this.injectedScript.document.head.appendChild(this._styleElement)}_switchCurrentTool(){var n,r,i;const t=this._tools[this.state.mode];t!==this._currentTool&&((r=(n=this._currentTool).cleanup)==null||r.call(n),this.clearHighlight(),this._currentTool=t,(i=this.injectedScript.document.body)==null||i.setAttribute("data-pw-cursor",t.cursor()))}setUIState(t,n){var r,i,s,o;this.delegate=n,t.actionPoint&&this.state.actionPoint&&t.actionPoint.x===this.state.actionPoint.x&&t.actionPoint.y===this.state.actionPoint.y||!t.actionPoint&&!this.state.actionPoint||(t.actionPoint?this.highlight.showActionPoint(t.actionPoint.x,t.actionPoint.y):this.highlight.hideActionPoint()),this.state=t,this.highlight.setLanguage(t.language),this._switchCurrentTool(),(r=this.overlay)==null||r.setUIState(t),(i=this._actionSelectorModel)!=null&&i.selector&&!((s=this._actionSelectorModel)!=null&&s.elements.length)&&(this._actionSelectorModel=null),t.actionSelector!==((o=this._actionSelectorModel)==null?void 0:o.selector)&&(this._actionSelectorModel=t.actionSelector?ey(this.injectedScript,t.actionSelector,this.document):null),(this.state.mode==="none"||this.state.mode==="standby")&&this.updateHighlight(this._actionSelectorModel,!1)}clearHighlight(){var t,n;(n=(t=this._currentTool).cleanup)==null||n.call(t),this.updateHighlight(null,!1)}_onClick(t){var n,r,i;t.isTrusted&&((n=this.overlay)!=null&&n.onClick(t)||this._ignoreOverlayEvent(t)||(i=(r=this._currentTool).onClick)==null||i.call(r,t))}_onContextMenu(t){var n,r;t.isTrusted&&(this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onContextMenu)==null||r.call(n,t))}_onDragStart(t){var n,r;t.isTrusted&&(this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onDragStart)==null||r.call(n,t))}_onPointerDown(t){var n,r;t.isTrusted&&(this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onPointerDown)==null||r.call(n,t))}_onPointerUp(t){var n,r;t.isTrusted&&(this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onPointerUp)==null||r.call(n,t))}_onMouseDown(t){var n,r;t.isTrusted&&(this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onMouseDown)==null||r.call(n,t))}_onMouseUp(t){var n,r,i;t.isTrusted&&((n=this.overlay)!=null&&n.onMouseUp(t)||this._ignoreOverlayEvent(t)||(i=(r=this._currentTool).onMouseUp)==null||i.call(r,t))}_onMouseMove(t){var n,r,i;t.isTrusted&&((n=this.overlay)!=null&&n.onMouseMove(t)||this._ignoreOverlayEvent(t)||(i=(r=this._currentTool).onMouseMove)==null||i.call(r,t))}_onMouseEnter(t){var n,r;t.isTrusted&&(this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onMouseEnter)==null||r.call(n,t))}_onMouseLeave(t){var n,r;t.isTrusted&&(this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onMouseLeave)==null||r.call(n,t))}_onFocus(t){var n,r;t.isTrusted&&(this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onFocus)==null||r.call(n,t))}_onScroll(t){var n,r;t.isTrusted&&(this.highlight.hideActionPoint(),(r=(n=this._currentTool).onScroll)==null||r.call(n,t))}_onInput(t){var n,r;this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onInput)==null||r.call(n,t)}_onKeyDown(t){var n,r;t.isTrusted&&(this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onKeyDown)==null||r.call(n,t))}_onKeyUp(t){var n,r;t.isTrusted&&(this._ignoreOverlayEvent(t)||(r=(n=this._currentTool).onKeyUp)==null||r.call(n,t))}updateHighlight(t,n){var i,s;let r=t==null?void 0:t.tooltipText;r===void 0&&!(t!=null&&t.tooltipList)&&(t!=null&&t.selector)&&(r=this.injectedScript.utils.asLocator(this.state.language,t.selector)),this.highlight.updateHighlight((t==null?void 0:t.elements)||[],{...t,tooltipText:r}),n&&((s=(i=this.delegate).highlightUpdated)==null||s.call(i))}_ignoreOverlayEvent(t){return t.composedPath().some(n=>(n.nodeName||"").toLowerCase()==="x-pw-glass")}deepEventTarget(t){var n;for(const r of t.composedPath())if(!((n=this.overlay)!=null&&n.contains(r)))return r;return t.composedPath()[0]}}function Yv(e){let t=e.activeElement;for(;t&&t.shadowRoot&&t.shadowRoot.activeElement;)t=t.shadowRoot.activeElement;return t}function Cc(e){return(e.altKey?1:0)|(e.ctrlKey?2:0)|(e.metaKey?4:0)|(e.shiftKey?8:0)}function Jv(e){switch(e.which){case 1:return"left";case 2:return"middle";case 3:return"right"}return"left"}function Zv(e){if(e.target.nodeName==="CANVAS")return{x:e.offsetX,y:e.offsetY}}function q(e){e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation()}function Io(e){if(!e||e.nodeName!=="INPUT")return null;const t=e;return["checkbox","radio"].includes(t.type)?t:null}function Nc(e){return!e||e.nodeName!=="INPUT"?!1:e.type.toLowerCase()==="range"}function Y(e,t,n,r){return e.addEventListener(t,n,r),()=>{e.removeEventListener(t,n,r)}}function Sp(e){for(const t of e)t();e.splice(0,e.length)}function ey(e,t,n){try{const r=e.parseSelector(t);return{selector:t,elements:e.querySelectorAll(r,n)}}catch{return{selector:t,elements:[]}}}function Ba(e,t,n){return`internal:attr=[${e}=${_e(t,(n==null?void 0:n.exact)||!1)}]`}function ty(e,t){return`internal:testid=[${e}=${_e(t,!0)}]`}function ny(e,t){return"internal:label="+st(e,!!(t!=null&&t.exact))}function ry(e,t){return Ba("alt",e,t)}function iy(e,t){return Ba("title",e,t)}function sy(e,t){return Ba("placeholder",e,t)}function oy(e,t){return"internal:text="+st(e,!!(t!=null&&t.exact))}function ly(e,t={}){const n=[];return t.checked!==void 0&&n.push(["checked",String(t.checked)]),t.disabled!==void 0&&n.push(["disabled",String(t.disabled)]),t.selected!==void 0&&n.push(["selected",String(t.selected)]),t.expanded!==void 0&&n.push(["expanded",String(t.expanded)]),t.includeHidden!==void 0&&n.push(["include-hidden",String(t.includeHidden)]),t.level!==void 0&&n.push(["level",String(t.level)]),t.name!==void 0&&n.push(["name",_e(t.name,!!t.exact)]),t.pressed!==void 0&&n.push(["pressed",String(t.pressed)]),`internal:role=${e}${n.map(([r,i])=>`[${r}=${i}]`).join("")}`}const xr=Symbol("selector");class On{constructor(t,n,r){if(r!=null&&r.hasText&&(n+=` >> internal:has-text=${st(r.hasText,!1)}`),r!=null&&r.hasNotText&&(n+=` >> internal:has-not-text=${st(r.hasNotText,!1)}`),r!=null&&r.has&&(n+=" >> internal:has="+JSON.stringify(r.has[xr])),r!=null&&r.hasNot&&(n+=" >> internal:has-not="+JSON.stringify(r.hasNot[xr])),this[xr]=n,n){const o=t.parseSelector(n);this.element=t.querySelector(o,t.document,!1),this.elements=t.querySelectorAll(o,t.document)}const i=n,s=this;s.locator=(o,l)=>new On(t,i?i+" >> "+o:o,l),s.getByTestId=o=>s.locator(ty(t.testIdAttributeNameForStrictErrorAndConsoleCodegen(),o)),s.getByAltText=(o,l)=>s.locator(ry(o,l)),s.getByLabel=(o,l)=>s.locator(ny(o,l)),s.getByPlaceholder=(o,l)=>s.locator(sy(o,l)),s.getByText=(o,l)=>s.locator(oy(o,l)),s.getByTitle=(o,l)=>s.locator(iy(o,l)),s.getByRole=(o,l={})=>s.locator(ly(o,l)),s.filter=o=>new On(t,n,o),s.first=()=>s.locator("nth=0"),s.last=()=>s.locator("nth=-1"),s.nth=o=>s.locator(`nth=${o}`),s.and=o=>new On(t,i+" >> internal:and="+JSON.stringify(o[xr])),s.or=o=>new On(t,i+" >> internal:or="+JSON.stringify(o[xr]))}}class ay{constructor(t){this._injectedScript=t,!this._injectedScript.window.playwright&&(this._injectedScript.window.playwright={$:(n,r)=>this._querySelector(n,!!r),$$:n=>this._querySelectorAll(n),inspect:n=>this._inspect(n),selector:n=>this._selector(n),generateLocator:(n,r)=>this._generateLocator(n,r),resume:()=>this._resume(),...new On(t,"")},delete this._injectedScript.window.playwright.filter,delete this._injectedScript.window.playwright.first,delete this._injectedScript.window.playwright.last,delete this._injectedScript.window.playwright.nth,delete this._injectedScript.window.playwright.and,delete this._injectedScript.window.playwright.or)}_querySelector(t,n){if(typeof t!="string")throw new Error("Usage: playwright.query('Playwright >> selector').");const r=this._injectedScript.parseSelector(t);return this._injectedScript.querySelector(r,this._injectedScript.document,n)}_querySelectorAll(t){if(typeof t!="string")throw new Error("Usage: playwright.$$('Playwright >> selector').");const n=this._injectedScript.parseSelector(t);return this._injectedScript.querySelectorAll(n,this._injectedScript.document)}_inspect(t){if(typeof t!="string")throw new Error("Usage: playwright.inspect('Playwright >> selector').");this._injectedScript.window.inspect(this._querySelector(t,!1))}_selector(t){if(!(t instanceof Element))throw new Error("Usage: playwright.selector(element).");return this._injectedScript.generateSelectorSimple(t)}_generateLocator(t,n){if(!(t instanceof Element))throw new Error("Usage: playwright.locator(element).");const r=this._injectedScript.generateSelectorSimple(t);return Xt(n||"javascript",r)}_resume(){this._injectedScript.window.__pw_resume().catch(()=>{})}}function uy(e,t){e=e.replace(/AriaRole\s*\.\s*([\w]+)/g,(s,o)=>o.toLowerCase()).replace(/(get_by_role|getByRole)\s*\(\s*(?:["'`])([^'"`]+)['"`]/g,(s,o,l)=>`${o}(${l.toLowerCase()}`);const n=[];let r="";for(let s=0;s<e.length;++s){const o=e[s];if(o!=='"'&&o!=="'"&&o!=="`"&&o!=="/"){r+=o;continue}const l=e[s-1]==="r"||e[s]==="/";++s;let a="";for(;s<e.length;){if(e[s]==="\\"){l?(e[s+1]!==o&&(a+=e[s]),++s,a+=e[s]):(++s,e[s]==="n"?a+=`
`:e[s]==="r"?a+="\r":e[s]==="t"?a+="	":a+=e[s]),++s;continue}if(e[s]!==o){a+=e[s++];continue}break}n.push({quote:o,text:a}),r+=(o==="/"?"r":"")+"$"+n.length}r=r.toLowerCase().replace(/get_by_alt_text/g,"getbyalttext").replace(/get_by_test_id/g,"getbytestid").replace(/get_by_([\w]+)/g,"getby$1").replace(/has_not_text/g,"hasnottext").replace(/has_text/g,"hastext").replace(/has_not/g,"hasnot").replace(/frame_locator/g,"framelocator").replace(/[{}\s]/g,"").replace(/new\(\)/g,"").replace(/new[\w]+\.[\w]+options\(\)/g,"").replace(/\.set/g,",set").replace(/\.or_\(/g,"or(").replace(/\.and_\(/g,"and(").replace(/:/g,"=").replace(/,re\.ignorecase/g,"i").replace(/,pattern.case_insensitive/g,"i").replace(/,regexoptions.ignorecase/g,"i").replace(/re.compile\(([^)]+)\)/g,"$1").replace(/pattern.compile\(([^)]+)\)/g,"r$1").replace(/newregex\(([^)]+)\)/g,"r$1").replace(/string=/g,"=").replace(/regex=/g,"=").replace(/,,/g,",");const i=n.map(s=>s.quote).filter(s=>"'\"`".includes(s))[0];return{selector:_p(r,n,t),preferredQuote:i}}function bc(e){return[...e.matchAll(/\$\d+/g)].length}function Lc(e,t){return e.replace(/\$(\d+)/g,(n,r)=>`$${r-t}`)}function _p(e,t,n){for(;;){const i=e.match(/filter\(,?(has=|hasnot=|sethas\(|sethasnot\()/);if(!i)break;const s=i.index+i[0].length;let o=0,l=s;for(;l<e.length&&(e[l]==="("?o++:e[l]===")"&&o--,!(o<0));l++);let a=e.substring(0,s),u=0;["sethas(","sethasnot("].includes(i[1])&&(u=1,a=a.replace(/sethas\($/,"has=").replace(/sethasnot\($/,"hasnot="));const c=bc(e.substring(0,s)),f=Lc(e.substring(s,l),c),p=bc(f),v=t.slice(c,c+p),y=JSON.stringify(_p(f,v,n));e=a.replace(/=$/,"2=")+`$${c+1}`+Lc(e.substring(l+u),p-1);const w=t.slice(0,c),x=t.slice(c+p);t=w.concat([{quote:'"',text:y}]).concat(x)}e=e.replace(/\,set([\w]+)\(([^)]+)\)/g,(i,s,o)=>","+s.toLowerCase()+"="+o.toLowerCase()).replace(/framelocator\(([^)]+)\)/g,"$1.internal:control=enter-frame").replace(/locator\(([^)]+),hastext=([^),]+)\)/g,"locator($1).internal:has-text=$2").replace(/locator\(([^)]+),hasnottext=([^),]+)\)/g,"locator($1).internal:has-not-text=$2").replace(/locator\(([^)]+),hastext=([^),]+)\)/g,"locator($1).internal:has-text=$2").replace(/locator\(([^)]+)\)/g,"$1").replace(/getbyrole\(([^)]+)\)/g,"internal:role=$1").replace(/getbytext\(([^)]+)\)/g,"internal:text=$1").replace(/getbylabel\(([^)]+)\)/g,"internal:label=$1").replace(/getbytestid\(([^)]+)\)/g,`internal:testid=[${n}=$1]`).replace(/getby(placeholder|alt|title)(?:text)?\(([^)]+)\)/g,"internal:attr=[$1=$2]").replace(/first(\(\))?/g,"nth=0").replace(/last(\(\))?/g,"nth=-1").replace(/nth\(([^)]+)\)/g,"nth=$1").replace(/filter\(,?hastext=([^)]+)\)/g,"internal:has-text=$1").replace(/filter\(,?hasnottext=([^)]+)\)/g,"internal:has-not-text=$1").replace(/filter\(,?has2=([^)]+)\)/g,"internal:has=$1").replace(/filter\(,?hasnot2=([^)]+)\)/g,"internal:has-not=$1").replace(/,exact=false/g,"").replace(/,exact=true/g,"s").replace(/\,/g,"][");const r=e.split(".");for(let i=0;i<r.length-1;i++)if(r[i]==="internal:control=enter-frame"&&r[i+1].startsWith("nth=")){const[s]=r.splice(i,1);r.splice(i+1,0,s)}return r.map(i=>!i.startsWith("internal:")||i==="internal:control"?i.replace(/\$(\d+)/g,(s,o)=>t[+o-1].text):(i=i.includes("[")?i.replace(/\]/,"")+"]":i,i=i.replace(/(?:r)\$(\d+)(i)?/g,(s,o,l)=>{const a=t[+o-1];return i.startsWith("internal:attr")||i.startsWith("internal:testid")||i.startsWith("internal:role")?_e(new RegExp(a.text),!1)+(l||""):st(new RegExp(a.text,l),!1)}).replace(/\$(\d+)(i|s)?/g,(s,o,l)=>{const a=t[+o-1];return i.startsWith("internal:has=")||i.startsWith("internal:has-not=")?a.text:i.startsWith("internal:testid")?_e(a.text,!0):i.startsWith("internal:attr")||i.startsWith("internal:role")?_e(a.text,l==="s"):st(a.text,l==="s")}),i)).join(" >> ")}function cy(e,t,n){try{return qs(t),t}catch{}try{const{selector:r,preferredQuote:i}=uy(t,n),s=xh(e,r,void 0,void 0,i),o=Ac(e,t);if(s.some(l=>Ac(e,l)===o))return r}catch{}return""}function Ac(e,t){return t=t.replace(/\s/g,""),e==="javascript"&&(t=t.replace(/\\?["`]/g,"'")),t}const dy=({url:e})=>d.jsxs("div",{className:"browser-frame-header",children:[d.jsxs("div",{style:{whiteSpace:"nowrap"},children:[d.jsx("span",{className:"browser-frame-dot",style:{backgroundColor:"rgb(242, 95, 88)"}}),d.jsx("span",{className:"browser-frame-dot",style:{backgroundColor:"rgb(251, 190, 60)"}}),d.jsx("span",{className:"browser-frame-dot",style:{backgroundColor:"rgb(88, 203, 66)"}})]}),d.jsx("div",{className:"browser-frame-address-bar",title:e||"about:blank",children:e||"about:blank"}),d.jsx("div",{style:{marginLeft:"auto"},children:d.jsxs("div",{children:[d.jsx("span",{className:"browser-frame-menu-bar"}),d.jsx("span",{className:"browser-frame-menu-bar"}),d.jsx("span",{className:"browser-frame-menu-bar"})]})})]}),fy=({action:e,sdkLanguage:t,testIdAttributeName:n,isInspecting:r,setIsInspecting:i,highlightedLocator:s,setHighlightedLocator:o})=>{const[l,a]=pn(),[u,c]=L.useState("action"),{snapshots:f}=L.useMemo(()=>{if(!e)return{snapshots:{}};let E=e.beforeSnapshot?{action:e,snapshotName:e.beforeSnapshot}:void 0,S=e;for(;!E&&S;)S=f0(S),E=S!=null&&S.afterSnapshot?{action:S,snapshotName:S==null?void 0:S.afterSnapshot}:void 0;const A=e.afterSnapshot?{action:e,snapshotName:e.afterSnapshot}:E,P=e.inputSnapshot?{action:e,snapshotName:e.inputSnapshot}:A;return P&&(P.point=e.point),{snapshots:{action:P,before:E,after:A}}},[e]),{snapshotInfoUrl:p,snapshotUrl:v,popoutUrl:y}=L.useMemo(()=>{const E=f[u];if(!E)return{snapshotUrl:py};const S=new URLSearchParams;S.set("trace",Nl(E.action).traceUrl),S.set("name",E.snapshotName),E.point&&(S.set("pointX",String(E.point.x)),S.set("pointY",String(E.point.y)));const A=new URL(`snapshot/${E.action.pageId}?${S.toString()}`,window.location.href).toString(),P=new URL(`snapshotInfo/${E.action.pageId}?${S.toString()}`,window.location.href).toString(),T=new URLSearchParams;T.set("r",A),T.set("trace",Nl(E.action).traceUrl),E.point&&(T.set("pointX",String(E.point.x)),T.set("pointY",String(E.point.y)));const I=new URL(`snapshot.html?${T.toString()}`,window.location.href).toString();return{snapshots:f,snapshotInfoUrl:P,snapshotUrl:A,popoutUrl:I}},[f,u]),w=L.useRef(null),x=L.useRef(null),[h,m]=L.useState({viewport:jc,url:""}),g=L.useRef({iteration:0,visibleIframe:0});L.useEffect(()=>{(async()=>{const E=g.current.iteration+1,S=1-g.current.visibleIframe;g.current.iteration=E;const A={url:"",viewport:jc};if(p){const I=await(await fetch(p)).json();I.error||(A.url=I.url,A.viewport=I.viewport)}if(g.current.iteration!==E)return;const P=[w,x][S].current;if(P){let T=()=>{};const I=new Promise(z=>T=z);try{P.addEventListener("load",T),P.addEventListener("error",T),P.contentWindow?P.contentWindow.location.replace(v):P.src=v,await I}catch{}finally{P.removeEventListener("load",T),P.removeEventListener("error",T)}}g.current.iteration===E&&(g.current.visibleIframe=S,m(A))})()},[v,p]);const N={width:h.viewport.width,height:h.viewport.height+40},k=Math.min(l.width/N.width,l.height/N.height,1),C={x:(l.width-N.width)/2,y:(l.height-N.height)/2};return d.jsxs("div",{className:"snapshot-tab",tabIndex:0,onKeyDown:E=>{E.key==="Escape"&&r&&i(!1)},children:[d.jsx(Mc,{isInspecting:r,sdkLanguage:t,testIdAttributeName:n,highlightedLocator:s,setHighlightedLocator:o,iframe:w.current,iteration:g.current.iteration}),d.jsx(Mc,{isInspecting:r,sdkLanguage:t,testIdAttributeName:n,highlightedLocator:s,setHighlightedLocator:o,iframe:x.current,iteration:g.current.iteration}),d.jsxs(_h,{children:[["action","before","after"].map(E=>d.jsx(Eh,{id:E,title:hy(E),selected:u===E,onSelect:()=>c(E)})),d.jsx("div",{style:{flex:"auto"}}),d.jsx(Bn,{icon:"link-external",title:"Open snapshot in a new tab",disabled:!y,onClick:()=>{const E=window.open(y||"","_blank");E==null||E.addEventListener("DOMContentLoaded",()=>{const S=new vp(E,!1,t,n,1,"chromium",[]);new ay(S)})}})]}),d.jsx("div",{ref:a,className:"snapshot-wrapper",children:d.jsxs("div",{className:"snapshot-container",style:{width:N.width+"px",height:N.height+"px",transform:`translate(${C.x}px, ${C.y}px) scale(${k})`},children:[d.jsx(dy,{url:h.url}),d.jsxs("div",{className:"snapshot-switcher",children:[d.jsx("iframe",{ref:w,name:"snapshot",title:"DOM Snapshot",className:g.current.visibleIframe===0?"snapshot-visible":""}),d.jsx("iframe",{ref:x,name:"snapshot",title:"DOM Snapshot",className:g.current.visibleIframe===1?"snapshot-visible":""})]})]})})]})};function hy(e){return e==="before"?"Before":e==="after"?"After":e==="action"?"Action":e}const Mc=({iframe:e,isInspecting:t,sdkLanguage:n,testIdAttributeName:r,highlightedLocator:i,setHighlightedLocator:s,iteration:o})=>(L.useEffect(()=>{const l=[],a=new URLSearchParams(window.location.search).get("isUnderTest")==="true";try{Ep(l,n,r,a,"",e==null?void 0:e.contentWindow)}catch{}for(const{recorder:u,frameSelector:c}of l){const f=cy(n,i,r);u.setUIState({mode:t?"inspecting":"none",actionSelector:f.startsWith(c)?f.substring(c.length).trim():void 0,language:n,testIdAttributeName:r,overlay:{offsetX:0}},{async setSelector(p){s(Xt(n,c+p))},highlightUpdated(){for(const p of l)p.recorder!==u&&p.recorder.clearHighlight()}})}},[e,t,i,s,n,r,o]),d.jsx(d.Fragment,{}));function Ep(e,t,n,r,i,s){if(!s)return;const o=s;if(!o._recorder){const l=new vp(s,r,t,n,1,"chromium",[]),a=new Gv(l);o._injectedScript=l,o._recorder={recorder:a,frameSelector:i}}e.push(o._recorder);for(let l=0;l<s.frames.length;++l){const a=s.frames[l],u=a.frameElement?o._injectedScript.generateSelectorSimple(a.frameElement,{omitInternalEngines:!0,testIdAttributeName:n})+" >> internal:control=enter-frame >> ":"";Ep(e,t,n,r,i+u,a)}}const jc={width:1280,height:720},py='data:text/html,<body style="background: #ddd"></body>',my=ai,gy=({stack:e,setSelectedFrame:t,selectedFrame:n})=>{const r=e||[];return d.jsx(my,{name:"stack-trace",items:r,selectedItem:r[n],render:i=>{const s=i.file[1]===":"?"\\":"/";return d.jsxs(d.Fragment,{children:[d.jsx("span",{className:"stack-trace-frame-function",children:i.function||"(anonymous)"}),d.jsx("span",{className:"stack-trace-frame-location",children:i.file.split(s).pop()}),d.jsx("span",{className:"stack-trace-frame-line",children:":"+i.line})]})},onSelected:i=>t(r.indexOf(i))})},vy=({stack:e,sources:t,hideStackFrames:n,rootDir:r,fallbackLocation:i})=>{const[s,o]=L.useState(),[l,a]=L.useState(0);L.useEffect(()=>{s!==e&&(o(e),a(0))},[e,s,o,a]);const{source:u,highlight:c,targetLine:f,fileName:p}=tm(async()=>{var _,N;const v=e==null?void 0:e[l],y=!(v!=null&&v.file);if(y&&!i)return{source:{file:"",errors:[],content:void 0},targetLine:0,highlight:[]};const w=y?i.file:v.file;let x=t.get(w);x||(x={errors:((_=i==null?void 0:i.source)==null?void 0:_.errors)||[],content:void 0},t.set(w,x));const h=y?(i==null?void 0:i.line)||((N=x.errors[0])==null?void 0:N.line)||0:v.line,m=r&&w.startsWith(r)?w.substring(r.length+1):w,g=x.errors.map(k=>({type:"error",line:k.line,message:k.message}));if(g.push({line:h,type:"running"}),x.content===void 0||y){const k=await yy(w);try{let C=await fetch(`sha1/src@${k}.txt`);C.status===404&&(C=await fetch(`file?path=${encodeURIComponent(w)}`)),C.status>=400?x.content=`<Unable to read "${w}">`:x.content=await C.text()}catch{x.content=`<Unable to read "${w}">`}}return{source:x,highlight:g,targetLine:h,fileName:m}},[e,l,r,i],{source:{errors:[],content:"Loading…"},highlight:[]});return d.jsxs(Es,{sidebarSize:200,orientation:"horizontal",sidebarHidden:n,children:[d.jsxs("div",{className:"vbox","data-testid":"source-code",children:[p&&d.jsx("div",{className:"source-tab-file-name",children:p}),d.jsx(Xs,{text:u.content||"",language:"javascript",highlight:c,revealLine:f,readOnly:!0,lineNumbers:!0})]}),d.jsx(gy,{stack:e,selectedFrame:l,setSelectedFrame:a})]})};async function yy(e){const t=new TextEncoder().encode(e),n=await crypto.subtle.digest("SHA-1",t),r=[],i=new DataView(n);for(let s=0;s<i.byteLength;s+=1){const o=i.getUint8(s).toString(16).padStart(2,"0");r.push(o)}return r.join("")}const kp={width:200,height:45},En=2.5,wy=kp.height+En*2,xy=({model:e,boundaries:t,previewPoint:n})=>{var c,f;const[r,i]=pn(),s=L.useRef(null);let o=0;if(s.current&&n){const p=s.current.getBoundingClientRect();o=(n.clientY-p.top+s.current.scrollTop)/wy|0}const l=(f=(c=e==null?void 0:e.pages)==null?void 0:c[o])==null?void 0:f.screencastFrames;let a,u;if(n!==void 0&&l){const p=t.minimum+(t.maximum-t.minimum)*n.x/r.width;a=l[Bc(l,p,Tp)-1];const v={width:Math.min(800,window.innerWidth/2|0),height:Math.min(800,window.innerHeight/2|0)};u=a?Cp({width:a.width,height:a.height},v):void 0}return d.jsxs("div",{className:"film-strip",ref:i,children:[d.jsx("div",{className:"film-strip-lanes",ref:s,children:e==null?void 0:e.pages.map((p,v)=>d.jsx(Sy,{boundaries:t,page:p,width:r.width},v))}),(n==null?void 0:n.x)!==void 0&&d.jsxs("div",{className:"film-strip-hover",style:{top:r.bottom+5,left:Math.min(n.x,r.width-(u?u.width:0)-10)},children:[n.action&&d.jsx("div",{className:"film-strip-hover-title",children:$a(n.action,n)}),a&&u&&d.jsx("div",{style:{width:u.width,height:u.height},children:d.jsx("img",{src:`sha1/${a.sha1}`,width:u.width,height:u.height})})]})]})},Sy=({boundaries:e,page:t,width:n})=>{const r={width:0,height:0},i=t.screencastFrames;for(const w of i)r.width=Math.max(r.width,w.width),r.height=Math.max(r.height,w.height);const s=Cp(r,kp),o=i[0].timestamp,l=i[i.length-1].timestamp,a=e.maximum-e.minimum,u=(o-e.minimum)/a*n,c=(e.maximum-l)/a*n,p=(l-o)/a*n/(s.width+2*En)|0,v=(l-o)/p,y=[];for(let w=0;o&&v&&w<p;++w){const x=o+v*w,h=Bc(i,x,Tp)-1;y.push(d.jsx("div",{className:"film-strip-frame",style:{width:s.width,height:s.height,backgroundImage:`url(sha1/${i[h].sha1})`,backgroundSize:`${s.width}px ${s.height}px`,margin:En,marginRight:En}},w))}return y.push(d.jsx("div",{className:"film-strip-frame",style:{width:s.width,height:s.height,backgroundImage:`url(sha1/${i[i.length-1].sha1})`,backgroundSize:`${s.width}px ${s.height}px`,margin:En,marginRight:En}},y.length)),d.jsx("div",{className:"film-strip-lane",style:{marginLeft:u+"px",marginRight:c+"px"},children:y})};function Tp(e,t){return e-t.timestamp}function Cp(e,t){const n=Math.max(e.width/t.width,e.height/t.height);return{width:e.width/n|0,height:e.height/n|0}}const _y=({model:e,boundaries:t,onSelected:n,highlightedAction:r,highlightedEntry:i,selectedTime:s,setSelectedTime:o,sdkLanguage:l})=>{const[a,u]=pn(),[c,f]=L.useState(),[p,v]=L.useState(),{offsets:y,curtainLeft:w,curtainRight:x}=L.useMemo(()=>{let E=s||t;if(c&&c.startX!==c.endX){const T=at(a.width,t,c.startX),I=at(a.width,t,c.endX);E={minimum:Math.min(T,I),maximum:Math.max(T,I)}}const S=ut(a.width,t,E.minimum),P=ut(a.width,t,t.maximum)-ut(a.width,t,E.maximum);return{offsets:Ey(a.width,t),curtainLeft:S,curtainRight:P}},[s,t,c,a]),h=L.useMemo(()=>{const E=[];for(const S of(e==null?void 0:e.actions)||[])S.class!=="Test"&&E.push({action:S,leftTime:S.startTime,rightTime:S.endTime||t.maximum,leftPosition:ut(a.width,t,S.startTime),rightPosition:ut(a.width,t,S.endTime||t.maximum),active:!1,error:!!S.error});for(const S of(e==null?void 0:e.resources)||[]){const A=S._monotonicTime,P=S._monotonicTime+S.time;E.push({resource:S,leftTime:A,rightTime:P,leftPosition:ut(a.width,t,A),rightPosition:ut(a.width,t,P),active:!1,error:!1})}return E},[e,t,a]);L.useMemo(()=>{for(const E of h)E.active=!!r&&E.action===r||!!i&&E.resource===i},[h,r,i]);const m=L.useCallback(E=>{if(v(void 0),!u.current)return;const S=E.clientX-u.current.getBoundingClientRect().left,A=at(a.width,t,S),P=s?ut(a.width,t,s.minimum):0,T=s?ut(a.width,t,s.maximum):0;s&&Math.abs(S-P)<10?f({startX:T,endX:S,type:"resize"}):s&&Math.abs(S-T)<10?f({startX:P,endX:S,type:"resize"}):s&&A>s.minimum&&A<s.maximum&&E.clientY-u.current.getBoundingClientRect().top<20?f({startX:P,endX:T,pivot:S,type:"move"}):f({startX:S,endX:S,type:"resize"})},[t,a,u,s]),g=L.useCallback(E=>{if(!u.current)return;const S=E.clientX-u.current.getBoundingClientRect().left,A=at(a.width,t,S),P=e==null?void 0:e.actions.findLast(ae=>ae.startTime<=A);if(!E.buttons){f(void 0);return}if(P&&n(P),!c)return;let T=c;if(c.type==="resize")T={...c,endX:S};else{const ae=S-c.pivot;let Ne=c.startX+ae,ue=c.endX+ae;Ne<0&&(Ne=0,ue=Ne+(c.endX-c.startX)),ue>a.width&&(ue=a.width,Ne=ue-(c.endX-c.startX)),T={...c,startX:Ne,endX:ue,pivot:S}}f(T);const I=at(a.width,t,T.startX),z=at(a.width,t,T.endX);I!==z&&o({minimum:Math.min(I,z),maximum:Math.max(I,z)})},[t,c,a,e,n,u,o]),_=L.useCallback(()=>{if(v(void 0),!!c){if(c.startX!==c.endX){const E=at(a.width,t,c.startX),S=at(a.width,t,c.endX);o({minimum:Math.min(E,S),maximum:Math.max(E,S)})}else{const E=at(a.width,t,c.startX),S=e==null?void 0:e.actions.findLast(A=>A.startTime<=E);S&&n(S),o(void 0)}f(void 0)}},[t,c,a,e,o,n]),N=L.useCallback(E=>{if(!u.current)return;const S=E.clientX-u.current.getBoundingClientRect().left,A=at(a.width,t,S),P=e==null?void 0:e.actions.findLast(T=>T.startTime<=A);v({x:S,clientY:E.clientY,action:P,sdkLanguage:l})},[t,a,e,u,l]),k=L.useCallback(()=>{v(void 0)},[]),C=L.useCallback(()=>{o(void 0)},[o]);return d.jsxs("div",{style:{flex:"none",borderBottom:"1px solid var(--vscode-panel-border)"},children:[!!c&&d.jsx(Th,{cursor:(c==null?void 0:c.type)==="resize"?"ew-resize":"grab",onPaneMouseUp:_,onPaneMouseMove:g,onPaneDoubleClick:C}),d.jsxs("div",{ref:u,className:"timeline-view",onMouseDown:m,onMouseMove:N,onMouseLeave:k,children:[d.jsx("div",{className:"timeline-grid",children:y.map((E,S)=>d.jsx("div",{className:"timeline-divider",style:{left:E.position+"px"},children:d.jsx("div",{className:"timeline-time",children:Qe(E.time-t.minimum)})},S))}),d.jsx("div",{style:{height:8}}),d.jsx(xy,{model:e,boundaries:t,previewPoint:p}),d.jsx("div",{className:"timeline-bars",children:h.map((E,S)=>d.jsx("div",{className:"timeline-bar"+(E.action?" action":"")+(E.resource?" network":"")+(E.active?" active":"")+(E.error?" error":""),style:{left:E.leftPosition,width:Math.max(1,E.rightPosition-E.leftPosition),top:ky(E),bottom:0}},S))}),d.jsx("div",{className:"timeline-marker",style:{display:p!==void 0?"block":"none",left:((p==null?void 0:p.x)||0)+"px"}}),s&&d.jsxs("div",{className:"timeline-window",children:[d.jsx("div",{className:"timeline-window-curtain left",style:{width:w}}),d.jsx("div",{className:"timeline-window-resizer",style:{left:-5}}),d.jsx("div",{className:"timeline-window-center",children:d.jsx("div",{className:"timeline-window-drag"})}),d.jsx("div",{className:"timeline-window-resizer",style:{left:5}}),d.jsx("div",{className:"timeline-window-curtain right",style:{width:x}})]})]})]})};function Ey(e,t){let r=e/64;const i=t.maximum-t.minimum,s=e/i;let o=i/r;const l=Math.ceil(Math.log(o)/Math.LN10);o=Math.pow(10,l),o*s>=5*64&&(o=o/5),o*s>=2*64&&(o=o/2);const a=t.minimum;let u=t.maximum;u+=64/s,r=Math.ceil((u-a)/o),o||(r=0);const c=[];for(let f=0;f<r;++f){const p=a+o*f;c.push({position:ut(e,t,p),time:p})}return c}function ut(e,t,n){return(n-t.minimum)/(t.maximum-t.minimum)*e}function at(e,t,n){return n/e*(t.maximum-t.minimum)+t.minimum}function ky(e){return e.resource?25:20}const Ty=({model:e})=>{var t,n;return e?d.jsxs("div",{className:"metadata-view vbox",children:[d.jsx("div",{className:"call-section",style:{paddingTop:2},children:"Time"}),!!e.wallTime&&d.jsxs("div",{className:"call-line",children:["start time:",d.jsx("span",{className:"call-value datetime",title:new Date(e.wallTime).toLocaleString(),children:new Date(e.wallTime).toLocaleString()})]}),d.jsxs("div",{className:"call-line",children:["duration:",d.jsx("span",{className:"call-value number",title:Qe(e.endTime-e.startTime),children:Qe(e.endTime-e.startTime)})]}),d.jsx("div",{className:"call-section",children:"Browser"}),d.jsxs("div",{className:"call-line",children:["engine:",d.jsx("span",{className:"call-value string",title:e.browserName,children:e.browserName})]}),e.channel&&d.jsxs("div",{className:"call-line",children:["channel:",d.jsx("span",{className:"call-value string",title:e.channel,children:e.channel})]}),e.platform&&d.jsxs("div",{className:"call-line",children:["platform:",d.jsx("span",{className:"call-value string",title:e.platform,children:e.platform})]}),e.options.userAgent&&d.jsxs("div",{className:"call-line",children:["user agent:",d.jsx("span",{className:"call-value datetime",title:e.options.userAgent,children:e.options.userAgent})]}),d.jsx("div",{className:"call-section",children:"Viewport"}),e.options.viewport&&d.jsxs("div",{className:"call-line",children:["width:",d.jsx("span",{className:"call-value number",title:String(!!((t=e.options.viewport)!=null&&t.width)),children:e.options.viewport.width})]}),e.options.viewport&&d.jsxs("div",{className:"call-line",children:["height:",d.jsx("span",{className:"call-value number",title:String(!!((n=e.options.viewport)!=null&&n.height)),children:e.options.viewport.height})]}),d.jsxs("div",{className:"call-line",children:["is mobile:",d.jsx("span",{className:"call-value boolean",title:String(!!e.options.isMobile),children:String(!!e.options.isMobile)})]}),e.options.deviceScaleFactor&&d.jsxs("div",{className:"call-line",children:["device scale:",d.jsx("span",{className:"call-value number",title:String(e.options.deviceScaleFactor),children:String(e.options.deviceScaleFactor)})]}),d.jsx("div",{className:"call-section",children:"Counts"}),d.jsxs("div",{className:"call-line",children:["pages:",d.jsx("span",{className:"call-value number",children:e.pages.length})]}),d.jsxs("div",{className:"call-line",children:["actions:",d.jsx("span",{className:"call-value number",children:e.actions.length})]}),d.jsxs("div",{className:"call-line",children:["events:",d.jsx("span",{className:"call-value number",children:e.events.length})]})]}):d.jsx(d.Fragment,{})};async function Po(e){const t=new Image;return e&&(t.src=e,await new Promise((n,r)=>{t.onload=n,t.onerror=n})),t}const zl={backgroundImage:`linear-gradient(45deg, #80808020 25%, transparent 25%),
                    linear-gradient(-45deg, #80808020 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #80808020 75%),
                    linear-gradient(-45deg, transparent 75%, #80808020 75%)`,backgroundSize:"20px 20px",backgroundPosition:"0 0, 0 10px, 10px -10px, -10px 0px",boxShadow:`rgb(0 0 0 / 10%) 0px 1.8px 1.9px,
              rgb(0 0 0 / 15%) 0px 6.1px 6.3px,
              rgb(0 0 0 / 10%) 0px -2px 4px,
              rgb(0 0 0 / 15%) 0px -6.1px 12px,
              rgb(0 0 0 / 25%) 0px 6px 12px`},Cy=({diff:e})=>{const[t,n]=L.useState(e.diff?"diff":"actual"),[r,i]=L.useState(!1),[s,o]=L.useState(null),[l,a]=L.useState(null),[u,c]=L.useState(null),[f,p]=pn();L.useEffect(()=>{(async()=>{var N,k,C;o(await Po((N=e.expected)==null?void 0:N.attachment.path)),a(await Po((k=e.actual)==null?void 0:k.attachment.path)),c(await Po((C=e.diff)==null?void 0:C.attachment.path))})()},[e]);const v=s&&l&&u,y=v?Math.max(s.naturalWidth,l.naturalWidth,200):500,w=v?Math.max(s.naturalHeight,l.naturalHeight,200):500,x=Math.min(1,(f.width-30)/y),h=Math.min(1,(f.width-50)/y/2),m=y*x,g=w*x,_={flex:"none",margin:"0 10px",cursor:"pointer",userSelect:"none"};return d.jsx("div",{"data-testid":"test-result-image-mismatch",style:{display:"flex",flexDirection:"column",alignItems:"center",flex:"auto"},ref:p,children:v&&d.jsxs(d.Fragment,{children:[d.jsxs("div",{"data-testid":"test-result-image-mismatch-tabs",style:{display:"flex",margin:"10px 0 20px"},children:[e.diff&&d.jsx("div",{style:{..._,fontWeight:t==="diff"?600:"initial"},onClick:()=>n("diff"),children:"Diff"}),d.jsx("div",{style:{..._,fontWeight:t==="actual"?600:"initial"},onClick:()=>n("actual"),children:"Actual"}),d.jsx("div",{style:{..._,fontWeight:t==="expected"?600:"initial"},onClick:()=>n("expected"),children:"Expected"}),d.jsx("div",{style:{..._,fontWeight:t==="sxs"?600:"initial"},onClick:()=>n("sxs"),children:"Side by side"}),d.jsx("div",{style:{..._,fontWeight:t==="slider"?600:"initial"},onClick:()=>n("slider"),children:"Slider"})]}),d.jsxs("div",{style:{display:"flex",justifyContent:"center",flex:"auto",minHeight:g+60},children:[e.diff&&t==="diff"&&d.jsx(vt,{image:u,alt:"Diff",canvasWidth:m,canvasHeight:g,scale:x}),e.diff&&t==="actual"&&d.jsx(vt,{image:l,alt:"Actual",canvasWidth:m,canvasHeight:g,scale:x}),e.diff&&t==="expected"&&d.jsx(vt,{image:s,alt:"Expected",canvasWidth:m,canvasHeight:g,scale:x}),e.diff&&t==="slider"&&d.jsx(Ny,{expectedImage:s,actualImage:l,canvasWidth:m,canvasHeight:g,scale:x}),e.diff&&t==="sxs"&&d.jsxs("div",{style:{display:"flex"},children:[d.jsx(vt,{image:s,title:"Expected",canvasWidth:h*y,canvasHeight:h*w,scale:h}),d.jsx(vt,{image:r?u:l,title:r?"Diff":"Actual",onClick:()=>i(!r),canvasWidth:h*y,canvasHeight:h*w,scale:h})]}),!e.diff&&t==="actual"&&d.jsx(vt,{image:l,title:"Actual",canvasWidth:m,canvasHeight:g,scale:x}),!e.diff&&t==="expected"&&d.jsx(vt,{image:s,title:"Expected",canvasWidth:m,canvasHeight:g,scale:x}),!e.diff&&t==="sxs"&&d.jsxs("div",{style:{display:"flex"},children:[d.jsx(vt,{image:s,title:"Expected",canvasWidth:h*y,canvasHeight:h*w,scale:h}),d.jsx(vt,{image:l,title:"Actual",canvasWidth:h*y,canvasHeight:h*w,scale:h})]})]}),d.jsxs("div",{style:{alignSelf:"start",lineHeight:"18px"},children:[d.jsx("div",{children:e.diff&&d.jsx("a",{target:"_blank",href:e.diff.attachment.path,children:e.diff.attachment.name})}),d.jsx("div",{children:d.jsx("a",{target:"_blank",href:e.actual.attachment.path,children:e.actual.attachment.name})}),d.jsx("div",{children:d.jsx("a",{target:"_blank",href:e.expected.attachment.path,children:e.expected.attachment.name})})]})]})})},Ny=({expectedImage:e,actualImage:t,canvasWidth:n,canvasHeight:r,scale:i})=>{const s={position:"absolute",top:0,left:0},[o,l]=L.useState(n/2),a=e.naturalWidth===t.naturalWidth&&e.naturalHeight===t.naturalHeight;return d.jsxs("div",{style:{flex:"none",display:"flex",alignItems:"center",flexDirection:"column",userSelect:"none"},children:[d.jsxs("div",{style:{margin:5},children:[!a&&d.jsx("span",{style:{flex:"none",margin:"0 5px"},children:"Expected "}),d.jsx("span",{children:e.naturalWidth}),d.jsx("span",{style:{flex:"none",margin:"0 5px"},children:"x"}),d.jsx("span",{children:e.naturalHeight}),!a&&d.jsx("span",{style:{flex:"none",margin:"0 5px 0 15px"},children:"Actual "}),!a&&d.jsx("span",{children:t.naturalWidth}),!a&&d.jsx("span",{style:{flex:"none",margin:"0 5px"},children:"x"}),!a&&d.jsx("span",{children:t.naturalHeight})]}),d.jsxs("div",{style:{position:"relative",width:n,height:r,margin:15,...zl},children:[d.jsx(Ch,{orientation:"horizontal",offsets:[o],setOffsets:u=>l(u[0]),resizerColor:"#57606a80",resizerWidth:6}),d.jsx("img",{alt:"Expected",style:{width:e.naturalWidth*i,height:e.naturalHeight*i},draggable:"false",src:e.src}),d.jsx("div",{style:{...s,bottom:0,overflow:"hidden",width:o,...zl},children:d.jsx("img",{alt:"Actual",style:{width:t.naturalWidth*i,height:t.naturalHeight*i},draggable:"false",src:t.src})})]})]})},vt=({image:e,title:t,alt:n,canvasWidth:r,canvasHeight:i,scale:s,onClick:o})=>d.jsxs("div",{style:{flex:"none",display:"flex",alignItems:"center",flexDirection:"column"},children:[d.jsxs("div",{style:{margin:5},children:[t&&d.jsx("span",{style:{flex:"none",margin:"0 5px"},children:t}),d.jsx("span",{children:e.naturalWidth}),d.jsx("span",{style:{flex:"none",margin:"0 5px"},children:"x"}),d.jsx("span",{children:e.naturalHeight})]}),d.jsx("div",{style:{display:"flex",flex:"none",width:r,height:i,margin:15,...zl},children:d.jsx("img",{width:e.naturalWidth*s,height:e.naturalHeight*s,alt:t||n,style:{cursor:o?"pointer":"initial"},draggable:"false",src:e.src,onClick:o})})]}),by=({model:e})=>{const{diffMap:t,screenshots:n,attachments:r}=L.useMemo(()=>{const i=new Set,s=new Set;for(const l of(e==null?void 0:e.actions)||[]){const a=l.context.traceUrl;for(const u of l.attachments||[])i.add({...u,traceUrl:a})}const o=new Map;for(const l of i){if(!l.path&&!l.sha1)continue;const a=l.name.match(/^(.*)-(expected|actual|diff)\.png$/);if(a){const u=a[1],c=a[2],f=o.get(u)||{expected:void 0,actual:void 0,diff:void 0};f[c]=l,o.set(u,f)}l.contentType.startsWith("image/")&&(s.add(l),i.delete(l))}return{diffMap:o,attachments:i,screenshots:s}},[e]);return!t.size&&!n.size&&!r.size?d.jsx(rr,{text:"No attachments"}):d.jsxs("div",{className:"attachments-tab",children:[[...t.values()].map(({expected:i,actual:s,diff:o})=>d.jsxs(d.Fragment,{children:[i&&s&&d.jsx("div",{className:"attachments-section",children:"Image diff"}),i&&s&&d.jsx(Cy,{diff:{name:"Image diff",expected:{attachment:{...i,path:Sr(i)},title:"Expected"},actual:{attachment:{...s,path:Sr(s)}},diff:o?{attachment:{...o,path:Sr(o)}}:void 0}})]})),n.size?d.jsx("div",{className:"attachments-section",children:"Screenshots"}):void 0,[...n.values()].map((i,s)=>{const o=Sr(i);return d.jsxs("div",{className:"attachment-item",children:[d.jsx("div",{children:d.jsx("img",{draggable:"false",src:o})}),d.jsx("div",{children:d.jsx("a",{target:"_blank",href:o,children:i.name})})]},`screenshot-${s}`)}),r.size?d.jsx("div",{className:"attachments-section",children:"Attachments"}):void 0,[...r.values()].map((i,s)=>d.jsx("div",{className:"attachment-item",children:d.jsx("a",{href:Sr(i)+"&download",children:i.name})},`attachment-${s}`))]})};function Sr(e){return e.sha1?"sha1/"+e.sha1+"?trace="+encodeURIComponent(e.traceUrl):"file?path="+encodeURIComponent(e.path)}const Ly=({sdkLanguage:e,setIsInspecting:t,highlightedLocator:n,setHighlightedLocator:r})=>d.jsxs("div",{className:"vbox",style:{backgroundColor:"var(--vscode-sideBar-background)"},children:[d.jsx("div",{style:{margin:"10px 0px 10px 10px",color:"var(--vscode-editorCodeLens-foreground)",flex:"none"},children:"Locator"}),d.jsx("div",{style:{margin:"0 10px 10px",flex:"auto"},children:d.jsx(Xs,{text:n,language:e,focusOnChange:!0,isFocused:!0,wrapLines:!0,onChange:i=>{r(i),t(!1)}})}),d.jsx("div",{style:{position:"absolute",right:5,top:5},children:d.jsx(Bn,{icon:"files",title:"Copy locator",onClick:()=>{rm(n)}})})]});function Ay(e){return e==="scheduled"?"codicon-clock":e==="running"?"codicon-loading":e==="failed"?"codicon-error":e==="passed"?"codicon-check":e==="skipped"?"codicon-circle-slash":"codicon-circle-outline"}function My(e){return e==="scheduled"?"Pending":e==="running"?"Running":e==="failed"?"Failed":e==="passed"?"Passed":e==="skipped"?"Skipped":"Did not run"}const Vy=({model:e,hideStackFrames:t,showSourcesFirst:n,rootDir:r,fallbackLocation:i,initialSelection:s,onSelectionChanged:o,isLive:l,status:a})=>{const[u,c]=L.useState(void 0),[f,p]=L.useState(void 0),[v,y]=L.useState(),[w,x]=L.useState(),[h,m]=L.useState("actions"),[g,_]=Ji("propertiesTab",n?"source":"call"),[N,k]=L.useState(!1),[C,E]=L.useState(""),S=e?v||u:void 0,[A,P]=L.useState(),[T,I]=Ji("propertiesSidebarLocation","bottom"),z=L.useCallback(H=>{c(H),p(H==null?void 0:H.stack)},[c,p]),ae=L.useMemo(()=>(e==null?void 0:e.sources)||new Map,[e]);L.useEffect(()=>{P(void 0)},[e]),L.useEffect(()=>{if(u&&(e!=null&&e.actions.includes(u)))return;const H=e==null?void 0:e.failedAction();if(s&&(e!=null&&e.actions.includes(s)))z(s);else if(H)z(H);else if(e!=null&&e.actions.length){let Wa=e.actions.length-1;for(let lr=0;lr<e.actions.length;++lr)if(e.actions[lr].apiName==="After Hooks"&&lr){Wa=lr-1;break}z(e.actions[Wa])}},[e,u,z,s]);const Ne=L.useCallback(H=>{z(H),o==null||o(H)},[z,o]),ue=L.useCallback(H=>{_(H),H!=="inspector"&&k(!1)},[_]),M=L.useCallback(H=>{E(H),ue("inspector")},[ue]),R=u1(e,A),$=T1(e,A),W=o1(e),ne=L.useMemo(()=>(e==null?void 0:e.actions.map(H=>H.attachments||[]).flat())||[],[e]),Je=(e==null?void 0:e.sdkLanguage)||"javascript",pt={id:"inspector",title:"Locator",render:()=>d.jsx(Ly,{sdkLanguage:Je,setIsInspecting:k,highlightedLocator:C,setHighlightedLocator:E})},or={id:"call",title:"Call",render:()=>d.jsx(e1,{action:S,sdkLanguage:Je})},mt={id:"log",title:"Log",render:()=>d.jsx(n1,{action:S,isLive:l})},vn={id:"errors",title:"Errors",errorCount:W.errors.size,render:()=>d.jsx(l1,{errorsModel:W,sdkLanguage:Je,revealInSource:H=>{H.action?z(H.action):p(H.stack),ue("source")}})},Ys={id:"source",title:"Source",render:()=>d.jsx(vy,{stack:f,sources:ae,hideStackFrames:t,rootDir:r,fallbackLocation:i})},bp={id:"console",title:"Console",count:R.entries.length,render:()=>d.jsx(c1,{consoleModel:R,boundaries:Js,selectedTime:A})},Lp={id:"network",title:"Network",count:$.resources.length,render:()=>d.jsx(C1,{boundaries:Js,networkModel:$,onEntryHovered:x})},Ap={id:"attachments",title:"Attachments",count:ne.length,render:()=>d.jsx(by,{model:e})},ui=[pt,or,mt,vn,bp,Lp,Ys,Ap];if(n){const H=ui.indexOf(Ys);ui.splice(H,1),ui.splice(1,0,Ys)}const{boundaries:Js}=L.useMemo(()=>{const H={minimum:(e==null?void 0:e.startTime)||0,maximum:(e==null?void 0:e.endTime)||3e4};return H.minimum>H.maximum&&(H.minimum=0,H.maximum=3e4),H.maximum+=(H.maximum-H.minimum)/20,{boundaries:H}},[e]);let ci=0;return!l&&e&&e.endTime>=0?ci=e.endTime-e.startTime:e&&e.wallTime&&(ci=Date.now()-e.wallTime),d.jsxs("div",{className:"vbox workbench",children:[d.jsx(_y,{model:e,boundaries:Js,highlightedAction:v,highlightedEntry:w,onSelected:Ne,sdkLanguage:Je,selectedTime:A,setSelectedTime:P}),d.jsxs(Es,{sidebarSize:250,orientation:T==="bottom"?"vertical":"horizontal",settingName:"propertiesSidebar",children:[d.jsxs(Es,{sidebarSize:250,orientation:"horizontal",sidebarIsFirst:!0,settingName:"actionListSidebar",children:[d.jsx(fy,{action:S,sdkLanguage:Je,testIdAttributeName:(e==null?void 0:e.testIdAttributeName)||"data-testid",isInspecting:N,setIsInspecting:k,highlightedLocator:C,setHighlightedLocator:M}),d.jsx(Ml,{tabs:[{id:"actions",title:"Actions",component:d.jsxs("div",{className:"vbox",children:[a&&d.jsxs("div",{className:"workbench-run-status",children:[d.jsx("span",{className:`codicon ${Ay(a)}`}),d.jsx("div",{children:My(a)}),d.jsx("div",{className:"spacer"}),d.jsx("div",{className:"workbench-run-duration",children:ci?Qe(ci):""})]}),d.jsx(J0,{sdkLanguage:Je,actions:(e==null?void 0:e.actions)||[],selectedAction:e?u:void 0,selectedTime:A,setSelectedTime:P,onSelected:Ne,onHighlighted:y,revealConsole:()=>ue("console"),isLive:l})]})},{id:"metadata",title:"Metadata",component:d.jsx(Ty,{model:e})}],selectedTab:h,setSelectedTab:m})]}),d.jsx(Ml,{tabs:ui,selectedTab:g,setSelectedTab:ue,leftToolbar:[d.jsx(Bn,{title:"Pick locator",icon:"target",toggled:N,onClick:()=>{N||ue("inspector"),k(!N)}})],rightToolbar:[T==="bottom"?d.jsx(Bn,{title:"Dock to right",icon:"layout-sidebar-right-off",onClick:()=>{I("right")}}):d.jsx(Bn,{title:"Dock to bottom",icon:"layout-panel-off",onClick:()=>{I("bottom")}})],mode:T==="bottom"?"default":"select"})]})]})};let jy=0,Np;const Dl=new Map;async function By(e){const t=new URLSearchParams(window.location.search).get("ws"),n=new URL(`../${t}`,window.location.toString());n.protocol=window.location.protocol==="https:"?"wss:":"ws:";const r=new WebSocket(n);return await new Promise(i=>r.addEventListener("open",i)),r.addEventListener("close",e.onClose),r.addEventListener("message",i=>{const s=JSON.parse(i.data),{id:o,result:l,error:a,method:u,params:c}=s;if(o){const f=Dl.get(o);if(!f)return;Dl.delete(o),a?f.reject(new Error(a)):f.resolve(l)}else e.onEvent(u,c)}),Np=r,setInterval(()=>Ic("ping").catch(()=>{}),3e4),Ic}const Ic=async(e,t)=>{const n=++jy,r={id:n,method:e,params:t};return Np.send(JSON.stringify(r)),new Promise((i,s)=>{Dl.set(n,{resolve:i,reject:s})})};export{Fy as M,zt as R,Es as S,Bn as T,Vy as W,g1 as _,Ry as a,Hy as b,By as c,Dy as d,Oy as e,zy as f,Ji as g,_h as h,Ay as i,d as j,Uy as k,K0 as l,Qe as m,Py as n,Ip as o,L as r,Or as s,$y as t,pn as u};
function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["./codeMirrorModule-0bpaqixv.js","../codeMirrorModule.Hs9-1ZG4.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
