from playwright.sync_api import sync_playwright, TimeoutError

def extract_top_stories():
    url = "https://www.msn.com/en-in/channel/topic/Top%20stories/tp-Y_0b495ad3-9beb-45f8-9214-c8e95aa2468f?cvid=16f3161c429c45ada7dfe946323bde43&ocid=msedgntp&pc=U531&ei=24"

    try:
        with sync_playwright() as playwright:
            # Launch browser
            browser = playwright.chromium.launch(headless=False)  # Set to True for production
            context = browser.new_context()
            page = context.new_page()

            # Navigate to the target URL
            page.goto(url, timeout=60000)
            page.wait_for_load_state("networkidle", timeout=30000)

            # Verify page title to ensure correct navigation
            assert "Top stories - MSN" in page.title(), "Page title does not match expected value."

            # Wait for the "Top Stories" section to load
            page.wait_for_selector(".top-stories", timeout=15000)

            # Extract top stories using multiple selector strategies
            top_stories = []
            try:
                # Primary selector strategy: CSS selectors
                story_elements = page.query_selector_all(".top-stories .story h2")
                for story in story_elements:
                    top_stories.append(story.inner_text().strip())

                # Fallback strategy: XPath selectors
                if len(top_stories) < 10:
                    story_elements_xpath = page.query_selector_all("//div[contains(@class, 'top-stories')]//h2")
                    for story in story_elements_xpath:
                        top_stories.append(story.inner_text().strip())

                # Final fallback: Text-based selectors (if necessary)
                if len(top_stories) < 10:
                    story_elements_text = page.locator("text=Top Stories").locator("h2")
                    for story in story_elements_text.element_handles():
                        top_stories.append(story.inner_text().strip())

            except Exception as e:
                print(f"Error extracting top stories: {e}")

            # Ensure we have at least 10 stories
            if len(top_stories) < 10:
                raise Exception(f"Only {len(top_stories)} stories were extracted. Expected at least 10.")

            # Print the top 10 stories
            print("Top 10 Stories:")
            for index, story in enumerate(top_stories[:10], start=1):
                print(f"{index}. {story}")

            # Close the browser
            browser.close()

    except TimeoutError as e:
        print(f"TimeoutError: {e}")
    except AssertionError as e:
        print(f"AssertionError: {e}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    extract_top_stories()