from playwright.sync_api import sync_playwright, TimeoutError

def fetch_top_stories():
    url = "https://www.msn.com/en-in/channel/topic/Top%20stories/tp-Y_0b495ad3-9beb-45f8-9214-c8e95aa2468f?cvid=16f3161c429c45ada7dfe946323bde43&ocid=msedgntp&pc=U531&ei=24"

    try:
        with sync_playwright() as playwright:
            browser = playwright.chromium.launch(headless=True)
            context = browser.new_context()
            page = context.new_page()

            # Navigate to the URL
            page.goto(url, timeout=60000)
            assert page.title() == "Top stories - MSN", "Page title does not match expected value."

            # Wait for the top stories section to load
            page.wait_for_selector(".top-stories-container", timeout=30000)

            # Select the top 10 stories
            story_cards = page.query_selector_all(".top-stories-container .story-card")
            assert len(story_cards) > 0, "No story cards found on the page."

            top_stories = []
            for i, card in enumerate(story_cards[:10]):
                title = card.query_selector(".title").inner_text() if card.query_selector(".title") else "No title"
                publisher = card.query_selector(".publisher").inner_text() if card.query_selector(".publisher") else "No publisher"
                timestamp = card.query_selector(".timestamp").inner_text() if card.query_selector(".timestamp") else "No timestamp"
                read_time = card.query_selector(".read-time").inner_text() if card.query_selector(".read-time") else "No read time"

                top_stories.append({
                    "Title": title,
                    "Publisher": publisher,
                    "Timestamp": timestamp,
                    "Read Time": read_time
                })

            # Print the top 10 stories
            for idx, story in enumerate(top_stories, start=1):
                print(f"{idx}. Title: {story['Title']}")
                print(f"   Publisher: {story['Publisher']}")
                print(f"   Timestamp: {story['Timestamp']}")
                print(f"   Read Time: {story['Read Time']}")
                print()

            # Clean up
            browser.close()

    except TimeoutError as e:
        print(f"TimeoutError: {e}")
    except AssertionError as e:
        print(f"AssertionError: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    fetch_top_stories()