from playwright.sync_api import sync_playwright, TimeoutError
import time

def fetch_top_stories():
    url = "https://www.msn.com/en-in/channel/topic/Top%20stories/tp-Y_0b495ad3-9beb-45f8-9214-c8e95aa2468f?cvid=16f3161c429c45ada7dfe946323bde43&ocid=msedgntp&pc=U531&ei=24"

    try:
        with sync_playwright() as playwright:
            browser = playwright.chromium.launch(headless=False)  # Set to False to see what's happening
            context = browser.new_context()
            page = context.new_page()

            # Navigate to the URL
            print("Navigating to MSN Top Stories page...")
            page.goto(url, timeout=60000)

            # Print actual page title for debugging
            actual_title = page.title()
            print(f"Actual page title: '{actual_title}'")

            # Wait for page to load
            page.wait_for_load_state("networkidle", timeout=30000)
            time.sleep(3)  # Additional wait for dynamic content

            # Try multiple possible selectors for news articles
            possible_selectors = [
                "article",
                "[data-module='NewsCard']",
                ".news-card",
                ".story-card",
                ".article-card",
                ".cs-card",
                "[role='article']",
                "a[href*='/news/']",
                "a[href*='/story/']"
            ]

            stories_found = []

            for selector in possible_selectors:
                try:
                    elements = page.query_selector_all(selector)
                    if elements:
                        print(f"Found {len(elements)} elements with selector: {selector}")

                        for i, element in enumerate(elements[:10]):
                            try:
                                # Try to extract text content
                                text_content = element.inner_text().strip()
                                if text_content and len(text_content) > 10:  # Filter out empty or very short content
                                    # Try to find a link
                                    link_element = element.query_selector("a") or element
                                    href = link_element.get_attribute("href") if hasattr(link_element, 'get_attribute') else ""

                                    stories_found.append({
                                        "index": i + 1,
                                        "text": text_content[:200] + "..." if len(text_content) > 200 else text_content,
                                        "link": href,
                                        "selector_used": selector
                                    })
                            except Exception as e:
                                continue

                        if stories_found:
                            break  # Use the first selector that finds content

                except Exception as e:
                    continue

            if not stories_found:
                # Fallback: get all links that might be news stories
                print("Trying fallback approach...")
                all_links = page.query_selector_all("a")
                for i, link in enumerate(all_links[:20]):
                    try:
                        text = link.inner_text().strip()
                        href = link.get_attribute("href")
                        if text and len(text) > 20 and href and ("/news/" in href or "/story/" in href):
                            stories_found.append({
                                "index": len(stories_found) + 1,
                                "text": text[:200] + "..." if len(text) > 200 else text,
                                "link": href,
                                "selector_used": "fallback link search"
                            })
                            if len(stories_found) >= 10:
                                break
                    except Exception:
                        continue

            # Print the results
            if stories_found:
                print(f"\n=== TOP {min(len(stories_found), 10)} STORIES ===\n")
                for story in stories_found[:10]:
                    print(f"{story['index']}. {story['text']}")
                    if story['link']:
                        print(f"   Link: {story['link']}")
                    print(f"   (Found using: {story['selector_used']})")
                    print()
            else:
                print("No stories found. The page structure might have changed.")
                print("Page content preview:")
                print(page.content()[:1000] + "...")

            # Clean up
            browser.close()

    except TimeoutError as e:
        print(f"TimeoutError: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    fetch_top_stories()