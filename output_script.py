import sys

try:
    from playwright.sync_api import sync_playwright
except ImportError:
    print("Error: Playwright module not found. Please install it using 'pip install playwright'.")
    sys.exit(1)

def navigate_to_google(page):
    try:
        print("Step 1: Navigating to Google...")
        page.goto("https://google.com", timeout=60000)
        page.wait_for_selector('input[aria-label="Search"]', timeout=10000)
        print("Step 1: Navigation successful.")
    except Exception as e:
        raise Exception(f"Step 1: Failed to load Google homepage. Error: {e}")

def perform_search(page, query):
    try:
        print(f"Step 2: Searching for '{query}'...")
        search_input_selector = 'input[aria-label="Search"]'
        search_button_selector = 'input[value="Google Search"]'
        
        # Type the search query
        page.fill(search_input_selector, query)
        
        # Click the search button
        page.click(search_button_selector)
        
        # Wait for search results to load
        page.wait_for_selector('.tF2Cxc a.LC20lb', timeout=15000)
        print("Step 2: Search executed successfully.")
    except Exception as e:
        raise Exception(f"Step 2: Search results failed to load. Error: {e}")

def extract_top_results(page, max_results=10):
    try:
        print(f"Step 3: Extracting top {max_results} search results...")
        result_title_selector = '.tF2Cxc a.LC20lb'
        result_url_selector = '.tF2Cxc a.LC20lb'
        
        # Extract titles and URLs
        titles = page.locator(result_title_selector).all_text_contents()
        urls = page.locator(result_url_selector).evaluate_all("elements => elements.map(el => el.href)")
        
        # Combine titles and URLs into a structured format
        results = [{"title": title, "url": url} for title, url in zip(titles, urls)]
        
        # Limit to the top `max_results`
        results = results[:max_results]
        
        print(f"Step 3: Successfully extracted {len(results)} results.")
        return results
    except Exception as e:
        raise Exception(f"Step 3: Failed to extract search results. Error: {e}")

def print_results(results):
    print("Step 4: Printing results...")
    if not results:
        print("No results found.")
        return
    for idx, result in enumerate(results, start=1):
        print(f"{idx}. {result['title']} - {result['url']}")
    print("Step 4: Results printed successfully.")

def execute_multi_step_action():
    query = "ai news"
    results = []  # Store results from Step 3
    
    try:
        with sync_playwright() as playwright:
            browser = playwright.chromium.launch(headless=True)
            context = browser.new_context()
            page = context.new_page()
            
            # Step 1: Navigate to Google
            navigate_to_google(page)
            
            # Step 2: Perform search
            perform_search(page, query)
            
            # Step 3: Extract top 10 results
            results = extract_top_results(page, max_results=10)
            
            # Step 4: Print results
            print_results(results)
            
            browser.close()
    except Exception as e:
        print(f"Error in multi-step execution: {e}")

if __name__ == "__main__":
    execute_multi_step_action()