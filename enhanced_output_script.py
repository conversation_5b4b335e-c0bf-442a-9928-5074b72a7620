from playwright.sync_api import sync_playwright, TimeoutError

def extract_top_stories():
    url = "https://www.msn.com/en-in/channel/topic/Top%20stories/tp-Y_0b495ad3-9beb-45f8-9214-c8e95aa2468f?cvid=16f3161c429c45ada7dfe946323bde43&ocid=msedgntp&pc=U531&ei=24"

    try:
        with sync_playwright() as playwright:
            browser = playwright.chromium.launch(headless=False)  # Set to True for production
            context = browser.new_context()
            page = context.new_page()

            # Navigate to the target URL
            page.goto(url, timeout=60000)
            page.wait_for_load_state("networkidle", timeout=30000)

            # Verify page title to ensure correct navigation
            page_title = page.title()
            assert "Top stories - MSN" in page_title, f"Unexpected page title: {page_title}"

            # Wait for the Top Stories section to load
            try:
                page.wait_for_selector(".news-card", timeout=15000)  # Primary selector
            except TimeoutError:
                print("Error: Top Stories section did not load in time.")
                browser.close()
                return

            # Extract top 10 stories using multiple selector strategies
            stories = []
            try:
                # Strategy 1: Reliable CSS selectors
                news_cards = page.query_selector_all(".news-card")
                if not news_cards:
                    raise Exception("No news cards found using primary selector.")

                # Extract details from the first 10 news cards
                for card in news_cards[:10]:
                    headline = card.query_selector(".headline") or card.query_selector("h3")
                    publisher = card.query_selector(".publisher") or card.query_selector("span.publisher")
                    timestamp = card.query_selector(".timestamp") or card.query_selector("span.timestamp")

                    # Fallback to text-based extraction if elements are missing
                    headline_text = headline.inner_text().strip() if headline else "Headline not found"
                    publisher_text = publisher.inner_text().strip() if publisher else "Publisher not found"
                    timestamp_text = timestamp.inner_text().strip() if timestamp else "Timestamp not found"

                    stories.append({
                        "headline": headline_text,
                        "publisher": publisher_text,
                        "timestamp": timestamp_text
                    })

                # Verify that we have extracted exactly 10 stories
                assert len(stories) == 10, f"Expected 10 stories, but extracted {len(stories)}."

            except Exception as e:
                print(f"Error during story extraction: {e}")
                browser.close()
                return

            # Print the extracted stories
            print("Top 10 Stories:")
            for idx, story in enumerate(stories, start=1):
                print(f"{idx}. Headline: {story['headline']}, Publisher: {story['publisher']}, Timestamp: {story['timestamp']}")

            browser.close()

    except TimeoutError as e:
        print(f"TimeoutError: {e}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    extract_top_stories()