import sys
from playwright.sync_api import sync_playwright

def execute_multi_step_action():
    url = "https://www.newegg.com"
    results = []  # Store results from the search

    def wait_for_captcha(page):
        try:
            print("Waiting for CAPTCHA to appear...")
            page.wait_for_selector('#captcha-checkbox', timeout=10000)
            print("CAPTCHA detected. Attempting to solve...")
            page.click('#captcha-checkbox')
            page.wait_for_load_state('networkidle', timeout=15000)
            print("CAPTCHA solved successfully.")
        except Exception as e:
            print(f"CAPTCHA solving failed or not detected: {e}. Proceeding...")

    def navigate_to_page(page):
        print("Step 1: Navigating to Newegg...")
        page.goto(url)
        wait_for_captcha(page)
        if "Just a moment..." in page.title():
            print("CAPTCHA page detected. Waiting for resolution...")
            wait_for_captcha(page)
        print("Navigation successful. Page title:", page.title())

    def search_for_laptops(page):
        print("Step 2: Searching for laptops...")
        try:
            search_box_selector = 'input[type="search"]'
            page.wait_for_selector(search_box_selector, timeout=10000)
            page.fill(search_box_selector, "laptops")
            page.press(search_box_selector, "Enter")
            page.wait_for_load_state('networkidle', timeout=15000)
            print("Search completed successfully.")
        except Exception as e:
            print(f"Search box not found or search failed: {e}")
            raise Exception("Search step failed.")

    def extract_top_10_results(page):
        print("Step 3: Extracting top 10 laptop names and prices...")
        try:
            product_selector = '.item-cell'
            page.wait_for_selector(product_selector, timeout=15000)
            product_elements = page.locator(product_selector)
            count = product_elements.count()
            print(f"Found {count} products. Extracting top 10...")

            for i in range(min(10, count)):
                name_selector = '.item-title'
                price_selector = '.price-current'
                try:
                    name = product_elements.nth(i).locator(name_selector).inner_text(timeout=5000)
                except Exception:
                    name = "Name not found"
                try:
                    price = product_elements.nth(i).locator(price_selector).inner_text(timeout=5000)
                except Exception:
                    price = "Price not found"
                results.append({"name": name, "price": price})
            print("Data extraction completed successfully.")
        except Exception as e:
            print(f"Failed to extract product data: {e}")
            raise Exception("Data extraction step failed.")

    def print_results():
        print("Step 4: Printing results...")
        if results:
            print("Top 10 Laptops:")
            for idx, result in enumerate(results, start=1):
                print(f"{idx}. {result['name']} - {result['price']}")
        else:
            print("No results to display.")

    try:
        with sync_playwright() as playwright:
            browser = playwright.chromium.launch(headless=False)
            context = browser.new_context()
            page = context.new_page()

            # Step 1: Navigate to the website
            navigate_to_page(page)

            # Step 2: Search for laptops
            search_for_laptops(page)

            # Step 3: Extract top 10 results
            extract_top_10_results(page)

            # Step 4: Print results
            print_results()

            browser.close()

    except ImportError:
        print("Playwright module not found. Please install it using 'pip install playwright'.")
        sys.exit(1)
    except Exception as e:
        print(f"Error in multi-step execution: {e}")

if __name__ == "__main__":
    execute_multi_step_action()