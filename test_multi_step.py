from src.action_decomposer import ActionDecomposer

def test_action_decomposition():
    decomposer = ActionDecomposer()
    
    # Test cases
    test_actions = [
        "search for 'ai news' and print the top 10 links with description",
        "navigate to login page and enter credentials then submit",
        "click on menu and select settings",
        "get all product names and prices from the page",
        "fill the form with user data and submit it",
        "search for 'python tutorial' then click on the first result and extract the content"
    ]
    
    for action in test_actions:
        print(f"\n{'='*60}")
        print(f"ACTION: {action}")
        print('='*60)
        
        steps = decomposer.decompose_action(action, "https://example.com")
        
        print(f"Decomposed into {len(steps)} steps:")
        for i, step in enumerate(steps, 1):
            print(f"\nStep {i}: {step.description}")
            print(f"  Type: {step.step_type}")
            print(f"  Target: {step.target_element}")
            print(f"  Input: {step.input_data}")
            print(f"  Expected Output: {step.expected_output}")
            print(f"  Dependencies: {step.depends_on}")
            print(f"  Selectors: {step.selectors}")
            print(f"  Validation: {list(step.validation_criteria.keys())}")

if __name__ == "__main__":
    test_action_decomposition()
