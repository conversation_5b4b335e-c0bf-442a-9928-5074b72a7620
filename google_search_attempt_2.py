from playwright.sync_api import sync_playwright

def print_top_10_results():
    url = "https://www.google.com/search?q=test"

    try:
        with sync_playwright() as playwright:
            browser = playwright.chromium.launch(headless=False)  # Set to True for production
            context = browser.new_context()
            page = context.new_page()

            # Navigate to the target URL
            print("Navigating to the target URL...")
            page.goto(url, timeout=60000)
            page.wait_for_load_state("networkidle", timeout=30000)

            # Check if CAPTCHA is present
            print("Checking for CAPTCHA...")
            captcha_frame = page.query_selector('iframe[src*="recaptcha/api2"]')
            if captcha_frame:
                print("CAPTCHA detected. Attempting to solve...")
                try:
                    # Interact with the CAPTCHA checkbox
                    page.frame_locator('iframe[src*="recaptcha/api2"]').locator('.recaptcha-checkbox').click()
                    print("CAPTCHA checkbox clicked. Waiting for verification...")
                    page.wait_for_timeout(5000)  # Wait for CAPTCHA verification
                except Exception as e:
                    print(f"Error interacting with CAPTCHA: {e}")
                    browser.close()
                    return

            # Wait for the search results container to load
            print("Waiting for search results...")
            try:
                page.wait_for_selector('div#search', timeout=10000)  # Primary selector
            except Exception as e:
                print(f"Primary selector for search results not found: {e}. Trying fallback selectors...")
                try:
                    page.wait_for_selector('//div[contains(@id, "search")]', timeout=10000)  # Fallback selector
                except Exception as e:
                    print(f"Fallback selector failed: {e}. Unable to locate search results.")
                    browser.close()
                    return

            # Extract the top 10 search results
            print("Extracting top 10 search results...")
            try:
                results = page.query_selector_all('div#search div.g')
                if not results:
                    results = page.query_selector_all('//div[contains(@id, "search")]//div[contains(@class, "g")]')

                if results:
                    print("Top 10 search results:")
                    for i, result in enumerate(results[:10], start=1):
                        title_element = result.query_selector('h3')
                        if title_element:
                            print(f"{i}. {title_element.inner_text()}")
                        else:
                            print(f"{i}. [No title found for this result]")
                else:
                    print("No search results found.")
            except Exception as e:
                print(f"Error extracting search results: {e}")

            # Close the browser
            print("Closing the browser...")
            browser.close()

    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    print_top_10_results()