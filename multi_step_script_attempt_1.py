from playwright.sync_api import sync_playwright, TimeoutError
import time

def navigate_to_google(page, url):
    try:
        print("Step 1: Navigating to Google...")
        page.goto(url, timeout=10000)  # Increase timeout for slow network conditions
        page.wait_for_selector('input[name="q"]', timeout=10000)  # Wait for the search input field
        print("Step 1: Navigation successful.")
    except TimeoutError:
        raise Exception("Failed to load Google homepage. Timeout occurred.")

def perform_search(page, query):
    try:
        print("Step 2: Performing search for query:", query)
        search_input = page.locator('input[name="q"]')
        search_input.fill(query)
        # Use multiple strategies to click the search button
        try:
            page.locator('input[value="Google Search"]').click()
        except:
            print("Primary search button selector failed. Trying fallback...")
            page.keyboard.press("Enter")  # Fallback to pressing Enter key
        page.wait_for_selector('.tF2Cxc', timeout=10000)  # Wait for search results to load
        print("Step 2: Search executed successfully.")
    except TimeoutError:
        raise Exception("Search results failed to load. Timeout occurred.")

def extract_results(page, max_results=10):
    try:
        print("Step 3: Extracting top search results...")
        results = []
        result_containers = page.locator('.tF2Cxc')
        count = min(result_containers.count(), max_results)
        
        for i in range(count):
            container = result_containers.nth(i)
            try:
                link_element = container.locator('a.LC20lb')
                description_element = container.locator('.IsZvec')
                
                link = link_element.get_attribute('href') or "No link available"
                title = link_element.inner_text() or "No title available"
                description = description_element.inner_text() or "No description available"
                
                results.append({
                    "title": title,
                    "link": link,
                    "description": description
                })
            except Exception as e:
                print(f"Error extracting result {i + 1}: {e}")
        
        if not results:
            raise Exception("No results found. Ensure the query is valid and results are loaded.")
        
        print("Step 3: Data extraction successful.")
        return results
    except Exception as e:
        raise Exception(f"Failed to extract search results: {e}")

def print_results(results):
    print("Step 4: Printing results...")
    for idx, result in enumerate(results, start=1):
        print(f"Result {idx}:")
        print(f"  Title: {result['title']}")
        print(f"  Link: {result['link']}")
        print(f"  Description: {result['description']}")
        print("-" * 50)
    print("Step 4: Results printed successfully.")

def execute_multi_step_action():
    url = "https://www.google.com"
    query = "ai news"
    max_results = 10
    
    try:
        with sync_playwright() as playwright:
            browser = playwright.chromium.launch(headless=True)  # Run in headless mode
            context = browser.new_context()
            page = context.new_page()
            
            # Step 1: Navigate to Google
            navigate_to_google(page, url)
            
            # Step 2: Perform search
            perform_search(page, query)
            
            # Step 3: Extract top 10 results
            results = extract_results(page, max_results)
            
            # Step 4: Print results
            print_results(results)
            
            browser.close()
    except Exception as e:
        print(f"Error in multi-step execution: {e}")

if __name__ == "__main__":
    execute_multi_step_action()