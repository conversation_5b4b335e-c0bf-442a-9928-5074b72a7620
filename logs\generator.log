2025-06-03 09:11:49 | INFO     | Starting script generation for URL: https://www.msn.com/en-in/channel/topic/Top%20stories/tp-Y_0b495ad3-9beb-45f8-9214-c8e95aa2468f?cvid=16f3161c429c45ada7dfe946323bde43&ocid=msedgntp&pc=U531&ei=24
2025-06-03 09:11:49 | INFO     | Action to automate: get top 10 stories and print it
2025-06-03 09:11:49 | INFO     | Capturing website: https://www.msn.com/en-in/channel/topic/Top%20stories/tp-Y_0b495ad3-9beb-45f8-9214-c8e95aa2468f?cvid=16f3161c429c45ada7dfe946323bde43&ocid=msedgntp&pc=U531&ei=24 using chromium
2025-06-03 09:11:56 | DEBUG    | Page loaded: https://www.msn.com/en-in/channel/topic/Top%20stories/tp-Y_0b495ad3-9beb-45f8-9214-c8e95aa2468f?cvid=16f3161c429c45ada7dfe946323bde43&ocid=msedgntp&pc=U531&ei=24
2025-06-03 09:11:57 | DEBUG    | Screenshot captured
2025-06-03 09:11:57 | ERROR    | Error capturing website: cannot write mode RGBA as JPEG
2025-06-03 09:11:57 | ERROR    | Failed to generate script
2025-06-03 09:13:44 | INFO     | Starting script generation for URL: https://www.msn.com/en-in/channel/topic/Top%20stories/tp-Y_0b495ad3-9beb-45f8-9214-c8e95aa2468f?cvid=16f3161c429c45ada7dfe946323bde43&ocid=msedgntp&pc=U531&ei=24
2025-06-03 09:13:44 | INFO     | Action to automate: get top 10 stories and print it
2025-06-03 09:13:44 | INFO     | Capturing website: https://www.msn.com/en-in/channel/topic/Top%20stories/tp-Y_0b495ad3-9beb-45f8-9214-c8e95aa2468f?cvid=16f3161c429c45ada7dfe946323bde43&ocid=msedgntp&pc=U531&ei=24 using chromium
2025-06-03 09:13:48 | DEBUG    | Page loaded: https://www.msn.com/en-in/channel/topic/Top%20stories/tp-Y_0b495ad3-9beb-45f8-9214-c8e95aa2468f?cvid=16f3161c429c45ada7dfe946323bde43&ocid=msedgntp&pc=U531&ei=24
2025-06-03 09:13:48 | DEBUG    | Screenshot captured
2025-06-03 09:13:48 | DEBUG    | DOM content extracted
2025-06-03 09:13:48 | INFO     | Using Azure OpenAI with endpoint: https://pep-aisp-hackathon.openai.azure.com/ and deployment: gpt-4o
2025-06-03 09:15:11 | INFO     | Starting script generation for URL: https://www.msn.com/en-in/channel/topic/Top%20stories/tp-Y_0b495ad3-9beb-45f8-9214-c8e95aa2468f?cvid=16f3161c429c45ada7dfe946323bde43&ocid=msedgntp&pc=U531&ei=24
2025-06-03 09:15:11 | INFO     | Action to automate: get top 10 stories and print it
2025-06-03 09:15:11 | INFO     | Capturing website: https://www.msn.com/en-in/channel/topic/Top%20stories/tp-Y_0b495ad3-9beb-45f8-9214-c8e95aa2468f?cvid=16f3161c429c45ada7dfe946323bde43&ocid=msedgntp&pc=U531&ei=24 using chromium
2025-06-03 09:15:17 | DEBUG    | Page loaded: https://www.msn.com/en-in/channel/topic/Top%20stories/tp-Y_0b495ad3-9beb-45f8-9214-c8e95aa2468f?cvid=16f3161c429c45ada7dfe946323bde43&ocid=msedgntp&pc=U531&ei=24
2025-06-03 09:15:18 | DEBUG    | Screenshot captured
2025-06-03 09:15:18 | DEBUG    | DOM content extracted
2025-06-03 09:15:18 | INFO     | Using Azure OpenAI with endpoint: https://pep-aisp-hackathon.openai.azure.com/ and deployment: gpt-4o
2025-06-03 09:20:32 | INFO     | Starting script generation for URL: https://www.msn.com/en-in/channel/topic/Top%20stories/tp-Y_0b495ad3-9beb-45f8-9214-c8e95aa2468f?cvid=16f3161c429c45ada7dfe946323bde43&ocid=msedgntp&pc=U531&ei=24
2025-06-03 09:20:32 | INFO     | Action to automate: get top 10 stories and print it
2025-06-03 09:20:32 | INFO     | Capturing website: https://www.msn.com/en-in/channel/topic/Top%20stories/tp-Y_0b495ad3-9beb-45f8-9214-c8e95aa2468f?cvid=16f3161c429c45ada7dfe946323bde43&ocid=msedgntp&pc=U531&ei=24 using chromium
2025-06-03 09:20:36 | DEBUG    | Page loaded: https://www.msn.com/en-in/channel/topic/Top%20stories/tp-Y_0b495ad3-9beb-45f8-9214-c8e95aa2468f?cvid=16f3161c429c45ada7dfe946323bde43&ocid=msedgntp&pc=U531&ei=24
2025-06-03 09:20:36 | DEBUG    | Screenshot captured
2025-06-03 09:20:37 | DEBUG    | DOM content extracted
2025-06-03 09:20:37 | INFO     | Using Azure OpenAI with endpoint: https://pep-aisp-hackathon.openai.azure.com/ and deployment: gpt-4o
2025-06-03 09:20:37 | INFO     | Sending screenshot to Vision API for analysis
2025-06-03 09:20:50 | DEBUG    | Successfully received vision analysis
2025-06-03 09:20:50 | INFO     | Using Azure OpenAI with endpoint: https://pep-aisp-hackathon.openai.azure.com/ and deployment: gpt-4o
2025-06-03 09:20:50 | INFO     | Generating Playwright script from combined context
2025-06-03 09:20:57 | DEBUG    | Successfully generated Playwright script
2025-06-03 09:20:57 | SUCCESS  | Script saved to: ./output_script.py
2025-06-03 09:20:57 | SUCCESS  | Script successfully generated and saved to: ./output_script.py
